// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: datacapture.Communicate.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_datacapture_2eCommunicate_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_datacapture_2eCommunicate_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021006 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_datacapture_2eCommunicate_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_datacapture_2eCommunicate_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_datacapture_2eCommunicate_2eproto;
namespace datacapture {
class ArgsList;
struct ArgsListDefaultTypeInternal;
extern ArgsListDefaultTypeInternal _ArgsList_default_instance_;
class CapStatus;
struct CapStatusDefaultTypeInternal;
extern CapStatusDefaultTypeInternal _CapStatus_default_instance_;
class ClientInfo;
struct ClientInfoDefaultTypeInternal;
extern ClientInfoDefaultTypeInternal _ClientInfo_default_instance_;
class CommonCMD;
struct CommonCMDDefaultTypeInternal;
extern CommonCMDDefaultTypeInternal _CommonCMD_default_instance_;
class CompressReqList;
struct CompressReqListDefaultTypeInternal;
extern CompressReqListDefaultTypeInternal _CompressReqList_default_instance_;
class CompressRes;
struct CompressResDefaultTypeInternal;
extern CompressResDefaultTypeInternal _CompressRes_default_instance_;
class ConnectArgs;
struct ConnectArgsDefaultTypeInternal;
extern ConnectArgsDefaultTypeInternal _ConnectArgs_default_instance_;
class DataQuery;
struct DataQueryDefaultTypeInternal;
extern DataQueryDefaultTypeInternal _DataQuery_default_instance_;
class DataQueryBatchesReq;
struct DataQueryBatchesReqDefaultTypeInternal;
extern DataQueryBatchesReqDefaultTypeInternal _DataQueryBatchesReq_default_instance_;
class DataQueryBatchesRes;
struct DataQueryBatchesResDefaultTypeInternal;
extern DataQueryBatchesResDefaultTypeInternal _DataQueryBatchesRes_default_instance_;
class DataQueryRes;
struct DataQueryResDefaultTypeInternal;
extern DataQueryResDefaultTypeInternal _DataQueryRes_default_instance_;
class Message;
struct MessageDefaultTypeInternal;
extern MessageDefaultTypeInternal _Message_default_instance_;
class Monitor;
struct MonitorDefaultTypeInternal;
extern MonitorDefaultTypeInternal _Monitor_default_instance_;
class NetConfig;
struct NetConfigDefaultTypeInternal;
extern NetConfigDefaultTypeInternal _NetConfig_default_instance_;
class RawData;
struct RawDataDefaultTypeInternal;
extern RawDataDefaultTypeInternal _RawData_default_instance_;
class RoadInfo;
struct RoadInfoDefaultTypeInternal;
extern RoadInfoDefaultTypeInternal _RoadInfo_default_instance_;
class RoadInfoList;
struct RoadInfoListDefaultTypeInternal;
extern RoadInfoListDefaultTypeInternal _RoadInfoList_default_instance_;
class SystemCmd;
struct SystemCmdDefaultTypeInternal;
extern SystemCmdDefaultTypeInternal _SystemCmd_default_instance_;
class SystemInfo;
struct SystemInfoDefaultTypeInternal;
extern SystemInfoDefaultTypeInternal _SystemInfo_default_instance_;
class SystemLog;
struct SystemLogDefaultTypeInternal;
extern SystemLogDefaultTypeInternal _SystemLog_default_instance_;
class SystemStatus;
struct SystemStatusDefaultTypeInternal;
extern SystemStatusDefaultTypeInternal _SystemStatus_default_instance_;
class storeInfo;
struct storeInfoDefaultTypeInternal;
extern storeInfoDefaultTypeInternal _storeInfo_default_instance_;
}  // namespace datacapture
PROTOBUF_NAMESPACE_OPEN
template<> ::datacapture::ArgsList* Arena::CreateMaybeMessage<::datacapture::ArgsList>(Arena*);
template<> ::datacapture::CapStatus* Arena::CreateMaybeMessage<::datacapture::CapStatus>(Arena*);
template<> ::datacapture::ClientInfo* Arena::CreateMaybeMessage<::datacapture::ClientInfo>(Arena*);
template<> ::datacapture::CommonCMD* Arena::CreateMaybeMessage<::datacapture::CommonCMD>(Arena*);
template<> ::datacapture::CompressReqList* Arena::CreateMaybeMessage<::datacapture::CompressReqList>(Arena*);
template<> ::datacapture::CompressRes* Arena::CreateMaybeMessage<::datacapture::CompressRes>(Arena*);
template<> ::datacapture::ConnectArgs* Arena::CreateMaybeMessage<::datacapture::ConnectArgs>(Arena*);
template<> ::datacapture::DataQuery* Arena::CreateMaybeMessage<::datacapture::DataQuery>(Arena*);
template<> ::datacapture::DataQueryBatchesReq* Arena::CreateMaybeMessage<::datacapture::DataQueryBatchesReq>(Arena*);
template<> ::datacapture::DataQueryBatchesRes* Arena::CreateMaybeMessage<::datacapture::DataQueryBatchesRes>(Arena*);
template<> ::datacapture::DataQueryRes* Arena::CreateMaybeMessage<::datacapture::DataQueryRes>(Arena*);
template<> ::datacapture::Message* Arena::CreateMaybeMessage<::datacapture::Message>(Arena*);
template<> ::datacapture::Monitor* Arena::CreateMaybeMessage<::datacapture::Monitor>(Arena*);
template<> ::datacapture::NetConfig* Arena::CreateMaybeMessage<::datacapture::NetConfig>(Arena*);
template<> ::datacapture::RawData* Arena::CreateMaybeMessage<::datacapture::RawData>(Arena*);
template<> ::datacapture::RoadInfo* Arena::CreateMaybeMessage<::datacapture::RoadInfo>(Arena*);
template<> ::datacapture::RoadInfoList* Arena::CreateMaybeMessage<::datacapture::RoadInfoList>(Arena*);
template<> ::datacapture::SystemCmd* Arena::CreateMaybeMessage<::datacapture::SystemCmd>(Arena*);
template<> ::datacapture::SystemInfo* Arena::CreateMaybeMessage<::datacapture::SystemInfo>(Arena*);
template<> ::datacapture::SystemLog* Arena::CreateMaybeMessage<::datacapture::SystemLog>(Arena*);
template<> ::datacapture::SystemStatus* Arena::CreateMaybeMessage<::datacapture::SystemStatus>(Arena*);
template<> ::datacapture::storeInfo* Arena::CreateMaybeMessage<::datacapture::storeInfo>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace datacapture {

enum Message_MessageType : int {
  Message_MessageType_PROTO_COMMON_CMD = 0,
  Message_MessageType_PROTO_UPDATE_CONFIG = 1,
  Message_MessageType_PROTO_UPDATE_STATUS = 2,
  Message_MessageType_PROTO_DATA_QUERY_REQ = 3,
  Message_MessageType_PROTO_DATA_QUERY_RES = 4,
  Message_MessageType_PROTO_DATA_COMPRESS_REQ = 5,
  Message_MessageType_PROTO_DATA_COMPRESS_RES = 6,
  Message_MessageType_PROTO_DATA_MONITOR = 7,
  Message_MessageType_PROTO_CONFIG_REQ = 8,
  Message_MessageType_PROTO_CONFIG_RES = 9,
  Message_MessageType_PROTO_ROADINFO_UPDATE = 10,
  Message_MessageType_PROTO_UPDATE_STSTEM_STATUS = 11,
  Message_MessageType_PROTO_DATA_QUERY_BATCHES_REQ = 12,
  Message_MessageType_PROTO_DATA_QUERY_BATCHES_RES = 13,
  Message_MessageType_PROTO_SYSTEM_INFO_REQ = 14,
  Message_MessageType_PROTO_SYSTEM_INFO_RES = 15,
  Message_MessageType_PROTO_CLIENT_INFO_REQ = 16,
  Message_MessageType_PROTO_CLIENT_INFO_RES = 17,
  Message_MessageType_PROTO_SYSTEM_CMD_REQ = 18,
  Message_MessageType_PROTO_SYSTEM_STORE_REQ = 19,
  Message_MessageType_PROTO_SYSTEM_NET_REQ = 20,
  Message_MessageType_PROTO_SYSTEM_CMD_RES = 21,
  Message_MessageType_PROTO_SYSTEM_STORE_RES = 22,
  Message_MessageType_PROTO_SYSTEM_NET_RES = 23,
  Message_MessageType_PROTO_DATABASE_CLOSED = 24,
  Message_MessageType_PROTO_COMMON_LOG = 25,
  Message_MessageType_Message_MessageType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  Message_MessageType_Message_MessageType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool Message_MessageType_IsValid(int value);
constexpr Message_MessageType Message_MessageType_MessageType_MIN = Message_MessageType_PROTO_COMMON_CMD;
constexpr Message_MessageType Message_MessageType_MessageType_MAX = Message_MessageType_PROTO_COMMON_LOG;
constexpr int Message_MessageType_MessageType_ARRAYSIZE = Message_MessageType_MessageType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Message_MessageType_descriptor();
template<typename T>
inline const std::string& Message_MessageType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, Message_MessageType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function Message_MessageType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    Message_MessageType_descriptor(), enum_t_value);
}
inline bool Message_MessageType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, Message_MessageType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<Message_MessageType>(
    Message_MessageType_descriptor(), name, value);
}
enum DataQuery_dataType : int {
  DataQuery_dataType_DATA_TYPE_RAW = 0,
  DataQuery_dataType_DATA_TYPE_STABILITY = 1,
  DataQuery_dataType_DATA_TYPE_TSARI = 2,
  DataQuery_dataType_DataQuery_dataType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  DataQuery_dataType_DataQuery_dataType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool DataQuery_dataType_IsValid(int value);
constexpr DataQuery_dataType DataQuery_dataType_dataType_MIN = DataQuery_dataType_DATA_TYPE_RAW;
constexpr DataQuery_dataType DataQuery_dataType_dataType_MAX = DataQuery_dataType_DATA_TYPE_TSARI;
constexpr int DataQuery_dataType_dataType_ARRAYSIZE = DataQuery_dataType_dataType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* DataQuery_dataType_descriptor();
template<typename T>
inline const std::string& DataQuery_dataType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, DataQuery_dataType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function DataQuery_dataType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    DataQuery_dataType_descriptor(), enum_t_value);
}
inline bool DataQuery_dataType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, DataQuery_dataType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<DataQuery_dataType>(
    DataQuery_dataType_descriptor(), name, value);
}
enum ConnectArgs_ConnectType : int {
  ConnectArgs_ConnectType_PROTO_MQTT = 0,
  ConnectArgs_ConnectType_PROTO_HTTP = 1,
  ConnectArgs_ConnectType_PROTO_KAFKA = 2,
  ConnectArgs_ConnectType_PROTO_TCP_SERVER = 3,
  ConnectArgs_ConnectType_ConnectArgs_ConnectType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  ConnectArgs_ConnectType_ConnectArgs_ConnectType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool ConnectArgs_ConnectType_IsValid(int value);
constexpr ConnectArgs_ConnectType ConnectArgs_ConnectType_ConnectType_MIN = ConnectArgs_ConnectType_PROTO_MQTT;
constexpr ConnectArgs_ConnectType ConnectArgs_ConnectType_ConnectType_MAX = ConnectArgs_ConnectType_PROTO_TCP_SERVER;
constexpr int ConnectArgs_ConnectType_ConnectType_ARRAYSIZE = ConnectArgs_ConnectType_ConnectType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ConnectArgs_ConnectType_descriptor();
template<typename T>
inline const std::string& ConnectArgs_ConnectType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, ConnectArgs_ConnectType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function ConnectArgs_ConnectType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    ConnectArgs_ConnectType_descriptor(), enum_t_value);
}
inline bool ConnectArgs_ConnectType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, ConnectArgs_ConnectType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<ConnectArgs_ConnectType>(
    ConnectArgs_ConnectType_descriptor(), name, value);
}
enum CommonCMD_cmdType : int {
  CommonCMD_cmdType_PROTO_SYSTEM_INIT = 0,
  CommonCMD_cmdType_PROTO_SYSTEM_START = 1,
  CommonCMD_cmdType_PROTO_SYSTEM_STOP = 2,
  CommonCMD_cmdType_PROTO_SYSTEM_PAUSE = 3,
  CommonCMD_cmdType_CommonCMD_cmdType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  CommonCMD_cmdType_CommonCMD_cmdType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool CommonCMD_cmdType_IsValid(int value);
constexpr CommonCMD_cmdType CommonCMD_cmdType_cmdType_MIN = CommonCMD_cmdType_PROTO_SYSTEM_INIT;
constexpr CommonCMD_cmdType CommonCMD_cmdType_cmdType_MAX = CommonCMD_cmdType_PROTO_SYSTEM_PAUSE;
constexpr int CommonCMD_cmdType_cmdType_ARRAYSIZE = CommonCMD_cmdType_cmdType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* CommonCMD_cmdType_descriptor();
template<typename T>
inline const std::string& CommonCMD_cmdType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, CommonCMD_cmdType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function CommonCMD_cmdType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    CommonCMD_cmdType_descriptor(), enum_t_value);
}
inline bool CommonCMD_cmdType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, CommonCMD_cmdType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<CommonCMD_cmdType>(
    CommonCMD_cmdType_descriptor(), name, value);
}
enum SystemStatus_statusType : int {
  SystemStatus_statusType_START = 0,
  SystemStatus_statusType_STOP = 1,
  SystemStatus_statusType_PAUSE = 2,
  SystemStatus_statusType_SystemStatus_statusType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  SystemStatus_statusType_SystemStatus_statusType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool SystemStatus_statusType_IsValid(int value);
constexpr SystemStatus_statusType SystemStatus_statusType_statusType_MIN = SystemStatus_statusType_START;
constexpr SystemStatus_statusType SystemStatus_statusType_statusType_MAX = SystemStatus_statusType_PAUSE;
constexpr int SystemStatus_statusType_statusType_ARRAYSIZE = SystemStatus_statusType_statusType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* SystemStatus_statusType_descriptor();
template<typename T>
inline const std::string& SystemStatus_statusType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, SystemStatus_statusType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function SystemStatus_statusType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    SystemStatus_statusType_descriptor(), enum_t_value);
}
inline bool SystemStatus_statusType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, SystemStatus_statusType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<SystemStatus_statusType>(
    SystemStatus_statusType_descriptor(), name, value);
}
enum SystemCmd_statusType : int {
  SystemCmd_statusType_RECONNECTSERVER = 0,
  SystemCmd_statusType_RESTARTSERVER = 1,
  SystemCmd_statusType_REBOOT = 2,
  SystemCmd_statusType_NOCMD = 3,
  SystemCmd_statusType_SystemCmd_statusType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  SystemCmd_statusType_SystemCmd_statusType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool SystemCmd_statusType_IsValid(int value);
constexpr SystemCmd_statusType SystemCmd_statusType_statusType_MIN = SystemCmd_statusType_RECONNECTSERVER;
constexpr SystemCmd_statusType SystemCmd_statusType_statusType_MAX = SystemCmd_statusType_NOCMD;
constexpr int SystemCmd_statusType_statusType_ARRAYSIZE = SystemCmd_statusType_statusType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* SystemCmd_statusType_descriptor();
template<typename T>
inline const std::string& SystemCmd_statusType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, SystemCmd_statusType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function SystemCmd_statusType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    SystemCmd_statusType_descriptor(), enum_t_value);
}
inline bool SystemCmd_statusType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, SystemCmd_statusType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<SystemCmd_statusType>(
    SystemCmd_statusType_descriptor(), name, value);
}
// ===================================================================

class Message final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:datacapture.Message) */ {
 public:
  inline Message() : Message(nullptr) {}
  ~Message() override;
  explicit PROTOBUF_CONSTEXPR Message(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Message(const Message& from);
  Message(Message&& from) noexcept
    : Message() {
    *this = ::std::move(from);
  }

  inline Message& operator=(const Message& from) {
    CopyFrom(from);
    return *this;
  }
  inline Message& operator=(Message&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Message& default_instance() {
    return *internal_default_instance();
  }
  enum DataCase {
    kCommonCmd = 2,
    kArgsList = 3,
    kCapStatus = 4,
    kDataQueryReq = 5,
    kDataQueryRes = 6,
    kCompressReq = 7,
    kCompressRes = 8,
    kMonitor = 9,
    kRoadInfoList = 10,
    kSystemStatus = 11,
    kDataQueryBatchesReq = 12,
    kDataQueryBatchesRes = 13,
    kSystemInfo = 14,
    kClientInfo = 15,
    kSystemcmd = 16,
    kStoreinfo = 17,
    kNetconfig = 18,
    kSystemLog = 19,
    DATA_NOT_SET = 0,
  };

  static inline const Message* internal_default_instance() {
    return reinterpret_cast<const Message*>(
               &_Message_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(Message& a, Message& b) {
    a.Swap(&b);
  }
  inline void Swap(Message* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Message* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Message* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Message>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Message& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Message& from) {
    Message::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Message* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "datacapture.Message";
  }
  protected:
  explicit Message(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef Message_MessageType MessageType;
  static constexpr MessageType PROTO_COMMON_CMD =
    Message_MessageType_PROTO_COMMON_CMD;
  static constexpr MessageType PROTO_UPDATE_CONFIG =
    Message_MessageType_PROTO_UPDATE_CONFIG;
  static constexpr MessageType PROTO_UPDATE_STATUS =
    Message_MessageType_PROTO_UPDATE_STATUS;
  static constexpr MessageType PROTO_DATA_QUERY_REQ =
    Message_MessageType_PROTO_DATA_QUERY_REQ;
  static constexpr MessageType PROTO_DATA_QUERY_RES =
    Message_MessageType_PROTO_DATA_QUERY_RES;
  static constexpr MessageType PROTO_DATA_COMPRESS_REQ =
    Message_MessageType_PROTO_DATA_COMPRESS_REQ;
  static constexpr MessageType PROTO_DATA_COMPRESS_RES =
    Message_MessageType_PROTO_DATA_COMPRESS_RES;
  static constexpr MessageType PROTO_DATA_MONITOR =
    Message_MessageType_PROTO_DATA_MONITOR;
  static constexpr MessageType PROTO_CONFIG_REQ =
    Message_MessageType_PROTO_CONFIG_REQ;
  static constexpr MessageType PROTO_CONFIG_RES =
    Message_MessageType_PROTO_CONFIG_RES;
  static constexpr MessageType PROTO_ROADINFO_UPDATE =
    Message_MessageType_PROTO_ROADINFO_UPDATE;
  static constexpr MessageType PROTO_UPDATE_STSTEM_STATUS =
    Message_MessageType_PROTO_UPDATE_STSTEM_STATUS;
  static constexpr MessageType PROTO_DATA_QUERY_BATCHES_REQ =
    Message_MessageType_PROTO_DATA_QUERY_BATCHES_REQ;
  static constexpr MessageType PROTO_DATA_QUERY_BATCHES_RES =
    Message_MessageType_PROTO_DATA_QUERY_BATCHES_RES;
  static constexpr MessageType PROTO_SYSTEM_INFO_REQ =
    Message_MessageType_PROTO_SYSTEM_INFO_REQ;
  static constexpr MessageType PROTO_SYSTEM_INFO_RES =
    Message_MessageType_PROTO_SYSTEM_INFO_RES;
  static constexpr MessageType PROTO_CLIENT_INFO_REQ =
    Message_MessageType_PROTO_CLIENT_INFO_REQ;
  static constexpr MessageType PROTO_CLIENT_INFO_RES =
    Message_MessageType_PROTO_CLIENT_INFO_RES;
  static constexpr MessageType PROTO_SYSTEM_CMD_REQ =
    Message_MessageType_PROTO_SYSTEM_CMD_REQ;
  static constexpr MessageType PROTO_SYSTEM_STORE_REQ =
    Message_MessageType_PROTO_SYSTEM_STORE_REQ;
  static constexpr MessageType PROTO_SYSTEM_NET_REQ =
    Message_MessageType_PROTO_SYSTEM_NET_REQ;
  static constexpr MessageType PROTO_SYSTEM_CMD_RES =
    Message_MessageType_PROTO_SYSTEM_CMD_RES;
  static constexpr MessageType PROTO_SYSTEM_STORE_RES =
    Message_MessageType_PROTO_SYSTEM_STORE_RES;
  static constexpr MessageType PROTO_SYSTEM_NET_RES =
    Message_MessageType_PROTO_SYSTEM_NET_RES;
  static constexpr MessageType PROTO_DATABASE_CLOSED =
    Message_MessageType_PROTO_DATABASE_CLOSED;
  static constexpr MessageType PROTO_COMMON_LOG =
    Message_MessageType_PROTO_COMMON_LOG;
  static inline bool MessageType_IsValid(int value) {
    return Message_MessageType_IsValid(value);
  }
  static constexpr MessageType MessageType_MIN =
    Message_MessageType_MessageType_MIN;
  static constexpr MessageType MessageType_MAX =
    Message_MessageType_MessageType_MAX;
  static constexpr int MessageType_ARRAYSIZE =
    Message_MessageType_MessageType_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  MessageType_descriptor() {
    return Message_MessageType_descriptor();
  }
  template<typename T>
  static inline const std::string& MessageType_Name(T enum_t_value) {
    static_assert(::std::is_same<T, MessageType>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function MessageType_Name.");
    return Message_MessageType_Name(enum_t_value);
  }
  static inline bool MessageType_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      MessageType* value) {
    return Message_MessageType_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kTypeFieldNumber = 1,
    kCommonCmdFieldNumber = 2,
    kArgsListFieldNumber = 3,
    kCapStatusFieldNumber = 4,
    kDataQueryReqFieldNumber = 5,
    kDataQueryResFieldNumber = 6,
    kCompressReqFieldNumber = 7,
    kCompressResFieldNumber = 8,
    kMonitorFieldNumber = 9,
    kRoadInfoListFieldNumber = 10,
    kSystemStatusFieldNumber = 11,
    kDataQueryBatchesReqFieldNumber = 12,
    kDataQueryBatchesResFieldNumber = 13,
    kSystemInfoFieldNumber = 14,
    kClientInfoFieldNumber = 15,
    kSystemcmdFieldNumber = 16,
    kStoreinfoFieldNumber = 17,
    kNetconfigFieldNumber = 18,
    kSystemLogFieldNumber = 19,
  };
  // .datacapture.Message.MessageType type = 1;
  void clear_type();
  ::datacapture::Message_MessageType type() const;
  void set_type(::datacapture::Message_MessageType value);
  private:
  ::datacapture::Message_MessageType _internal_type() const;
  void _internal_set_type(::datacapture::Message_MessageType value);
  public:

  // .datacapture.CommonCMD commonCmd = 2;
  bool has_commoncmd() const;
  private:
  bool _internal_has_commoncmd() const;
  public:
  void clear_commoncmd();
  const ::datacapture::CommonCMD& commoncmd() const;
  PROTOBUF_NODISCARD ::datacapture::CommonCMD* release_commoncmd();
  ::datacapture::CommonCMD* mutable_commoncmd();
  void set_allocated_commoncmd(::datacapture::CommonCMD* commoncmd);
  private:
  const ::datacapture::CommonCMD& _internal_commoncmd() const;
  ::datacapture::CommonCMD* _internal_mutable_commoncmd();
  public:
  void unsafe_arena_set_allocated_commoncmd(
      ::datacapture::CommonCMD* commoncmd);
  ::datacapture::CommonCMD* unsafe_arena_release_commoncmd();

  // .datacapture.ArgsList argsList = 3;
  bool has_argslist() const;
  private:
  bool _internal_has_argslist() const;
  public:
  void clear_argslist();
  const ::datacapture::ArgsList& argslist() const;
  PROTOBUF_NODISCARD ::datacapture::ArgsList* release_argslist();
  ::datacapture::ArgsList* mutable_argslist();
  void set_allocated_argslist(::datacapture::ArgsList* argslist);
  private:
  const ::datacapture::ArgsList& _internal_argslist() const;
  ::datacapture::ArgsList* _internal_mutable_argslist();
  public:
  void unsafe_arena_set_allocated_argslist(
      ::datacapture::ArgsList* argslist);
  ::datacapture::ArgsList* unsafe_arena_release_argslist();

  // .datacapture.CapStatus capStatus = 4;
  bool has_capstatus() const;
  private:
  bool _internal_has_capstatus() const;
  public:
  void clear_capstatus();
  const ::datacapture::CapStatus& capstatus() const;
  PROTOBUF_NODISCARD ::datacapture::CapStatus* release_capstatus();
  ::datacapture::CapStatus* mutable_capstatus();
  void set_allocated_capstatus(::datacapture::CapStatus* capstatus);
  private:
  const ::datacapture::CapStatus& _internal_capstatus() const;
  ::datacapture::CapStatus* _internal_mutable_capstatus();
  public:
  void unsafe_arena_set_allocated_capstatus(
      ::datacapture::CapStatus* capstatus);
  ::datacapture::CapStatus* unsafe_arena_release_capstatus();

  // .datacapture.DataQuery dataQueryReq = 5;
  bool has_dataqueryreq() const;
  private:
  bool _internal_has_dataqueryreq() const;
  public:
  void clear_dataqueryreq();
  const ::datacapture::DataQuery& dataqueryreq() const;
  PROTOBUF_NODISCARD ::datacapture::DataQuery* release_dataqueryreq();
  ::datacapture::DataQuery* mutable_dataqueryreq();
  void set_allocated_dataqueryreq(::datacapture::DataQuery* dataqueryreq);
  private:
  const ::datacapture::DataQuery& _internal_dataqueryreq() const;
  ::datacapture::DataQuery* _internal_mutable_dataqueryreq();
  public:
  void unsafe_arena_set_allocated_dataqueryreq(
      ::datacapture::DataQuery* dataqueryreq);
  ::datacapture::DataQuery* unsafe_arena_release_dataqueryreq();

  // .datacapture.DataQueryRes dataQueryRes = 6;
  bool has_dataqueryres() const;
  private:
  bool _internal_has_dataqueryres() const;
  public:
  void clear_dataqueryres();
  const ::datacapture::DataQueryRes& dataqueryres() const;
  PROTOBUF_NODISCARD ::datacapture::DataQueryRes* release_dataqueryres();
  ::datacapture::DataQueryRes* mutable_dataqueryres();
  void set_allocated_dataqueryres(::datacapture::DataQueryRes* dataqueryres);
  private:
  const ::datacapture::DataQueryRes& _internal_dataqueryres() const;
  ::datacapture::DataQueryRes* _internal_mutable_dataqueryres();
  public:
  void unsafe_arena_set_allocated_dataqueryres(
      ::datacapture::DataQueryRes* dataqueryres);
  ::datacapture::DataQueryRes* unsafe_arena_release_dataqueryres();

  // .datacapture.CompressReqList compressReq = 7;
  bool has_compressreq() const;
  private:
  bool _internal_has_compressreq() const;
  public:
  void clear_compressreq();
  const ::datacapture::CompressReqList& compressreq() const;
  PROTOBUF_NODISCARD ::datacapture::CompressReqList* release_compressreq();
  ::datacapture::CompressReqList* mutable_compressreq();
  void set_allocated_compressreq(::datacapture::CompressReqList* compressreq);
  private:
  const ::datacapture::CompressReqList& _internal_compressreq() const;
  ::datacapture::CompressReqList* _internal_mutable_compressreq();
  public:
  void unsafe_arena_set_allocated_compressreq(
      ::datacapture::CompressReqList* compressreq);
  ::datacapture::CompressReqList* unsafe_arena_release_compressreq();

  // .datacapture.CompressRes compressRes = 8;
  bool has_compressres() const;
  private:
  bool _internal_has_compressres() const;
  public:
  void clear_compressres();
  const ::datacapture::CompressRes& compressres() const;
  PROTOBUF_NODISCARD ::datacapture::CompressRes* release_compressres();
  ::datacapture::CompressRes* mutable_compressres();
  void set_allocated_compressres(::datacapture::CompressRes* compressres);
  private:
  const ::datacapture::CompressRes& _internal_compressres() const;
  ::datacapture::CompressRes* _internal_mutable_compressres();
  public:
  void unsafe_arena_set_allocated_compressres(
      ::datacapture::CompressRes* compressres);
  ::datacapture::CompressRes* unsafe_arena_release_compressres();

  // .datacapture.Monitor monitor = 9;
  bool has_monitor() const;
  private:
  bool _internal_has_monitor() const;
  public:
  void clear_monitor();
  const ::datacapture::Monitor& monitor() const;
  PROTOBUF_NODISCARD ::datacapture::Monitor* release_monitor();
  ::datacapture::Monitor* mutable_monitor();
  void set_allocated_monitor(::datacapture::Monitor* monitor);
  private:
  const ::datacapture::Monitor& _internal_monitor() const;
  ::datacapture::Monitor* _internal_mutable_monitor();
  public:
  void unsafe_arena_set_allocated_monitor(
      ::datacapture::Monitor* monitor);
  ::datacapture::Monitor* unsafe_arena_release_monitor();

  // .datacapture.RoadInfoList roadInfoList = 10;
  bool has_roadinfolist() const;
  private:
  bool _internal_has_roadinfolist() const;
  public:
  void clear_roadinfolist();
  const ::datacapture::RoadInfoList& roadinfolist() const;
  PROTOBUF_NODISCARD ::datacapture::RoadInfoList* release_roadinfolist();
  ::datacapture::RoadInfoList* mutable_roadinfolist();
  void set_allocated_roadinfolist(::datacapture::RoadInfoList* roadinfolist);
  private:
  const ::datacapture::RoadInfoList& _internal_roadinfolist() const;
  ::datacapture::RoadInfoList* _internal_mutable_roadinfolist();
  public:
  void unsafe_arena_set_allocated_roadinfolist(
      ::datacapture::RoadInfoList* roadinfolist);
  ::datacapture::RoadInfoList* unsafe_arena_release_roadinfolist();

  // .datacapture.SystemStatus systemStatus = 11;
  bool has_systemstatus() const;
  private:
  bool _internal_has_systemstatus() const;
  public:
  void clear_systemstatus();
  const ::datacapture::SystemStatus& systemstatus() const;
  PROTOBUF_NODISCARD ::datacapture::SystemStatus* release_systemstatus();
  ::datacapture::SystemStatus* mutable_systemstatus();
  void set_allocated_systemstatus(::datacapture::SystemStatus* systemstatus);
  private:
  const ::datacapture::SystemStatus& _internal_systemstatus() const;
  ::datacapture::SystemStatus* _internal_mutable_systemstatus();
  public:
  void unsafe_arena_set_allocated_systemstatus(
      ::datacapture::SystemStatus* systemstatus);
  ::datacapture::SystemStatus* unsafe_arena_release_systemstatus();

  // .datacapture.DataQueryBatchesReq dataQueryBatchesReq = 12;
  bool has_dataquerybatchesreq() const;
  private:
  bool _internal_has_dataquerybatchesreq() const;
  public:
  void clear_dataquerybatchesreq();
  const ::datacapture::DataQueryBatchesReq& dataquerybatchesreq() const;
  PROTOBUF_NODISCARD ::datacapture::DataQueryBatchesReq* release_dataquerybatchesreq();
  ::datacapture::DataQueryBatchesReq* mutable_dataquerybatchesreq();
  void set_allocated_dataquerybatchesreq(::datacapture::DataQueryBatchesReq* dataquerybatchesreq);
  private:
  const ::datacapture::DataQueryBatchesReq& _internal_dataquerybatchesreq() const;
  ::datacapture::DataQueryBatchesReq* _internal_mutable_dataquerybatchesreq();
  public:
  void unsafe_arena_set_allocated_dataquerybatchesreq(
      ::datacapture::DataQueryBatchesReq* dataquerybatchesreq);
  ::datacapture::DataQueryBatchesReq* unsafe_arena_release_dataquerybatchesreq();

  // .datacapture.DataQueryBatchesRes dataQueryBatchesRes = 13;
  bool has_dataquerybatchesres() const;
  private:
  bool _internal_has_dataquerybatchesres() const;
  public:
  void clear_dataquerybatchesres();
  const ::datacapture::DataQueryBatchesRes& dataquerybatchesres() const;
  PROTOBUF_NODISCARD ::datacapture::DataQueryBatchesRes* release_dataquerybatchesres();
  ::datacapture::DataQueryBatchesRes* mutable_dataquerybatchesres();
  void set_allocated_dataquerybatchesres(::datacapture::DataQueryBatchesRes* dataquerybatchesres);
  private:
  const ::datacapture::DataQueryBatchesRes& _internal_dataquerybatchesres() const;
  ::datacapture::DataQueryBatchesRes* _internal_mutable_dataquerybatchesres();
  public:
  void unsafe_arena_set_allocated_dataquerybatchesres(
      ::datacapture::DataQueryBatchesRes* dataquerybatchesres);
  ::datacapture::DataQueryBatchesRes* unsafe_arena_release_dataquerybatchesres();

  // .datacapture.SystemInfo systemInfo = 14;
  bool has_systeminfo() const;
  private:
  bool _internal_has_systeminfo() const;
  public:
  void clear_systeminfo();
  const ::datacapture::SystemInfo& systeminfo() const;
  PROTOBUF_NODISCARD ::datacapture::SystemInfo* release_systeminfo();
  ::datacapture::SystemInfo* mutable_systeminfo();
  void set_allocated_systeminfo(::datacapture::SystemInfo* systeminfo);
  private:
  const ::datacapture::SystemInfo& _internal_systeminfo() const;
  ::datacapture::SystemInfo* _internal_mutable_systeminfo();
  public:
  void unsafe_arena_set_allocated_systeminfo(
      ::datacapture::SystemInfo* systeminfo);
  ::datacapture::SystemInfo* unsafe_arena_release_systeminfo();

  // .datacapture.ClientInfo clientInfo = 15;
  bool has_clientinfo() const;
  private:
  bool _internal_has_clientinfo() const;
  public:
  void clear_clientinfo();
  const ::datacapture::ClientInfo& clientinfo() const;
  PROTOBUF_NODISCARD ::datacapture::ClientInfo* release_clientinfo();
  ::datacapture::ClientInfo* mutable_clientinfo();
  void set_allocated_clientinfo(::datacapture::ClientInfo* clientinfo);
  private:
  const ::datacapture::ClientInfo& _internal_clientinfo() const;
  ::datacapture::ClientInfo* _internal_mutable_clientinfo();
  public:
  void unsafe_arena_set_allocated_clientinfo(
      ::datacapture::ClientInfo* clientinfo);
  ::datacapture::ClientInfo* unsafe_arena_release_clientinfo();

  // .datacapture.SystemCmd systemcmd = 16;
  bool has_systemcmd() const;
  private:
  bool _internal_has_systemcmd() const;
  public:
  void clear_systemcmd();
  const ::datacapture::SystemCmd& systemcmd() const;
  PROTOBUF_NODISCARD ::datacapture::SystemCmd* release_systemcmd();
  ::datacapture::SystemCmd* mutable_systemcmd();
  void set_allocated_systemcmd(::datacapture::SystemCmd* systemcmd);
  private:
  const ::datacapture::SystemCmd& _internal_systemcmd() const;
  ::datacapture::SystemCmd* _internal_mutable_systemcmd();
  public:
  void unsafe_arena_set_allocated_systemcmd(
      ::datacapture::SystemCmd* systemcmd);
  ::datacapture::SystemCmd* unsafe_arena_release_systemcmd();

  // .datacapture.storeInfo storeinfo = 17;
  bool has_storeinfo() const;
  private:
  bool _internal_has_storeinfo() const;
  public:
  void clear_storeinfo();
  const ::datacapture::storeInfo& storeinfo() const;
  PROTOBUF_NODISCARD ::datacapture::storeInfo* release_storeinfo();
  ::datacapture::storeInfo* mutable_storeinfo();
  void set_allocated_storeinfo(::datacapture::storeInfo* storeinfo);
  private:
  const ::datacapture::storeInfo& _internal_storeinfo() const;
  ::datacapture::storeInfo* _internal_mutable_storeinfo();
  public:
  void unsafe_arena_set_allocated_storeinfo(
      ::datacapture::storeInfo* storeinfo);
  ::datacapture::storeInfo* unsafe_arena_release_storeinfo();

  // .datacapture.NetConfig netconfig = 18;
  bool has_netconfig() const;
  private:
  bool _internal_has_netconfig() const;
  public:
  void clear_netconfig();
  const ::datacapture::NetConfig& netconfig() const;
  PROTOBUF_NODISCARD ::datacapture::NetConfig* release_netconfig();
  ::datacapture::NetConfig* mutable_netconfig();
  void set_allocated_netconfig(::datacapture::NetConfig* netconfig);
  private:
  const ::datacapture::NetConfig& _internal_netconfig() const;
  ::datacapture::NetConfig* _internal_mutable_netconfig();
  public:
  void unsafe_arena_set_allocated_netconfig(
      ::datacapture::NetConfig* netconfig);
  ::datacapture::NetConfig* unsafe_arena_release_netconfig();

  // .datacapture.SystemLog systemLog = 19;
  bool has_systemlog() const;
  private:
  bool _internal_has_systemlog() const;
  public:
  void clear_systemlog();
  const ::datacapture::SystemLog& systemlog() const;
  PROTOBUF_NODISCARD ::datacapture::SystemLog* release_systemlog();
  ::datacapture::SystemLog* mutable_systemlog();
  void set_allocated_systemlog(::datacapture::SystemLog* systemlog);
  private:
  const ::datacapture::SystemLog& _internal_systemlog() const;
  ::datacapture::SystemLog* _internal_mutable_systemlog();
  public:
  void unsafe_arena_set_allocated_systemlog(
      ::datacapture::SystemLog* systemlog);
  ::datacapture::SystemLog* unsafe_arena_release_systemlog();

  void clear_data();
  DataCase data_case() const;
  // @@protoc_insertion_point(class_scope:datacapture.Message)
 private:
  class _Internal;
  void set_has_commoncmd();
  void set_has_argslist();
  void set_has_capstatus();
  void set_has_dataqueryreq();
  void set_has_dataqueryres();
  void set_has_compressreq();
  void set_has_compressres();
  void set_has_monitor();
  void set_has_roadinfolist();
  void set_has_systemstatus();
  void set_has_dataquerybatchesreq();
  void set_has_dataquerybatchesres();
  void set_has_systeminfo();
  void set_has_clientinfo();
  void set_has_systemcmd();
  void set_has_storeinfo();
  void set_has_netconfig();
  void set_has_systemlog();

  inline bool has_data() const;
  inline void clear_has_data();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int type_;
    union DataUnion {
      constexpr DataUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      ::datacapture::CommonCMD* commoncmd_;
      ::datacapture::ArgsList* argslist_;
      ::datacapture::CapStatus* capstatus_;
      ::datacapture::DataQuery* dataqueryreq_;
      ::datacapture::DataQueryRes* dataqueryres_;
      ::datacapture::CompressReqList* compressreq_;
      ::datacapture::CompressRes* compressres_;
      ::datacapture::Monitor* monitor_;
      ::datacapture::RoadInfoList* roadinfolist_;
      ::datacapture::SystemStatus* systemstatus_;
      ::datacapture::DataQueryBatchesReq* dataquerybatchesreq_;
      ::datacapture::DataQueryBatchesRes* dataquerybatchesres_;
      ::datacapture::SystemInfo* systeminfo_;
      ::datacapture::ClientInfo* clientinfo_;
      ::datacapture::SystemCmd* systemcmd_;
      ::datacapture::storeInfo* storeinfo_;
      ::datacapture::NetConfig* netconfig_;
      ::datacapture::SystemLog* systemlog_;
    } data_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_datacapture_2eCommunicate_2eproto;
};
// -------------------------------------------------------------------

class RawData final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:datacapture.RawData) */ {
 public:
  inline RawData() : RawData(nullptr) {}
  ~RawData() override;
  explicit PROTOBUF_CONSTEXPR RawData(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RawData(const RawData& from);
  RawData(RawData&& from) noexcept
    : RawData() {
    *this = ::std::move(from);
  }

  inline RawData& operator=(const RawData& from) {
    CopyFrom(from);
    return *this;
  }
  inline RawData& operator=(RawData&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RawData& default_instance() {
    return *internal_default_instance();
  }
  static inline const RawData* internal_default_instance() {
    return reinterpret_cast<const RawData*>(
               &_RawData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(RawData& a, RawData& b) {
    a.Swap(&b);
  }
  inline void Swap(RawData* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RawData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RawData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RawData>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RawData& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RawData& from) {
    RawData::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RawData* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "datacapture.RawData";
  }
  protected:
  explicit RawData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStrCrossroadIdFieldNumber = 1,
    kStrDataFieldNumber = 5,
    kLlRecvTimeFieldNumber = 2,
    kLlDataTimeFieldNumber = 3,
    kDwDatalengthFieldNumber = 4,
  };
  // string strCrossroadId = 1;
  void clear_strcrossroadid();
  const std::string& strcrossroadid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_strcrossroadid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_strcrossroadid();
  PROTOBUF_NODISCARD std::string* release_strcrossroadid();
  void set_allocated_strcrossroadid(std::string* strcrossroadid);
  private:
  const std::string& _internal_strcrossroadid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_strcrossroadid(const std::string& value);
  std::string* _internal_mutable_strcrossroadid();
  public:

  // string strData = 5;
  void clear_strdata();
  const std::string& strdata() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_strdata(ArgT0&& arg0, ArgT... args);
  std::string* mutable_strdata();
  PROTOBUF_NODISCARD std::string* release_strdata();
  void set_allocated_strdata(std::string* strdata);
  private:
  const std::string& _internal_strdata() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_strdata(const std::string& value);
  std::string* _internal_mutable_strdata();
  public:

  // uint64 llRecvTime = 2;
  void clear_llrecvtime();
  uint64_t llrecvtime() const;
  void set_llrecvtime(uint64_t value);
  private:
  uint64_t _internal_llrecvtime() const;
  void _internal_set_llrecvtime(uint64_t value);
  public:

  // uint64 llDataTime = 3;
  void clear_lldatatime();
  uint64_t lldatatime() const;
  void set_lldatatime(uint64_t value);
  private:
  uint64_t _internal_lldatatime() const;
  void _internal_set_lldatatime(uint64_t value);
  public:

  // uint32 dwDatalength = 4;
  void clear_dwdatalength();
  uint32_t dwdatalength() const;
  void set_dwdatalength(uint32_t value);
  private:
  uint32_t _internal_dwdatalength() const;
  void _internal_set_dwdatalength(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:datacapture.RawData)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr strcrossroadid_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr strdata_;
    uint64_t llrecvtime_;
    uint64_t lldatatime_;
    uint32_t dwdatalength_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_datacapture_2eCommunicate_2eproto;
};
// -------------------------------------------------------------------

class CapStatus final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:datacapture.CapStatus) */ {
 public:
  inline CapStatus() : CapStatus(nullptr) {}
  ~CapStatus() override;
  explicit PROTOBUF_CONSTEXPR CapStatus(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CapStatus(const CapStatus& from);
  CapStatus(CapStatus&& from) noexcept
    : CapStatus() {
    *this = ::std::move(from);
  }

  inline CapStatus& operator=(const CapStatus& from) {
    CopyFrom(from);
    return *this;
  }
  inline CapStatus& operator=(CapStatus&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CapStatus& default_instance() {
    return *internal_default_instance();
  }
  static inline const CapStatus* internal_default_instance() {
    return reinterpret_cast<const CapStatus*>(
               &_CapStatus_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(CapStatus& a, CapStatus& b) {
    a.Swap(&b);
  }
  inline void Swap(CapStatus* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CapStatus* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CapStatus* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CapStatus>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CapStatus& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CapStatus& from) {
    CapStatus::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CapStatus* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "datacapture.CapStatus";
  }
  protected:
  explicit CapStatus(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStrCrossroadIdFieldNumber = 1,
    kStrDescribeFieldNumber = 2,
    kLlTimestampFieldNumber = 3,
    kBIsCaptruingFieldNumber = 4,
    kFFreqFieldNumber = 5,
    kDwRecvDataCntFieldNumber = 6,
  };
  // string strCrossroadId = 1;
  void clear_strcrossroadid();
  const std::string& strcrossroadid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_strcrossroadid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_strcrossroadid();
  PROTOBUF_NODISCARD std::string* release_strcrossroadid();
  void set_allocated_strcrossroadid(std::string* strcrossroadid);
  private:
  const std::string& _internal_strcrossroadid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_strcrossroadid(const std::string& value);
  std::string* _internal_mutable_strcrossroadid();
  public:

  // string strDescribe = 2;
  void clear_strdescribe();
  const std::string& strdescribe() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_strdescribe(ArgT0&& arg0, ArgT... args);
  std::string* mutable_strdescribe();
  PROTOBUF_NODISCARD std::string* release_strdescribe();
  void set_allocated_strdescribe(std::string* strdescribe);
  private:
  const std::string& _internal_strdescribe() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_strdescribe(const std::string& value);
  std::string* _internal_mutable_strdescribe();
  public:

  // uint64 llTimestamp = 3;
  void clear_lltimestamp();
  uint64_t lltimestamp() const;
  void set_lltimestamp(uint64_t value);
  private:
  uint64_t _internal_lltimestamp() const;
  void _internal_set_lltimestamp(uint64_t value);
  public:

  // bool bIsCaptruing = 4;
  void clear_biscaptruing();
  bool biscaptruing() const;
  void set_biscaptruing(bool value);
  private:
  bool _internal_biscaptruing() const;
  void _internal_set_biscaptruing(bool value);
  public:

  // float fFreq = 5;
  void clear_ffreq();
  float ffreq() const;
  void set_ffreq(float value);
  private:
  float _internal_ffreq() const;
  void _internal_set_ffreq(float value);
  public:

  // uint32 dwRecvDataCnt = 6;
  void clear_dwrecvdatacnt();
  uint32_t dwrecvdatacnt() const;
  void set_dwrecvdatacnt(uint32_t value);
  private:
  uint32_t _internal_dwrecvdatacnt() const;
  void _internal_set_dwrecvdatacnt(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:datacapture.CapStatus)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr strcrossroadid_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr strdescribe_;
    uint64_t lltimestamp_;
    bool biscaptruing_;
    float ffreq_;
    uint32_t dwrecvdatacnt_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_datacapture_2eCommunicate_2eproto;
};
// -------------------------------------------------------------------

class Monitor final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:datacapture.Monitor) */ {
 public:
  inline Monitor() : Monitor(nullptr) {}
  ~Monitor() override;
  explicit PROTOBUF_CONSTEXPR Monitor(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Monitor(const Monitor& from);
  Monitor(Monitor&& from) noexcept
    : Monitor() {
    *this = ::std::move(from);
  }

  inline Monitor& operator=(const Monitor& from) {
    CopyFrom(from);
    return *this;
  }
  inline Monitor& operator=(Monitor&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Monitor& default_instance() {
    return *internal_default_instance();
  }
  static inline const Monitor* internal_default_instance() {
    return reinterpret_cast<const Monitor*>(
               &_Monitor_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(Monitor& a, Monitor& b) {
    a.Swap(&b);
  }
  inline void Swap(Monitor* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Monitor* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Monitor* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Monitor>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Monitor& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Monitor& from) {
    Monitor::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Monitor* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "datacapture.Monitor";
  }
  protected:
  explicit Monitor(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStatusListFieldNumber = 5,
    kLlUpdateTimeFieldNumber = 2,
    kLlStartTimeFieldNumber = 3,
    kLlDurationTimeFieldNumber = 4,
    kDwStatusCntFieldNumber = 1,
  };
  // repeated .datacapture.CapStatus statusList = 5;
  int statuslist_size() const;
  private:
  int _internal_statuslist_size() const;
  public:
  void clear_statuslist();
  ::datacapture::CapStatus* mutable_statuslist(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::datacapture::CapStatus >*
      mutable_statuslist();
  private:
  const ::datacapture::CapStatus& _internal_statuslist(int index) const;
  ::datacapture::CapStatus* _internal_add_statuslist();
  public:
  const ::datacapture::CapStatus& statuslist(int index) const;
  ::datacapture::CapStatus* add_statuslist();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::datacapture::CapStatus >&
      statuslist() const;

  // uint64 llUpdateTime = 2;
  void clear_llupdatetime();
  uint64_t llupdatetime() const;
  void set_llupdatetime(uint64_t value);
  private:
  uint64_t _internal_llupdatetime() const;
  void _internal_set_llupdatetime(uint64_t value);
  public:

  // uint64 llStartTime = 3;
  void clear_llstarttime();
  uint64_t llstarttime() const;
  void set_llstarttime(uint64_t value);
  private:
  uint64_t _internal_llstarttime() const;
  void _internal_set_llstarttime(uint64_t value);
  public:

  // uint64 llDurationTime = 4;
  void clear_lldurationtime();
  uint64_t lldurationtime() const;
  void set_lldurationtime(uint64_t value);
  private:
  uint64_t _internal_lldurationtime() const;
  void _internal_set_lldurationtime(uint64_t value);
  public:

  // uint32 dwStatusCnt = 1;
  void clear_dwstatuscnt();
  uint32_t dwstatuscnt() const;
  void set_dwstatuscnt(uint32_t value);
  private:
  uint32_t _internal_dwstatuscnt() const;
  void _internal_set_dwstatuscnt(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:datacapture.Monitor)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::datacapture::CapStatus > statuslist_;
    uint64_t llupdatetime_;
    uint64_t llstarttime_;
    uint64_t lldurationtime_;
    uint32_t dwstatuscnt_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_datacapture_2eCommunicate_2eproto;
};
// -------------------------------------------------------------------

class DataQuery final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:datacapture.DataQuery) */ {
 public:
  inline DataQuery() : DataQuery(nullptr) {}
  ~DataQuery() override;
  explicit PROTOBUF_CONSTEXPR DataQuery(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DataQuery(const DataQuery& from);
  DataQuery(DataQuery&& from) noexcept
    : DataQuery() {
    *this = ::std::move(from);
  }

  inline DataQuery& operator=(const DataQuery& from) {
    CopyFrom(from);
    return *this;
  }
  inline DataQuery& operator=(DataQuery&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DataQuery& default_instance() {
    return *internal_default_instance();
  }
  static inline const DataQuery* internal_default_instance() {
    return reinterpret_cast<const DataQuery*>(
               &_DataQuery_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(DataQuery& a, DataQuery& b) {
    a.Swap(&b);
  }
  inline void Swap(DataQuery* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DataQuery* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DataQuery* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DataQuery>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DataQuery& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const DataQuery& from) {
    DataQuery::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DataQuery* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "datacapture.DataQuery";
  }
  protected:
  explicit DataQuery(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef DataQuery_dataType dataType;
  static constexpr dataType DATA_TYPE_RAW =
    DataQuery_dataType_DATA_TYPE_RAW;
  static constexpr dataType DATA_TYPE_STABILITY =
    DataQuery_dataType_DATA_TYPE_STABILITY;
  static constexpr dataType DATA_TYPE_TSARI =
    DataQuery_dataType_DATA_TYPE_TSARI;
  static inline bool dataType_IsValid(int value) {
    return DataQuery_dataType_IsValid(value);
  }
  static constexpr dataType dataType_MIN =
    DataQuery_dataType_dataType_MIN;
  static constexpr dataType dataType_MAX =
    DataQuery_dataType_dataType_MAX;
  static constexpr int dataType_ARRAYSIZE =
    DataQuery_dataType_dataType_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  dataType_descriptor() {
    return DataQuery_dataType_descriptor();
  }
  template<typename T>
  static inline const std::string& dataType_Name(T enum_t_value) {
    static_assert(::std::is_same<T, dataType>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function dataType_Name.");
    return DataQuery_dataType_Name(enum_t_value);
  }
  static inline bool dataType_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      dataType* value) {
    return DataQuery_dataType_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kStrCrossroadIdFieldNumber = 1,
    kLlStartTimeFieldNumber = 3,
    kLlEndTimeFieldNumber = 4,
    kTypeFieldNumber = 2,
  };
  // string strCrossroadId = 1;
  void clear_strcrossroadid();
  const std::string& strcrossroadid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_strcrossroadid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_strcrossroadid();
  PROTOBUF_NODISCARD std::string* release_strcrossroadid();
  void set_allocated_strcrossroadid(std::string* strcrossroadid);
  private:
  const std::string& _internal_strcrossroadid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_strcrossroadid(const std::string& value);
  std::string* _internal_mutable_strcrossroadid();
  public:

  // uint64 llStartTime = 3;
  void clear_llstarttime();
  uint64_t llstarttime() const;
  void set_llstarttime(uint64_t value);
  private:
  uint64_t _internal_llstarttime() const;
  void _internal_set_llstarttime(uint64_t value);
  public:

  // uint64 llEndTime = 4;
  void clear_llendtime();
  uint64_t llendtime() const;
  void set_llendtime(uint64_t value);
  private:
  uint64_t _internal_llendtime() const;
  void _internal_set_llendtime(uint64_t value);
  public:

  // .datacapture.DataQuery.dataType type = 2;
  void clear_type();
  ::datacapture::DataQuery_dataType type() const;
  void set_type(::datacapture::DataQuery_dataType value);
  private:
  ::datacapture::DataQuery_dataType _internal_type() const;
  void _internal_set_type(::datacapture::DataQuery_dataType value);
  public:

  // @@protoc_insertion_point(class_scope:datacapture.DataQuery)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr strcrossroadid_;
    uint64_t llstarttime_;
    uint64_t llendtime_;
    int type_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_datacapture_2eCommunicate_2eproto;
};
// -------------------------------------------------------------------

class DataQueryRes final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:datacapture.DataQueryRes) */ {
 public:
  inline DataQueryRes() : DataQueryRes(nullptr) {}
  ~DataQueryRes() override;
  explicit PROTOBUF_CONSTEXPR DataQueryRes(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DataQueryRes(const DataQueryRes& from);
  DataQueryRes(DataQueryRes&& from) noexcept
    : DataQueryRes() {
    *this = ::std::move(from);
  }

  inline DataQueryRes& operator=(const DataQueryRes& from) {
    CopyFrom(from);
    return *this;
  }
  inline DataQueryRes& operator=(DataQueryRes&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DataQueryRes& default_instance() {
    return *internal_default_instance();
  }
  static inline const DataQueryRes* internal_default_instance() {
    return reinterpret_cast<const DataQueryRes*>(
               &_DataQueryRes_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(DataQueryRes& a, DataQueryRes& b) {
    a.Swap(&b);
  }
  inline void Swap(DataQueryRes* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DataQueryRes* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DataQueryRes* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DataQueryRes>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DataQueryRes& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const DataQueryRes& from) {
    DataQueryRes::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DataQueryRes* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "datacapture.DataQueryRes";
  }
  protected:
  explicit DataQueryRes(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDataListFieldNumber = 4,
    kStrErrFieldNumber = 3,
    kDwCntFieldNumber = 1,
    kBIsSucceedFieldNumber = 2,
  };
  // repeated .datacapture.RawData dataList = 4;
  int datalist_size() const;
  private:
  int _internal_datalist_size() const;
  public:
  void clear_datalist();
  ::datacapture::RawData* mutable_datalist(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::datacapture::RawData >*
      mutable_datalist();
  private:
  const ::datacapture::RawData& _internal_datalist(int index) const;
  ::datacapture::RawData* _internal_add_datalist();
  public:
  const ::datacapture::RawData& datalist(int index) const;
  ::datacapture::RawData* add_datalist();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::datacapture::RawData >&
      datalist() const;

  // string strErr = 3;
  void clear_strerr();
  const std::string& strerr() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_strerr(ArgT0&& arg0, ArgT... args);
  std::string* mutable_strerr();
  PROTOBUF_NODISCARD std::string* release_strerr();
  void set_allocated_strerr(std::string* strerr);
  private:
  const std::string& _internal_strerr() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_strerr(const std::string& value);
  std::string* _internal_mutable_strerr();
  public:

  // uint32 dwCnt = 1;
  void clear_dwcnt();
  uint32_t dwcnt() const;
  void set_dwcnt(uint32_t value);
  private:
  uint32_t _internal_dwcnt() const;
  void _internal_set_dwcnt(uint32_t value);
  public:

  // bool bIsSucceed = 2;
  void clear_bissucceed();
  bool bissucceed() const;
  void set_bissucceed(bool value);
  private:
  bool _internal_bissucceed() const;
  void _internal_set_bissucceed(bool value);
  public:

  // @@protoc_insertion_point(class_scope:datacapture.DataQueryRes)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::datacapture::RawData > datalist_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr strerr_;
    uint32_t dwcnt_;
    bool bissucceed_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_datacapture_2eCommunicate_2eproto;
};
// -------------------------------------------------------------------

class DataQueryBatchesReq final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:datacapture.DataQueryBatchesReq) */ {
 public:
  inline DataQueryBatchesReq() : DataQueryBatchesReq(nullptr) {}
  ~DataQueryBatchesReq() override;
  explicit PROTOBUF_CONSTEXPR DataQueryBatchesReq(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DataQueryBatchesReq(const DataQueryBatchesReq& from);
  DataQueryBatchesReq(DataQueryBatchesReq&& from) noexcept
    : DataQueryBatchesReq() {
    *this = ::std::move(from);
  }

  inline DataQueryBatchesReq& operator=(const DataQueryBatchesReq& from) {
    CopyFrom(from);
    return *this;
  }
  inline DataQueryBatchesReq& operator=(DataQueryBatchesReq&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DataQueryBatchesReq& default_instance() {
    return *internal_default_instance();
  }
  static inline const DataQueryBatchesReq* internal_default_instance() {
    return reinterpret_cast<const DataQueryBatchesReq*>(
               &_DataQueryBatchesReq_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(DataQueryBatchesReq& a, DataQueryBatchesReq& b) {
    a.Swap(&b);
  }
  inline void Swap(DataQueryBatchesReq* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DataQueryBatchesReq* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DataQueryBatchesReq* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DataQueryBatchesReq>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DataQueryBatchesReq& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const DataQueryBatchesReq& from) {
    DataQueryBatchesReq::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DataQueryBatchesReq* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "datacapture.DataQueryBatchesReq";
  }
  protected:
  explicit DataQueryBatchesReq(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStrCrossIDFieldNumber = 1,
    kLlStartTimeFieldNumber = 2,
    kLlEndTimeFieldNumber = 3,
    kDwNowPageFieldNumber = 4,
    kDwPageSizeFieldNumber = 5,
  };
  // string strCrossID = 1;
  void clear_strcrossid();
  const std::string& strcrossid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_strcrossid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_strcrossid();
  PROTOBUF_NODISCARD std::string* release_strcrossid();
  void set_allocated_strcrossid(std::string* strcrossid);
  private:
  const std::string& _internal_strcrossid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_strcrossid(const std::string& value);
  std::string* _internal_mutable_strcrossid();
  public:

  // uint64 llStartTime = 2;
  void clear_llstarttime();
  uint64_t llstarttime() const;
  void set_llstarttime(uint64_t value);
  private:
  uint64_t _internal_llstarttime() const;
  void _internal_set_llstarttime(uint64_t value);
  public:

  // uint64 llEndTime = 3;
  void clear_llendtime();
  uint64_t llendtime() const;
  void set_llendtime(uint64_t value);
  private:
  uint64_t _internal_llendtime() const;
  void _internal_set_llendtime(uint64_t value);
  public:

  // uint32 dwNowPage = 4;
  void clear_dwnowpage();
  uint32_t dwnowpage() const;
  void set_dwnowpage(uint32_t value);
  private:
  uint32_t _internal_dwnowpage() const;
  void _internal_set_dwnowpage(uint32_t value);
  public:

  // uint32 dwPageSize = 5;
  void clear_dwpagesize();
  uint32_t dwpagesize() const;
  void set_dwpagesize(uint32_t value);
  private:
  uint32_t _internal_dwpagesize() const;
  void _internal_set_dwpagesize(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:datacapture.DataQueryBatchesReq)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr strcrossid_;
    uint64_t llstarttime_;
    uint64_t llendtime_;
    uint32_t dwnowpage_;
    uint32_t dwpagesize_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_datacapture_2eCommunicate_2eproto;
};
// -------------------------------------------------------------------

class DataQueryBatchesRes final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:datacapture.DataQueryBatchesRes) */ {
 public:
  inline DataQueryBatchesRes() : DataQueryBatchesRes(nullptr) {}
  ~DataQueryBatchesRes() override;
  explicit PROTOBUF_CONSTEXPR DataQueryBatchesRes(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DataQueryBatchesRes(const DataQueryBatchesRes& from);
  DataQueryBatchesRes(DataQueryBatchesRes&& from) noexcept
    : DataQueryBatchesRes() {
    *this = ::std::move(from);
  }

  inline DataQueryBatchesRes& operator=(const DataQueryBatchesRes& from) {
    CopyFrom(from);
    return *this;
  }
  inline DataQueryBatchesRes& operator=(DataQueryBatchesRes&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DataQueryBatchesRes& default_instance() {
    return *internal_default_instance();
  }
  static inline const DataQueryBatchesRes* internal_default_instance() {
    return reinterpret_cast<const DataQueryBatchesRes*>(
               &_DataQueryBatchesRes_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(DataQueryBatchesRes& a, DataQueryBatchesRes& b) {
    a.Swap(&b);
  }
  inline void Swap(DataQueryBatchesRes* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DataQueryBatchesRes* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DataQueryBatchesRes* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DataQueryBatchesRes>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DataQueryBatchesRes& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const DataQueryBatchesRes& from) {
    DataQueryBatchesRes::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DataQueryBatchesRes* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "datacapture.DataQueryBatchesRes";
  }
  protected:
  explicit DataQueryBatchesRes(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDataListFieldNumber = 8,
    kStrErrFieldNumber = 6,
    kBIsSucceedFieldNumber = 1,
    kDwNowPageFieldNumber = 2,
    kDwPageSizeFieldNumber = 3,
    kDwTotalPagesFieldNumber = 4,
    kDwTotalDatasFieldNumber = 5,
    kDwCntFieldNumber = 7,
  };
  // repeated .datacapture.RawData dataList = 8;
  int datalist_size() const;
  private:
  int _internal_datalist_size() const;
  public:
  void clear_datalist();
  ::datacapture::RawData* mutable_datalist(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::datacapture::RawData >*
      mutable_datalist();
  private:
  const ::datacapture::RawData& _internal_datalist(int index) const;
  ::datacapture::RawData* _internal_add_datalist();
  public:
  const ::datacapture::RawData& datalist(int index) const;
  ::datacapture::RawData* add_datalist();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::datacapture::RawData >&
      datalist() const;

  // string strErr = 6;
  void clear_strerr();
  const std::string& strerr() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_strerr(ArgT0&& arg0, ArgT... args);
  std::string* mutable_strerr();
  PROTOBUF_NODISCARD std::string* release_strerr();
  void set_allocated_strerr(std::string* strerr);
  private:
  const std::string& _internal_strerr() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_strerr(const std::string& value);
  std::string* _internal_mutable_strerr();
  public:

  // bool bIsSucceed = 1;
  void clear_bissucceed();
  bool bissucceed() const;
  void set_bissucceed(bool value);
  private:
  bool _internal_bissucceed() const;
  void _internal_set_bissucceed(bool value);
  public:

  // uint32 dwNowPage = 2;
  void clear_dwnowpage();
  uint32_t dwnowpage() const;
  void set_dwnowpage(uint32_t value);
  private:
  uint32_t _internal_dwnowpage() const;
  void _internal_set_dwnowpage(uint32_t value);
  public:

  // uint32 dwPageSize = 3;
  void clear_dwpagesize();
  uint32_t dwpagesize() const;
  void set_dwpagesize(uint32_t value);
  private:
  uint32_t _internal_dwpagesize() const;
  void _internal_set_dwpagesize(uint32_t value);
  public:

  // uint32 dwTotalPages = 4;
  void clear_dwtotalpages();
  uint32_t dwtotalpages() const;
  void set_dwtotalpages(uint32_t value);
  private:
  uint32_t _internal_dwtotalpages() const;
  void _internal_set_dwtotalpages(uint32_t value);
  public:

  // uint32 dwTotalDatas = 5;
  void clear_dwtotaldatas();
  uint32_t dwtotaldatas() const;
  void set_dwtotaldatas(uint32_t value);
  private:
  uint32_t _internal_dwtotaldatas() const;
  void _internal_set_dwtotaldatas(uint32_t value);
  public:

  // uint32 dwCnt = 7;
  void clear_dwcnt();
  uint32_t dwcnt() const;
  void set_dwcnt(uint32_t value);
  private:
  uint32_t _internal_dwcnt() const;
  void _internal_set_dwcnt(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:datacapture.DataQueryBatchesRes)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::datacapture::RawData > datalist_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr strerr_;
    bool bissucceed_;
    uint32_t dwnowpage_;
    uint32_t dwpagesize_;
    uint32_t dwtotalpages_;
    uint32_t dwtotaldatas_;
    uint32_t dwcnt_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_datacapture_2eCommunicate_2eproto;
};
// -------------------------------------------------------------------

class CompressReqList final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:datacapture.CompressReqList) */ {
 public:
  inline CompressReqList() : CompressReqList(nullptr) {}
  ~CompressReqList() override;
  explicit PROTOBUF_CONSTEXPR CompressReqList(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CompressReqList(const CompressReqList& from);
  CompressReqList(CompressReqList&& from) noexcept
    : CompressReqList() {
    *this = ::std::move(from);
  }

  inline CompressReqList& operator=(const CompressReqList& from) {
    CopyFrom(from);
    return *this;
  }
  inline CompressReqList& operator=(CompressReqList&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CompressReqList& default_instance() {
    return *internal_default_instance();
  }
  static inline const CompressReqList* internal_default_instance() {
    return reinterpret_cast<const CompressReqList*>(
               &_CompressReqList_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(CompressReqList& a, CompressReqList& b) {
    a.Swap(&b);
  }
  inline void Swap(CompressReqList* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CompressReqList* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CompressReqList* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CompressReqList>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CompressReqList& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CompressReqList& from) {
    CompressReqList::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CompressReqList* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "datacapture.CompressReqList";
  }
  protected:
  explicit CompressReqList(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kQueryListFieldNumber = 6,
    kStrHttpUrlFieldNumber = 3,
    kStrPackageNameFieldNumber = 4,
    kStrSerialNumFieldNumber = 5,
    kCompressTypeFieldNumber = 1,
    kDwCntFieldNumber = 2,
  };
  // repeated .datacapture.DataQuery queryList = 6;
  int querylist_size() const;
  private:
  int _internal_querylist_size() const;
  public:
  void clear_querylist();
  ::datacapture::DataQuery* mutable_querylist(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::datacapture::DataQuery >*
      mutable_querylist();
  private:
  const ::datacapture::DataQuery& _internal_querylist(int index) const;
  ::datacapture::DataQuery* _internal_add_querylist();
  public:
  const ::datacapture::DataQuery& querylist(int index) const;
  ::datacapture::DataQuery* add_querylist();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::datacapture::DataQuery >&
      querylist() const;

  // string strHttpUrl = 3;
  void clear_strhttpurl();
  const std::string& strhttpurl() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_strhttpurl(ArgT0&& arg0, ArgT... args);
  std::string* mutable_strhttpurl();
  PROTOBUF_NODISCARD std::string* release_strhttpurl();
  void set_allocated_strhttpurl(std::string* strhttpurl);
  private:
  const std::string& _internal_strhttpurl() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_strhttpurl(const std::string& value);
  std::string* _internal_mutable_strhttpurl();
  public:

  // string strPackageName = 4;
  void clear_strpackagename();
  const std::string& strpackagename() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_strpackagename(ArgT0&& arg0, ArgT... args);
  std::string* mutable_strpackagename();
  PROTOBUF_NODISCARD std::string* release_strpackagename();
  void set_allocated_strpackagename(std::string* strpackagename);
  private:
  const std::string& _internal_strpackagename() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_strpackagename(const std::string& value);
  std::string* _internal_mutable_strpackagename();
  public:

  // string strSerialNum = 5;
  void clear_strserialnum();
  const std::string& strserialnum() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_strserialnum(ArgT0&& arg0, ArgT... args);
  std::string* mutable_strserialnum();
  PROTOBUF_NODISCARD std::string* release_strserialnum();
  void set_allocated_strserialnum(std::string* strserialnum);
  private:
  const std::string& _internal_strserialnum() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_strserialnum(const std::string& value);
  std::string* _internal_mutable_strserialnum();
  public:

  // uint32 compressType = 1;
  void clear_compresstype();
  uint32_t compresstype() const;
  void set_compresstype(uint32_t value);
  private:
  uint32_t _internal_compresstype() const;
  void _internal_set_compresstype(uint32_t value);
  public:

  // uint32 dwCnt = 2;
  void clear_dwcnt();
  uint32_t dwcnt() const;
  void set_dwcnt(uint32_t value);
  private:
  uint32_t _internal_dwcnt() const;
  void _internal_set_dwcnt(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:datacapture.CompressReqList)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::datacapture::DataQuery > querylist_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr strhttpurl_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr strpackagename_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr strserialnum_;
    uint32_t compresstype_;
    uint32_t dwcnt_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_datacapture_2eCommunicate_2eproto;
};
// -------------------------------------------------------------------

class CompressRes final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:datacapture.CompressRes) */ {
 public:
  inline CompressRes() : CompressRes(nullptr) {}
  ~CompressRes() override;
  explicit PROTOBUF_CONSTEXPR CompressRes(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CompressRes(const CompressRes& from);
  CompressRes(CompressRes&& from) noexcept
    : CompressRes() {
    *this = ::std::move(from);
  }

  inline CompressRes& operator=(const CompressRes& from) {
    CopyFrom(from);
    return *this;
  }
  inline CompressRes& operator=(CompressRes&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CompressRes& default_instance() {
    return *internal_default_instance();
  }
  static inline const CompressRes* internal_default_instance() {
    return reinterpret_cast<const CompressRes*>(
               &_CompressRes_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(CompressRes& a, CompressRes& b) {
    a.Swap(&b);
  }
  inline void Swap(CompressRes* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CompressRes* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CompressRes* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CompressRes>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CompressRes& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CompressRes& from) {
    CompressRes::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CompressRes* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "datacapture.CompressRes";
  }
  protected:
  explicit CompressRes(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFilepathFieldNumber = 3,
    kStrErrFieldNumber = 4,
    kStrSerialNumFieldNumber = 5,
    kStrPackageNameFieldNumber = 6,
    kCompressTypeFieldNumber = 1,
    kBIsSucceedFieldNumber = 2,
  };
  // string filepath = 3;
  void clear_filepath();
  const std::string& filepath() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_filepath(ArgT0&& arg0, ArgT... args);
  std::string* mutable_filepath();
  PROTOBUF_NODISCARD std::string* release_filepath();
  void set_allocated_filepath(std::string* filepath);
  private:
  const std::string& _internal_filepath() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_filepath(const std::string& value);
  std::string* _internal_mutable_filepath();
  public:

  // string strErr = 4;
  void clear_strerr();
  const std::string& strerr() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_strerr(ArgT0&& arg0, ArgT... args);
  std::string* mutable_strerr();
  PROTOBUF_NODISCARD std::string* release_strerr();
  void set_allocated_strerr(std::string* strerr);
  private:
  const std::string& _internal_strerr() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_strerr(const std::string& value);
  std::string* _internal_mutable_strerr();
  public:

  // string strSerialNum = 5;
  void clear_strserialnum();
  const std::string& strserialnum() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_strserialnum(ArgT0&& arg0, ArgT... args);
  std::string* mutable_strserialnum();
  PROTOBUF_NODISCARD std::string* release_strserialnum();
  void set_allocated_strserialnum(std::string* strserialnum);
  private:
  const std::string& _internal_strserialnum() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_strserialnum(const std::string& value);
  std::string* _internal_mutable_strserialnum();
  public:

  // string strPackageName = 6;
  void clear_strpackagename();
  const std::string& strpackagename() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_strpackagename(ArgT0&& arg0, ArgT... args);
  std::string* mutable_strpackagename();
  PROTOBUF_NODISCARD std::string* release_strpackagename();
  void set_allocated_strpackagename(std::string* strpackagename);
  private:
  const std::string& _internal_strpackagename() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_strpackagename(const std::string& value);
  std::string* _internal_mutable_strpackagename();
  public:

  // uint32 compressType = 1;
  void clear_compresstype();
  uint32_t compresstype() const;
  void set_compresstype(uint32_t value);
  private:
  uint32_t _internal_compresstype() const;
  void _internal_set_compresstype(uint32_t value);
  public:

  // bool bIsSucceed = 2;
  void clear_bissucceed();
  bool bissucceed() const;
  void set_bissucceed(bool value);
  private:
  bool _internal_bissucceed() const;
  void _internal_set_bissucceed(bool value);
  public:

  // @@protoc_insertion_point(class_scope:datacapture.CompressRes)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr filepath_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr strerr_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr strserialnum_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr strpackagename_;
    uint32_t compresstype_;
    bool bissucceed_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_datacapture_2eCommunicate_2eproto;
};
// -------------------------------------------------------------------

class ConnectArgs final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:datacapture.ConnectArgs) */ {
 public:
  inline ConnectArgs() : ConnectArgs(nullptr) {}
  ~ConnectArgs() override;
  explicit PROTOBUF_CONSTEXPR ConnectArgs(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ConnectArgs(const ConnectArgs& from);
  ConnectArgs(ConnectArgs&& from) noexcept
    : ConnectArgs() {
    *this = ::std::move(from);
  }

  inline ConnectArgs& operator=(const ConnectArgs& from) {
    CopyFrom(from);
    return *this;
  }
  inline ConnectArgs& operator=(ConnectArgs&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ConnectArgs& default_instance() {
    return *internal_default_instance();
  }
  static inline const ConnectArgs* internal_default_instance() {
    return reinterpret_cast<const ConnectArgs*>(
               &_ConnectArgs_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(ConnectArgs& a, ConnectArgs& b) {
    a.Swap(&b);
  }
  inline void Swap(ConnectArgs* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ConnectArgs* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ConnectArgs* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ConnectArgs>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ConnectArgs& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ConnectArgs& from) {
    ConnectArgs::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ConnectArgs* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "datacapture.ConnectArgs";
  }
  protected:
  explicit ConnectArgs(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef ConnectArgs_ConnectType ConnectType;
  static constexpr ConnectType PROTO_MQTT =
    ConnectArgs_ConnectType_PROTO_MQTT;
  static constexpr ConnectType PROTO_HTTP =
    ConnectArgs_ConnectType_PROTO_HTTP;
  static constexpr ConnectType PROTO_KAFKA =
    ConnectArgs_ConnectType_PROTO_KAFKA;
  static constexpr ConnectType PROTO_TCP_SERVER =
    ConnectArgs_ConnectType_PROTO_TCP_SERVER;
  static inline bool ConnectType_IsValid(int value) {
    return ConnectArgs_ConnectType_IsValid(value);
  }
  static constexpr ConnectType ConnectType_MIN =
    ConnectArgs_ConnectType_ConnectType_MIN;
  static constexpr ConnectType ConnectType_MAX =
    ConnectArgs_ConnectType_ConnectType_MAX;
  static constexpr int ConnectType_ARRAYSIZE =
    ConnectArgs_ConnectType_ConnectType_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  ConnectType_descriptor() {
    return ConnectArgs_ConnectType_descriptor();
  }
  template<typename T>
  static inline const std::string& ConnectType_Name(T enum_t_value) {
    static_assert(::std::is_same<T, ConnectType>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function ConnectType_Name.");
    return ConnectArgs_ConnectType_Name(enum_t_value);
  }
  static inline bool ConnectType_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      ConnectType* value) {
    return ConnectArgs_ConnectType_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kStrCrossroadIdFieldNumber = 4,
    kStrTopiceFieldNumber = 5,
    kStrPasswordFieldNumber = 6,
    kStrClientIdFieldNumber = 7,
    kStrAddrFieldNumber = 8,
    kStrUsernameFieldNumber = 9,
    kStrDescribeFieldNumber = 10,
    kDwNoFieldNumber = 1,
    kIsEnableFieldNumber = 2,
    kTypeFieldNumber = 3,
    kDwFactoryFieldNumber = 11,
  };
  // string strCrossroadId = 4;
  void clear_strcrossroadid();
  const std::string& strcrossroadid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_strcrossroadid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_strcrossroadid();
  PROTOBUF_NODISCARD std::string* release_strcrossroadid();
  void set_allocated_strcrossroadid(std::string* strcrossroadid);
  private:
  const std::string& _internal_strcrossroadid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_strcrossroadid(const std::string& value);
  std::string* _internal_mutable_strcrossroadid();
  public:

  // string strTopice = 5;
  void clear_strtopice();
  const std::string& strtopice() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_strtopice(ArgT0&& arg0, ArgT... args);
  std::string* mutable_strtopice();
  PROTOBUF_NODISCARD std::string* release_strtopice();
  void set_allocated_strtopice(std::string* strtopice);
  private:
  const std::string& _internal_strtopice() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_strtopice(const std::string& value);
  std::string* _internal_mutable_strtopice();
  public:

  // string strPassword = 6;
  void clear_strpassword();
  const std::string& strpassword() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_strpassword(ArgT0&& arg0, ArgT... args);
  std::string* mutable_strpassword();
  PROTOBUF_NODISCARD std::string* release_strpassword();
  void set_allocated_strpassword(std::string* strpassword);
  private:
  const std::string& _internal_strpassword() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_strpassword(const std::string& value);
  std::string* _internal_mutable_strpassword();
  public:

  // string strClientId = 7;
  void clear_strclientid();
  const std::string& strclientid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_strclientid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_strclientid();
  PROTOBUF_NODISCARD std::string* release_strclientid();
  void set_allocated_strclientid(std::string* strclientid);
  private:
  const std::string& _internal_strclientid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_strclientid(const std::string& value);
  std::string* _internal_mutable_strclientid();
  public:

  // string strAddr = 8;
  void clear_straddr();
  const std::string& straddr() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_straddr(ArgT0&& arg0, ArgT... args);
  std::string* mutable_straddr();
  PROTOBUF_NODISCARD std::string* release_straddr();
  void set_allocated_straddr(std::string* straddr);
  private:
  const std::string& _internal_straddr() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_straddr(const std::string& value);
  std::string* _internal_mutable_straddr();
  public:

  // string strUsername = 9;
  void clear_strusername();
  const std::string& strusername() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_strusername(ArgT0&& arg0, ArgT... args);
  std::string* mutable_strusername();
  PROTOBUF_NODISCARD std::string* release_strusername();
  void set_allocated_strusername(std::string* strusername);
  private:
  const std::string& _internal_strusername() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_strusername(const std::string& value);
  std::string* _internal_mutable_strusername();
  public:

  // string strDescribe = 10;
  void clear_strdescribe();
  const std::string& strdescribe() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_strdescribe(ArgT0&& arg0, ArgT... args);
  std::string* mutable_strdescribe();
  PROTOBUF_NODISCARD std::string* release_strdescribe();
  void set_allocated_strdescribe(std::string* strdescribe);
  private:
  const std::string& _internal_strdescribe() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_strdescribe(const std::string& value);
  std::string* _internal_mutable_strdescribe();
  public:

  // uint32 dwNo = 1;
  void clear_dwno();
  uint32_t dwno() const;
  void set_dwno(uint32_t value);
  private:
  uint32_t _internal_dwno() const;
  void _internal_set_dwno(uint32_t value);
  public:

  // bool isEnable = 2;
  void clear_isenable();
  bool isenable() const;
  void set_isenable(bool value);
  private:
  bool _internal_isenable() const;
  void _internal_set_isenable(bool value);
  public:

  // .datacapture.ConnectArgs.ConnectType type = 3;
  void clear_type();
  ::datacapture::ConnectArgs_ConnectType type() const;
  void set_type(::datacapture::ConnectArgs_ConnectType value);
  private:
  ::datacapture::ConnectArgs_ConnectType _internal_type() const;
  void _internal_set_type(::datacapture::ConnectArgs_ConnectType value);
  public:

  // uint32 dwFactory = 11;
  void clear_dwfactory();
  uint32_t dwfactory() const;
  void set_dwfactory(uint32_t value);
  private:
  uint32_t _internal_dwfactory() const;
  void _internal_set_dwfactory(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:datacapture.ConnectArgs)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr strcrossroadid_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr strtopice_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr strpassword_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr strclientid_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr straddr_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr strusername_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr strdescribe_;
    uint32_t dwno_;
    bool isenable_;
    int type_;
    uint32_t dwfactory_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_datacapture_2eCommunicate_2eproto;
};
// -------------------------------------------------------------------

class ArgsList final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:datacapture.ArgsList) */ {
 public:
  inline ArgsList() : ArgsList(nullptr) {}
  ~ArgsList() override;
  explicit PROTOBUF_CONSTEXPR ArgsList(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ArgsList(const ArgsList& from);
  ArgsList(ArgsList&& from) noexcept
    : ArgsList() {
    *this = ::std::move(from);
  }

  inline ArgsList& operator=(const ArgsList& from) {
    CopyFrom(from);
    return *this;
  }
  inline ArgsList& operator=(ArgsList&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ArgsList& default_instance() {
    return *internal_default_instance();
  }
  static inline const ArgsList* internal_default_instance() {
    return reinterpret_cast<const ArgsList*>(
               &_ArgsList_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(ArgsList& a, ArgsList& b) {
    a.Swap(&b);
  }
  inline void Swap(ArgsList* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ArgsList* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ArgsList* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ArgsList>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ArgsList& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ArgsList& from) {
    ArgsList::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ArgsList* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "datacapture.ArgsList";
  }
  protected:
  explicit ArgsList(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kArgsListFieldNumber = 2,
    kDwCntFieldNumber = 1,
  };
  // repeated .datacapture.ConnectArgs argsList = 2;
  int argslist_size() const;
  private:
  int _internal_argslist_size() const;
  public:
  void clear_argslist();
  ::datacapture::ConnectArgs* mutable_argslist(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::datacapture::ConnectArgs >*
      mutable_argslist();
  private:
  const ::datacapture::ConnectArgs& _internal_argslist(int index) const;
  ::datacapture::ConnectArgs* _internal_add_argslist();
  public:
  const ::datacapture::ConnectArgs& argslist(int index) const;
  ::datacapture::ConnectArgs* add_argslist();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::datacapture::ConnectArgs >&
      argslist() const;

  // uint32 dwCnt = 1;
  void clear_dwcnt();
  uint32_t dwcnt() const;
  void set_dwcnt(uint32_t value);
  private:
  uint32_t _internal_dwcnt() const;
  void _internal_set_dwcnt(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:datacapture.ArgsList)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::datacapture::ConnectArgs > argslist_;
    uint32_t dwcnt_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_datacapture_2eCommunicate_2eproto;
};
// -------------------------------------------------------------------

class CommonCMD final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:datacapture.CommonCMD) */ {
 public:
  inline CommonCMD() : CommonCMD(nullptr) {}
  ~CommonCMD() override;
  explicit PROTOBUF_CONSTEXPR CommonCMD(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CommonCMD(const CommonCMD& from);
  CommonCMD(CommonCMD&& from) noexcept
    : CommonCMD() {
    *this = ::std::move(from);
  }

  inline CommonCMD& operator=(const CommonCMD& from) {
    CopyFrom(from);
    return *this;
  }
  inline CommonCMD& operator=(CommonCMD&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CommonCMD& default_instance() {
    return *internal_default_instance();
  }
  static inline const CommonCMD* internal_default_instance() {
    return reinterpret_cast<const CommonCMD*>(
               &_CommonCMD_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(CommonCMD& a, CommonCMD& b) {
    a.Swap(&b);
  }
  inline void Swap(CommonCMD* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CommonCMD* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CommonCMD* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CommonCMD>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CommonCMD& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CommonCMD& from) {
    CommonCMD::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CommonCMD* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "datacapture.CommonCMD";
  }
  protected:
  explicit CommonCMD(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef CommonCMD_cmdType cmdType;
  static constexpr cmdType PROTO_SYSTEM_INIT =
    CommonCMD_cmdType_PROTO_SYSTEM_INIT;
  static constexpr cmdType PROTO_SYSTEM_START =
    CommonCMD_cmdType_PROTO_SYSTEM_START;
  static constexpr cmdType PROTO_SYSTEM_STOP =
    CommonCMD_cmdType_PROTO_SYSTEM_STOP;
  static constexpr cmdType PROTO_SYSTEM_PAUSE =
    CommonCMD_cmdType_PROTO_SYSTEM_PAUSE;
  static inline bool cmdType_IsValid(int value) {
    return CommonCMD_cmdType_IsValid(value);
  }
  static constexpr cmdType cmdType_MIN =
    CommonCMD_cmdType_cmdType_MIN;
  static constexpr cmdType cmdType_MAX =
    CommonCMD_cmdType_cmdType_MAX;
  static constexpr int cmdType_ARRAYSIZE =
    CommonCMD_cmdType_cmdType_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  cmdType_descriptor() {
    return CommonCMD_cmdType_descriptor();
  }
  template<typename T>
  static inline const std::string& cmdType_Name(T enum_t_value) {
    static_assert(::std::is_same<T, cmdType>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function cmdType_Name.");
    return CommonCMD_cmdType_Name(enum_t_value);
  }
  static inline bool cmdType_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      cmdType* value) {
    return CommonCMD_cmdType_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kTypeFieldNumber = 1,
  };
  // .datacapture.CommonCMD.cmdType type = 1;
  void clear_type();
  ::datacapture::CommonCMD_cmdType type() const;
  void set_type(::datacapture::CommonCMD_cmdType value);
  private:
  ::datacapture::CommonCMD_cmdType _internal_type() const;
  void _internal_set_type(::datacapture::CommonCMD_cmdType value);
  public:

  // @@protoc_insertion_point(class_scope:datacapture.CommonCMD)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int type_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_datacapture_2eCommunicate_2eproto;
};
// -------------------------------------------------------------------

class RoadInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:datacapture.RoadInfo) */ {
 public:
  inline RoadInfo() : RoadInfo(nullptr) {}
  ~RoadInfo() override;
  explicit PROTOBUF_CONSTEXPR RoadInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RoadInfo(const RoadInfo& from);
  RoadInfo(RoadInfo&& from) noexcept
    : RoadInfo() {
    *this = ::std::move(from);
  }

  inline RoadInfo& operator=(const RoadInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline RoadInfo& operator=(RoadInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RoadInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const RoadInfo* internal_default_instance() {
    return reinterpret_cast<const RoadInfo*>(
               &_RoadInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(RoadInfo& a, RoadInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(RoadInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RoadInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RoadInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RoadInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RoadInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RoadInfo& from) {
    RoadInfo::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RoadInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "datacapture.RoadInfo";
  }
  protected:
  explicit RoadInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStrIDFieldNumber = 1,
    kStrDescribeFieldNumber = 2,
  };
  // string strID = 1;
  void clear_strid();
  const std::string& strid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_strid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_strid();
  PROTOBUF_NODISCARD std::string* release_strid();
  void set_allocated_strid(std::string* strid);
  private:
  const std::string& _internal_strid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_strid(const std::string& value);
  std::string* _internal_mutable_strid();
  public:

  // string strDescribe = 2;
  void clear_strdescribe();
  const std::string& strdescribe() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_strdescribe(ArgT0&& arg0, ArgT... args);
  std::string* mutable_strdescribe();
  PROTOBUF_NODISCARD std::string* release_strdescribe();
  void set_allocated_strdescribe(std::string* strdescribe);
  private:
  const std::string& _internal_strdescribe() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_strdescribe(const std::string& value);
  std::string* _internal_mutable_strdescribe();
  public:

  // @@protoc_insertion_point(class_scope:datacapture.RoadInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr strid_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr strdescribe_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_datacapture_2eCommunicate_2eproto;
};
// -------------------------------------------------------------------

class RoadInfoList final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:datacapture.RoadInfoList) */ {
 public:
  inline RoadInfoList() : RoadInfoList(nullptr) {}
  ~RoadInfoList() override;
  explicit PROTOBUF_CONSTEXPR RoadInfoList(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RoadInfoList(const RoadInfoList& from);
  RoadInfoList(RoadInfoList&& from) noexcept
    : RoadInfoList() {
    *this = ::std::move(from);
  }

  inline RoadInfoList& operator=(const RoadInfoList& from) {
    CopyFrom(from);
    return *this;
  }
  inline RoadInfoList& operator=(RoadInfoList&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RoadInfoList& default_instance() {
    return *internal_default_instance();
  }
  static inline const RoadInfoList* internal_default_instance() {
    return reinterpret_cast<const RoadInfoList*>(
               &_RoadInfoList_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  friend void swap(RoadInfoList& a, RoadInfoList& b) {
    a.Swap(&b);
  }
  inline void Swap(RoadInfoList* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RoadInfoList* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RoadInfoList* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RoadInfoList>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RoadInfoList& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RoadInfoList& from) {
    RoadInfoList::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RoadInfoList* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "datacapture.RoadInfoList";
  }
  protected:
  explicit RoadInfoList(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kListFieldNumber = 2,
    kDwCntFieldNumber = 1,
  };
  // repeated .datacapture.RoadInfo list = 2;
  int list_size() const;
  private:
  int _internal_list_size() const;
  public:
  void clear_list();
  ::datacapture::RoadInfo* mutable_list(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::datacapture::RoadInfo >*
      mutable_list();
  private:
  const ::datacapture::RoadInfo& _internal_list(int index) const;
  ::datacapture::RoadInfo* _internal_add_list();
  public:
  const ::datacapture::RoadInfo& list(int index) const;
  ::datacapture::RoadInfo* add_list();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::datacapture::RoadInfo >&
      list() const;

  // uint32 dwCnt = 1;
  void clear_dwcnt();
  uint32_t dwcnt() const;
  void set_dwcnt(uint32_t value);
  private:
  uint32_t _internal_dwcnt() const;
  void _internal_set_dwcnt(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:datacapture.RoadInfoList)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::datacapture::RoadInfo > list_;
    uint32_t dwcnt_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_datacapture_2eCommunicate_2eproto;
};
// -------------------------------------------------------------------

class SystemStatus final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:datacapture.SystemStatus) */ {
 public:
  inline SystemStatus() : SystemStatus(nullptr) {}
  ~SystemStatus() override;
  explicit PROTOBUF_CONSTEXPR SystemStatus(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SystemStatus(const SystemStatus& from);
  SystemStatus(SystemStatus&& from) noexcept
    : SystemStatus() {
    *this = ::std::move(from);
  }

  inline SystemStatus& operator=(const SystemStatus& from) {
    CopyFrom(from);
    return *this;
  }
  inline SystemStatus& operator=(SystemStatus&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SystemStatus& default_instance() {
    return *internal_default_instance();
  }
  static inline const SystemStatus* internal_default_instance() {
    return reinterpret_cast<const SystemStatus*>(
               &_SystemStatus_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  friend void swap(SystemStatus& a, SystemStatus& b) {
    a.Swap(&b);
  }
  inline void Swap(SystemStatus* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SystemStatus* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SystemStatus* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SystemStatus>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SystemStatus& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SystemStatus& from) {
    SystemStatus::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SystemStatus* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "datacapture.SystemStatus";
  }
  protected:
  explicit SystemStatus(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef SystemStatus_statusType statusType;
  static constexpr statusType START =
    SystemStatus_statusType_START;
  static constexpr statusType STOP =
    SystemStatus_statusType_STOP;
  static constexpr statusType PAUSE =
    SystemStatus_statusType_PAUSE;
  static inline bool statusType_IsValid(int value) {
    return SystemStatus_statusType_IsValid(value);
  }
  static constexpr statusType statusType_MIN =
    SystemStatus_statusType_statusType_MIN;
  static constexpr statusType statusType_MAX =
    SystemStatus_statusType_statusType_MAX;
  static constexpr int statusType_ARRAYSIZE =
    SystemStatus_statusType_statusType_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  statusType_descriptor() {
    return SystemStatus_statusType_descriptor();
  }
  template<typename T>
  static inline const std::string& statusType_Name(T enum_t_value) {
    static_assert(::std::is_same<T, statusType>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function statusType_Name.");
    return SystemStatus_statusType_Name(enum_t_value);
  }
  static inline bool statusType_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      statusType* value) {
    return SystemStatus_statusType_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kLlTimestampFieldNumber = 1,
    kTypeFieldNumber = 2,
  };
  // uint64 llTimestamp = 1;
  void clear_lltimestamp();
  uint64_t lltimestamp() const;
  void set_lltimestamp(uint64_t value);
  private:
  uint64_t _internal_lltimestamp() const;
  void _internal_set_lltimestamp(uint64_t value);
  public:

  // .datacapture.SystemStatus.statusType type = 2;
  void clear_type();
  ::datacapture::SystemStatus_statusType type() const;
  void set_type(::datacapture::SystemStatus_statusType value);
  private:
  ::datacapture::SystemStatus_statusType _internal_type() const;
  void _internal_set_type(::datacapture::SystemStatus_statusType value);
  public:

  // @@protoc_insertion_point(class_scope:datacapture.SystemStatus)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    uint64_t lltimestamp_;
    int type_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_datacapture_2eCommunicate_2eproto;
};
// -------------------------------------------------------------------

class SystemInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:datacapture.SystemInfo) */ {
 public:
  inline SystemInfo() : SystemInfo(nullptr) {}
  ~SystemInfo() override;
  explicit PROTOBUF_CONSTEXPR SystemInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SystemInfo(const SystemInfo& from);
  SystemInfo(SystemInfo&& from) noexcept
    : SystemInfo() {
    *this = ::std::move(from);
  }

  inline SystemInfo& operator=(const SystemInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline SystemInfo& operator=(SystemInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SystemInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const SystemInfo* internal_default_instance() {
    return reinterpret_cast<const SystemInfo*>(
               &_SystemInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    16;

  friend void swap(SystemInfo& a, SystemInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(SystemInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SystemInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SystemInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SystemInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SystemInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SystemInfo& from) {
    SystemInfo::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SystemInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "datacapture.SystemInfo";
  }
  protected:
  explicit SystemInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCpuUsedFieldNumber = 1,
    kCpuTempFieldNumber = 2,
    kCpuFreqFieldNumber = 3,
    kCpuCoreNumFieldNumber = 4,
    kMemoryUsedFieldNumber = 5,
    kMemoryTotalFieldNumber = 6,
    kDiskUsedFieldNumber = 7,
  };
  // float cpuUsed = 1;
  void clear_cpuused();
  float cpuused() const;
  void set_cpuused(float value);
  private:
  float _internal_cpuused() const;
  void _internal_set_cpuused(float value);
  public:

  // float cpuTemp = 2;
  void clear_cputemp();
  float cputemp() const;
  void set_cputemp(float value);
  private:
  float _internal_cputemp() const;
  void _internal_set_cputemp(float value);
  public:

  // float cpuFreq = 3;
  void clear_cpufreq();
  float cpufreq() const;
  void set_cpufreq(float value);
  private:
  float _internal_cpufreq() const;
  void _internal_set_cpufreq(float value);
  public:

  // float cpuCoreNum = 4;
  void clear_cpucorenum();
  float cpucorenum() const;
  void set_cpucorenum(float value);
  private:
  float _internal_cpucorenum() const;
  void _internal_set_cpucorenum(float value);
  public:

  // float memoryUsed = 5;
  void clear_memoryused();
  float memoryused() const;
  void set_memoryused(float value);
  private:
  float _internal_memoryused() const;
  void _internal_set_memoryused(float value);
  public:

  // float memoryTotal = 6;
  void clear_memorytotal();
  float memorytotal() const;
  void set_memorytotal(float value);
  private:
  float _internal_memorytotal() const;
  void _internal_set_memorytotal(float value);
  public:

  // float diskUsed = 7;
  void clear_diskused();
  float diskused() const;
  void set_diskused(float value);
  private:
  float _internal_diskused() const;
  void _internal_set_diskused(float value);
  public:

  // @@protoc_insertion_point(class_scope:datacapture.SystemInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    float cpuused_;
    float cputemp_;
    float cpufreq_;
    float cpucorenum_;
    float memoryused_;
    float memorytotal_;
    float diskused_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_datacapture_2eCommunicate_2eproto;
};
// -------------------------------------------------------------------

class ClientInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:datacapture.ClientInfo) */ {
 public:
  inline ClientInfo() : ClientInfo(nullptr) {}
  ~ClientInfo() override;
  explicit PROTOBUF_CONSTEXPR ClientInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ClientInfo(const ClientInfo& from);
  ClientInfo(ClientInfo&& from) noexcept
    : ClientInfo() {
    *this = ::std::move(from);
  }

  inline ClientInfo& operator=(const ClientInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline ClientInfo& operator=(ClientInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ClientInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const ClientInfo* internal_default_instance() {
    return reinterpret_cast<const ClientInfo*>(
               &_ClientInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    17;

  friend void swap(ClientInfo& a, ClientInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(ClientInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ClientInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ClientInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ClientInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ClientInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ClientInfo& from) {
    ClientInfo::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ClientInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "datacapture.ClientInfo";
  }
  protected:
  explicit ClientInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIpFieldNumber = 1,
    kPortFieldNumber = 2,
  };
  // string ip = 1;
  void clear_ip();
  const std::string& ip() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_ip(ArgT0&& arg0, ArgT... args);
  std::string* mutable_ip();
  PROTOBUF_NODISCARD std::string* release_ip();
  void set_allocated_ip(std::string* ip);
  private:
  const std::string& _internal_ip() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_ip(const std::string& value);
  std::string* _internal_mutable_ip();
  public:

  // uint32 port = 2;
  void clear_port();
  uint32_t port() const;
  void set_port(uint32_t value);
  private:
  uint32_t _internal_port() const;
  void _internal_set_port(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:datacapture.ClientInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr ip_;
    uint32_t port_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_datacapture_2eCommunicate_2eproto;
};
// -------------------------------------------------------------------

class NetConfig final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:datacapture.NetConfig) */ {
 public:
  inline NetConfig() : NetConfig(nullptr) {}
  ~NetConfig() override;
  explicit PROTOBUF_CONSTEXPR NetConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  NetConfig(const NetConfig& from);
  NetConfig(NetConfig&& from) noexcept
    : NetConfig() {
    *this = ::std::move(from);
  }

  inline NetConfig& operator=(const NetConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline NetConfig& operator=(NetConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const NetConfig& default_instance() {
    return *internal_default_instance();
  }
  static inline const NetConfig* internal_default_instance() {
    return reinterpret_cast<const NetConfig*>(
               &_NetConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    18;

  friend void swap(NetConfig& a, NetConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(NetConfig* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(NetConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  NetConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<NetConfig>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const NetConfig& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const NetConfig& from) {
    NetConfig::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(NetConfig* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "datacapture.NetConfig";
  }
  protected:
  explicit NetConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLocalIpFieldNumber = 1,
    kMaskFieldNumber = 2,
    kGatewayFieldNumber = 3,
    kTargetIpFieldNumber = 4,
    kRouteFieldNumber = 5,
  };
  // string localIp = 1;
  void clear_localip();
  const std::string& localip() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_localip(ArgT0&& arg0, ArgT... args);
  std::string* mutable_localip();
  PROTOBUF_NODISCARD std::string* release_localip();
  void set_allocated_localip(std::string* localip);
  private:
  const std::string& _internal_localip() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_localip(const std::string& value);
  std::string* _internal_mutable_localip();
  public:

  // string mask = 2;
  void clear_mask();
  const std::string& mask() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_mask(ArgT0&& arg0, ArgT... args);
  std::string* mutable_mask();
  PROTOBUF_NODISCARD std::string* release_mask();
  void set_allocated_mask(std::string* mask);
  private:
  const std::string& _internal_mask() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_mask(const std::string& value);
  std::string* _internal_mutable_mask();
  public:

  // string gateway = 3;
  void clear_gateway();
  const std::string& gateway() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_gateway(ArgT0&& arg0, ArgT... args);
  std::string* mutable_gateway();
  PROTOBUF_NODISCARD std::string* release_gateway();
  void set_allocated_gateway(std::string* gateway);
  private:
  const std::string& _internal_gateway() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_gateway(const std::string& value);
  std::string* _internal_mutable_gateway();
  public:

  // string targetIp = 4;
  void clear_targetip();
  const std::string& targetip() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_targetip(ArgT0&& arg0, ArgT... args);
  std::string* mutable_targetip();
  PROTOBUF_NODISCARD std::string* release_targetip();
  void set_allocated_targetip(std::string* targetip);
  private:
  const std::string& _internal_targetip() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_targetip(const std::string& value);
  std::string* _internal_mutable_targetip();
  public:

  // string route = 5;
  void clear_route();
  const std::string& route() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_route(ArgT0&& arg0, ArgT... args);
  std::string* mutable_route();
  PROTOBUF_NODISCARD std::string* release_route();
  void set_allocated_route(std::string* route);
  private:
  const std::string& _internal_route() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_route(const std::string& value);
  std::string* _internal_mutable_route();
  public:

  // @@protoc_insertion_point(class_scope:datacapture.NetConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr localip_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr mask_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr gateway_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr targetip_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr route_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_datacapture_2eCommunicate_2eproto;
};
// -------------------------------------------------------------------

class storeInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:datacapture.storeInfo) */ {
 public:
  inline storeInfo() : storeInfo(nullptr) {}
  ~storeInfo() override;
  explicit PROTOBUF_CONSTEXPR storeInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  storeInfo(const storeInfo& from);
  storeInfo(storeInfo&& from) noexcept
    : storeInfo() {
    *this = ::std::move(from);
  }

  inline storeInfo& operator=(const storeInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline storeInfo& operator=(storeInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const storeInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const storeInfo* internal_default_instance() {
    return reinterpret_cast<const storeInfo*>(
               &_storeInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    19;

  friend void swap(storeInfo& a, storeInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(storeInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(storeInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  storeInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<storeInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const storeInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const storeInfo& from) {
    storeInfo::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(storeInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "datacapture.storeInfo";
  }
  protected:
  explicit storeInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStorePathFieldNumber = 1,
  };
  // string storePath = 1;
  void clear_storepath();
  const std::string& storepath() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_storepath(ArgT0&& arg0, ArgT... args);
  std::string* mutable_storepath();
  PROTOBUF_NODISCARD std::string* release_storepath();
  void set_allocated_storepath(std::string* storepath);
  private:
  const std::string& _internal_storepath() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_storepath(const std::string& value);
  std::string* _internal_mutable_storepath();
  public:

  // @@protoc_insertion_point(class_scope:datacapture.storeInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr storepath_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_datacapture_2eCommunicate_2eproto;
};
// -------------------------------------------------------------------

class SystemCmd final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:datacapture.SystemCmd) */ {
 public:
  inline SystemCmd() : SystemCmd(nullptr) {}
  ~SystemCmd() override;
  explicit PROTOBUF_CONSTEXPR SystemCmd(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SystemCmd(const SystemCmd& from);
  SystemCmd(SystemCmd&& from) noexcept
    : SystemCmd() {
    *this = ::std::move(from);
  }

  inline SystemCmd& operator=(const SystemCmd& from) {
    CopyFrom(from);
    return *this;
  }
  inline SystemCmd& operator=(SystemCmd&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SystemCmd& default_instance() {
    return *internal_default_instance();
  }
  static inline const SystemCmd* internal_default_instance() {
    return reinterpret_cast<const SystemCmd*>(
               &_SystemCmd_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    20;

  friend void swap(SystemCmd& a, SystemCmd& b) {
    a.Swap(&b);
  }
  inline void Swap(SystemCmd* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SystemCmd* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SystemCmd* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SystemCmd>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SystemCmd& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SystemCmd& from) {
    SystemCmd::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SystemCmd* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "datacapture.SystemCmd";
  }
  protected:
  explicit SystemCmd(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef SystemCmd_statusType statusType;
  static constexpr statusType RECONNECTSERVER =
    SystemCmd_statusType_RECONNECTSERVER;
  static constexpr statusType RESTARTSERVER =
    SystemCmd_statusType_RESTARTSERVER;
  static constexpr statusType REBOOT =
    SystemCmd_statusType_REBOOT;
  static constexpr statusType NOCMD =
    SystemCmd_statusType_NOCMD;
  static inline bool statusType_IsValid(int value) {
    return SystemCmd_statusType_IsValid(value);
  }
  static constexpr statusType statusType_MIN =
    SystemCmd_statusType_statusType_MIN;
  static constexpr statusType statusType_MAX =
    SystemCmd_statusType_statusType_MAX;
  static constexpr int statusType_ARRAYSIZE =
    SystemCmd_statusType_statusType_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  statusType_descriptor() {
    return SystemCmd_statusType_descriptor();
  }
  template<typename T>
  static inline const std::string& statusType_Name(T enum_t_value) {
    static_assert(::std::is_same<T, statusType>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function statusType_Name.");
    return SystemCmd_statusType_Name(enum_t_value);
  }
  static inline bool statusType_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      statusType* value) {
    return SystemCmd_statusType_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kPortFieldNumber = 1,
    kTypeFieldNumber = 2,
  };
  // uint32 port = 1;
  void clear_port();
  uint32_t port() const;
  void set_port(uint32_t value);
  private:
  uint32_t _internal_port() const;
  void _internal_set_port(uint32_t value);
  public:

  // .datacapture.SystemCmd.statusType type = 2;
  void clear_type();
  ::datacapture::SystemCmd_statusType type() const;
  void set_type(::datacapture::SystemCmd_statusType value);
  private:
  ::datacapture::SystemCmd_statusType _internal_type() const;
  void _internal_set_type(::datacapture::SystemCmd_statusType value);
  public:

  // @@protoc_insertion_point(class_scope:datacapture.SystemCmd)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    uint32_t port_;
    int type_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_datacapture_2eCommunicate_2eproto;
};
// -------------------------------------------------------------------

class SystemLog final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:datacapture.SystemLog) */ {
 public:
  inline SystemLog() : SystemLog(nullptr) {}
  ~SystemLog() override;
  explicit PROTOBUF_CONSTEXPR SystemLog(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SystemLog(const SystemLog& from);
  SystemLog(SystemLog&& from) noexcept
    : SystemLog() {
    *this = ::std::move(from);
  }

  inline SystemLog& operator=(const SystemLog& from) {
    CopyFrom(from);
    return *this;
  }
  inline SystemLog& operator=(SystemLog&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SystemLog& default_instance() {
    return *internal_default_instance();
  }
  static inline const SystemLog* internal_default_instance() {
    return reinterpret_cast<const SystemLog*>(
               &_SystemLog_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    21;

  friend void swap(SystemLog& a, SystemLog& b) {
    a.Swap(&b);
  }
  inline void Swap(SystemLog* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SystemLog* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SystemLog* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SystemLog>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SystemLog& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SystemLog& from) {
    SystemLog::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SystemLog* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "datacapture.SystemLog";
  }
  protected:
  explicit SystemLog(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLogFieldNumber = 2,
    kStatusFieldNumber = 1,
  };
  // string log = 2;
  void clear_log();
  const std::string& log() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_log(ArgT0&& arg0, ArgT... args);
  std::string* mutable_log();
  PROTOBUF_NODISCARD std::string* release_log();
  void set_allocated_log(std::string* log);
  private:
  const std::string& _internal_log() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_log(const std::string& value);
  std::string* _internal_mutable_log();
  public:

  // bool status = 1;
  void clear_status();
  bool status() const;
  void set_status(bool value);
  private:
  bool _internal_status() const;
  void _internal_set_status(bool value);
  public:

  // @@protoc_insertion_point(class_scope:datacapture.SystemLog)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr log_;
    bool status_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_datacapture_2eCommunicate_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// Message

// .datacapture.Message.MessageType type = 1;
inline void Message::clear_type() {
  _impl_.type_ = 0;
}
inline ::datacapture::Message_MessageType Message::_internal_type() const {
  return static_cast< ::datacapture::Message_MessageType >(_impl_.type_);
}
inline ::datacapture::Message_MessageType Message::type() const {
  // @@protoc_insertion_point(field_get:datacapture.Message.type)
  return _internal_type();
}
inline void Message::_internal_set_type(::datacapture::Message_MessageType value) {
  
  _impl_.type_ = value;
}
inline void Message::set_type(::datacapture::Message_MessageType value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:datacapture.Message.type)
}

// .datacapture.CommonCMD commonCmd = 2;
inline bool Message::_internal_has_commoncmd() const {
  return data_case() == kCommonCmd;
}
inline bool Message::has_commoncmd() const {
  return _internal_has_commoncmd();
}
inline void Message::set_has_commoncmd() {
  _impl_._oneof_case_[0] = kCommonCmd;
}
inline void Message::clear_commoncmd() {
  if (_internal_has_commoncmd()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.data_.commoncmd_;
    }
    clear_has_data();
  }
}
inline ::datacapture::CommonCMD* Message::release_commoncmd() {
  // @@protoc_insertion_point(field_release:datacapture.Message.commonCmd)
  if (_internal_has_commoncmd()) {
    clear_has_data();
    ::datacapture::CommonCMD* temp = _impl_.data_.commoncmd_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.data_.commoncmd_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::datacapture::CommonCMD& Message::_internal_commoncmd() const {
  return _internal_has_commoncmd()
      ? *_impl_.data_.commoncmd_
      : reinterpret_cast< ::datacapture::CommonCMD&>(::datacapture::_CommonCMD_default_instance_);
}
inline const ::datacapture::CommonCMD& Message::commoncmd() const {
  // @@protoc_insertion_point(field_get:datacapture.Message.commonCmd)
  return _internal_commoncmd();
}
inline ::datacapture::CommonCMD* Message::unsafe_arena_release_commoncmd() {
  // @@protoc_insertion_point(field_unsafe_arena_release:datacapture.Message.commonCmd)
  if (_internal_has_commoncmd()) {
    clear_has_data();
    ::datacapture::CommonCMD* temp = _impl_.data_.commoncmd_;
    _impl_.data_.commoncmd_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Message::unsafe_arena_set_allocated_commoncmd(::datacapture::CommonCMD* commoncmd) {
  clear_data();
  if (commoncmd) {
    set_has_commoncmd();
    _impl_.data_.commoncmd_ = commoncmd;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:datacapture.Message.commonCmd)
}
inline ::datacapture::CommonCMD* Message::_internal_mutable_commoncmd() {
  if (!_internal_has_commoncmd()) {
    clear_data();
    set_has_commoncmd();
    _impl_.data_.commoncmd_ = CreateMaybeMessage< ::datacapture::CommonCMD >(GetArenaForAllocation());
  }
  return _impl_.data_.commoncmd_;
}
inline ::datacapture::CommonCMD* Message::mutable_commoncmd() {
  ::datacapture::CommonCMD* _msg = _internal_mutable_commoncmd();
  // @@protoc_insertion_point(field_mutable:datacapture.Message.commonCmd)
  return _msg;
}

// .datacapture.ArgsList argsList = 3;
inline bool Message::_internal_has_argslist() const {
  return data_case() == kArgsList;
}
inline bool Message::has_argslist() const {
  return _internal_has_argslist();
}
inline void Message::set_has_argslist() {
  _impl_._oneof_case_[0] = kArgsList;
}
inline void Message::clear_argslist() {
  if (_internal_has_argslist()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.data_.argslist_;
    }
    clear_has_data();
  }
}
inline ::datacapture::ArgsList* Message::release_argslist() {
  // @@protoc_insertion_point(field_release:datacapture.Message.argsList)
  if (_internal_has_argslist()) {
    clear_has_data();
    ::datacapture::ArgsList* temp = _impl_.data_.argslist_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.data_.argslist_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::datacapture::ArgsList& Message::_internal_argslist() const {
  return _internal_has_argslist()
      ? *_impl_.data_.argslist_
      : reinterpret_cast< ::datacapture::ArgsList&>(::datacapture::_ArgsList_default_instance_);
}
inline const ::datacapture::ArgsList& Message::argslist() const {
  // @@protoc_insertion_point(field_get:datacapture.Message.argsList)
  return _internal_argslist();
}
inline ::datacapture::ArgsList* Message::unsafe_arena_release_argslist() {
  // @@protoc_insertion_point(field_unsafe_arena_release:datacapture.Message.argsList)
  if (_internal_has_argslist()) {
    clear_has_data();
    ::datacapture::ArgsList* temp = _impl_.data_.argslist_;
    _impl_.data_.argslist_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Message::unsafe_arena_set_allocated_argslist(::datacapture::ArgsList* argslist) {
  clear_data();
  if (argslist) {
    set_has_argslist();
    _impl_.data_.argslist_ = argslist;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:datacapture.Message.argsList)
}
inline ::datacapture::ArgsList* Message::_internal_mutable_argslist() {
  if (!_internal_has_argslist()) {
    clear_data();
    set_has_argslist();
    _impl_.data_.argslist_ = CreateMaybeMessage< ::datacapture::ArgsList >(GetArenaForAllocation());
  }
  return _impl_.data_.argslist_;
}
inline ::datacapture::ArgsList* Message::mutable_argslist() {
  ::datacapture::ArgsList* _msg = _internal_mutable_argslist();
  // @@protoc_insertion_point(field_mutable:datacapture.Message.argsList)
  return _msg;
}

// .datacapture.CapStatus capStatus = 4;
inline bool Message::_internal_has_capstatus() const {
  return data_case() == kCapStatus;
}
inline bool Message::has_capstatus() const {
  return _internal_has_capstatus();
}
inline void Message::set_has_capstatus() {
  _impl_._oneof_case_[0] = kCapStatus;
}
inline void Message::clear_capstatus() {
  if (_internal_has_capstatus()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.data_.capstatus_;
    }
    clear_has_data();
  }
}
inline ::datacapture::CapStatus* Message::release_capstatus() {
  // @@protoc_insertion_point(field_release:datacapture.Message.capStatus)
  if (_internal_has_capstatus()) {
    clear_has_data();
    ::datacapture::CapStatus* temp = _impl_.data_.capstatus_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.data_.capstatus_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::datacapture::CapStatus& Message::_internal_capstatus() const {
  return _internal_has_capstatus()
      ? *_impl_.data_.capstatus_
      : reinterpret_cast< ::datacapture::CapStatus&>(::datacapture::_CapStatus_default_instance_);
}
inline const ::datacapture::CapStatus& Message::capstatus() const {
  // @@protoc_insertion_point(field_get:datacapture.Message.capStatus)
  return _internal_capstatus();
}
inline ::datacapture::CapStatus* Message::unsafe_arena_release_capstatus() {
  // @@protoc_insertion_point(field_unsafe_arena_release:datacapture.Message.capStatus)
  if (_internal_has_capstatus()) {
    clear_has_data();
    ::datacapture::CapStatus* temp = _impl_.data_.capstatus_;
    _impl_.data_.capstatus_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Message::unsafe_arena_set_allocated_capstatus(::datacapture::CapStatus* capstatus) {
  clear_data();
  if (capstatus) {
    set_has_capstatus();
    _impl_.data_.capstatus_ = capstatus;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:datacapture.Message.capStatus)
}
inline ::datacapture::CapStatus* Message::_internal_mutable_capstatus() {
  if (!_internal_has_capstatus()) {
    clear_data();
    set_has_capstatus();
    _impl_.data_.capstatus_ = CreateMaybeMessage< ::datacapture::CapStatus >(GetArenaForAllocation());
  }
  return _impl_.data_.capstatus_;
}
inline ::datacapture::CapStatus* Message::mutable_capstatus() {
  ::datacapture::CapStatus* _msg = _internal_mutable_capstatus();
  // @@protoc_insertion_point(field_mutable:datacapture.Message.capStatus)
  return _msg;
}

// .datacapture.DataQuery dataQueryReq = 5;
inline bool Message::_internal_has_dataqueryreq() const {
  return data_case() == kDataQueryReq;
}
inline bool Message::has_dataqueryreq() const {
  return _internal_has_dataqueryreq();
}
inline void Message::set_has_dataqueryreq() {
  _impl_._oneof_case_[0] = kDataQueryReq;
}
inline void Message::clear_dataqueryreq() {
  if (_internal_has_dataqueryreq()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.data_.dataqueryreq_;
    }
    clear_has_data();
  }
}
inline ::datacapture::DataQuery* Message::release_dataqueryreq() {
  // @@protoc_insertion_point(field_release:datacapture.Message.dataQueryReq)
  if (_internal_has_dataqueryreq()) {
    clear_has_data();
    ::datacapture::DataQuery* temp = _impl_.data_.dataqueryreq_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.data_.dataqueryreq_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::datacapture::DataQuery& Message::_internal_dataqueryreq() const {
  return _internal_has_dataqueryreq()
      ? *_impl_.data_.dataqueryreq_
      : reinterpret_cast< ::datacapture::DataQuery&>(::datacapture::_DataQuery_default_instance_);
}
inline const ::datacapture::DataQuery& Message::dataqueryreq() const {
  // @@protoc_insertion_point(field_get:datacapture.Message.dataQueryReq)
  return _internal_dataqueryreq();
}
inline ::datacapture::DataQuery* Message::unsafe_arena_release_dataqueryreq() {
  // @@protoc_insertion_point(field_unsafe_arena_release:datacapture.Message.dataQueryReq)
  if (_internal_has_dataqueryreq()) {
    clear_has_data();
    ::datacapture::DataQuery* temp = _impl_.data_.dataqueryreq_;
    _impl_.data_.dataqueryreq_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Message::unsafe_arena_set_allocated_dataqueryreq(::datacapture::DataQuery* dataqueryreq) {
  clear_data();
  if (dataqueryreq) {
    set_has_dataqueryreq();
    _impl_.data_.dataqueryreq_ = dataqueryreq;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:datacapture.Message.dataQueryReq)
}
inline ::datacapture::DataQuery* Message::_internal_mutable_dataqueryreq() {
  if (!_internal_has_dataqueryreq()) {
    clear_data();
    set_has_dataqueryreq();
    _impl_.data_.dataqueryreq_ = CreateMaybeMessage< ::datacapture::DataQuery >(GetArenaForAllocation());
  }
  return _impl_.data_.dataqueryreq_;
}
inline ::datacapture::DataQuery* Message::mutable_dataqueryreq() {
  ::datacapture::DataQuery* _msg = _internal_mutable_dataqueryreq();
  // @@protoc_insertion_point(field_mutable:datacapture.Message.dataQueryReq)
  return _msg;
}

// .datacapture.DataQueryRes dataQueryRes = 6;
inline bool Message::_internal_has_dataqueryres() const {
  return data_case() == kDataQueryRes;
}
inline bool Message::has_dataqueryres() const {
  return _internal_has_dataqueryres();
}
inline void Message::set_has_dataqueryres() {
  _impl_._oneof_case_[0] = kDataQueryRes;
}
inline void Message::clear_dataqueryres() {
  if (_internal_has_dataqueryres()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.data_.dataqueryres_;
    }
    clear_has_data();
  }
}
inline ::datacapture::DataQueryRes* Message::release_dataqueryres() {
  // @@protoc_insertion_point(field_release:datacapture.Message.dataQueryRes)
  if (_internal_has_dataqueryres()) {
    clear_has_data();
    ::datacapture::DataQueryRes* temp = _impl_.data_.dataqueryres_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.data_.dataqueryres_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::datacapture::DataQueryRes& Message::_internal_dataqueryres() const {
  return _internal_has_dataqueryres()
      ? *_impl_.data_.dataqueryres_
      : reinterpret_cast< ::datacapture::DataQueryRes&>(::datacapture::_DataQueryRes_default_instance_);
}
inline const ::datacapture::DataQueryRes& Message::dataqueryres() const {
  // @@protoc_insertion_point(field_get:datacapture.Message.dataQueryRes)
  return _internal_dataqueryres();
}
inline ::datacapture::DataQueryRes* Message::unsafe_arena_release_dataqueryres() {
  // @@protoc_insertion_point(field_unsafe_arena_release:datacapture.Message.dataQueryRes)
  if (_internal_has_dataqueryres()) {
    clear_has_data();
    ::datacapture::DataQueryRes* temp = _impl_.data_.dataqueryres_;
    _impl_.data_.dataqueryres_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Message::unsafe_arena_set_allocated_dataqueryres(::datacapture::DataQueryRes* dataqueryres) {
  clear_data();
  if (dataqueryres) {
    set_has_dataqueryres();
    _impl_.data_.dataqueryres_ = dataqueryres;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:datacapture.Message.dataQueryRes)
}
inline ::datacapture::DataQueryRes* Message::_internal_mutable_dataqueryres() {
  if (!_internal_has_dataqueryres()) {
    clear_data();
    set_has_dataqueryres();
    _impl_.data_.dataqueryres_ = CreateMaybeMessage< ::datacapture::DataQueryRes >(GetArenaForAllocation());
  }
  return _impl_.data_.dataqueryres_;
}
inline ::datacapture::DataQueryRes* Message::mutable_dataqueryres() {
  ::datacapture::DataQueryRes* _msg = _internal_mutable_dataqueryres();
  // @@protoc_insertion_point(field_mutable:datacapture.Message.dataQueryRes)
  return _msg;
}

// .datacapture.CompressReqList compressReq = 7;
inline bool Message::_internal_has_compressreq() const {
  return data_case() == kCompressReq;
}
inline bool Message::has_compressreq() const {
  return _internal_has_compressreq();
}
inline void Message::set_has_compressreq() {
  _impl_._oneof_case_[0] = kCompressReq;
}
inline void Message::clear_compressreq() {
  if (_internal_has_compressreq()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.data_.compressreq_;
    }
    clear_has_data();
  }
}
inline ::datacapture::CompressReqList* Message::release_compressreq() {
  // @@protoc_insertion_point(field_release:datacapture.Message.compressReq)
  if (_internal_has_compressreq()) {
    clear_has_data();
    ::datacapture::CompressReqList* temp = _impl_.data_.compressreq_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.data_.compressreq_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::datacapture::CompressReqList& Message::_internal_compressreq() const {
  return _internal_has_compressreq()
      ? *_impl_.data_.compressreq_
      : reinterpret_cast< ::datacapture::CompressReqList&>(::datacapture::_CompressReqList_default_instance_);
}
inline const ::datacapture::CompressReqList& Message::compressreq() const {
  // @@protoc_insertion_point(field_get:datacapture.Message.compressReq)
  return _internal_compressreq();
}
inline ::datacapture::CompressReqList* Message::unsafe_arena_release_compressreq() {
  // @@protoc_insertion_point(field_unsafe_arena_release:datacapture.Message.compressReq)
  if (_internal_has_compressreq()) {
    clear_has_data();
    ::datacapture::CompressReqList* temp = _impl_.data_.compressreq_;
    _impl_.data_.compressreq_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Message::unsafe_arena_set_allocated_compressreq(::datacapture::CompressReqList* compressreq) {
  clear_data();
  if (compressreq) {
    set_has_compressreq();
    _impl_.data_.compressreq_ = compressreq;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:datacapture.Message.compressReq)
}
inline ::datacapture::CompressReqList* Message::_internal_mutable_compressreq() {
  if (!_internal_has_compressreq()) {
    clear_data();
    set_has_compressreq();
    _impl_.data_.compressreq_ = CreateMaybeMessage< ::datacapture::CompressReqList >(GetArenaForAllocation());
  }
  return _impl_.data_.compressreq_;
}
inline ::datacapture::CompressReqList* Message::mutable_compressreq() {
  ::datacapture::CompressReqList* _msg = _internal_mutable_compressreq();
  // @@protoc_insertion_point(field_mutable:datacapture.Message.compressReq)
  return _msg;
}

// .datacapture.CompressRes compressRes = 8;
inline bool Message::_internal_has_compressres() const {
  return data_case() == kCompressRes;
}
inline bool Message::has_compressres() const {
  return _internal_has_compressres();
}
inline void Message::set_has_compressres() {
  _impl_._oneof_case_[0] = kCompressRes;
}
inline void Message::clear_compressres() {
  if (_internal_has_compressres()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.data_.compressres_;
    }
    clear_has_data();
  }
}
inline ::datacapture::CompressRes* Message::release_compressres() {
  // @@protoc_insertion_point(field_release:datacapture.Message.compressRes)
  if (_internal_has_compressres()) {
    clear_has_data();
    ::datacapture::CompressRes* temp = _impl_.data_.compressres_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.data_.compressres_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::datacapture::CompressRes& Message::_internal_compressres() const {
  return _internal_has_compressres()
      ? *_impl_.data_.compressres_
      : reinterpret_cast< ::datacapture::CompressRes&>(::datacapture::_CompressRes_default_instance_);
}
inline const ::datacapture::CompressRes& Message::compressres() const {
  // @@protoc_insertion_point(field_get:datacapture.Message.compressRes)
  return _internal_compressres();
}
inline ::datacapture::CompressRes* Message::unsafe_arena_release_compressres() {
  // @@protoc_insertion_point(field_unsafe_arena_release:datacapture.Message.compressRes)
  if (_internal_has_compressres()) {
    clear_has_data();
    ::datacapture::CompressRes* temp = _impl_.data_.compressres_;
    _impl_.data_.compressres_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Message::unsafe_arena_set_allocated_compressres(::datacapture::CompressRes* compressres) {
  clear_data();
  if (compressres) {
    set_has_compressres();
    _impl_.data_.compressres_ = compressres;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:datacapture.Message.compressRes)
}
inline ::datacapture::CompressRes* Message::_internal_mutable_compressres() {
  if (!_internal_has_compressres()) {
    clear_data();
    set_has_compressres();
    _impl_.data_.compressres_ = CreateMaybeMessage< ::datacapture::CompressRes >(GetArenaForAllocation());
  }
  return _impl_.data_.compressres_;
}
inline ::datacapture::CompressRes* Message::mutable_compressres() {
  ::datacapture::CompressRes* _msg = _internal_mutable_compressres();
  // @@protoc_insertion_point(field_mutable:datacapture.Message.compressRes)
  return _msg;
}

// .datacapture.Monitor monitor = 9;
inline bool Message::_internal_has_monitor() const {
  return data_case() == kMonitor;
}
inline bool Message::has_monitor() const {
  return _internal_has_monitor();
}
inline void Message::set_has_monitor() {
  _impl_._oneof_case_[0] = kMonitor;
}
inline void Message::clear_monitor() {
  if (_internal_has_monitor()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.data_.monitor_;
    }
    clear_has_data();
  }
}
inline ::datacapture::Monitor* Message::release_monitor() {
  // @@protoc_insertion_point(field_release:datacapture.Message.monitor)
  if (_internal_has_monitor()) {
    clear_has_data();
    ::datacapture::Monitor* temp = _impl_.data_.monitor_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.data_.monitor_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::datacapture::Monitor& Message::_internal_monitor() const {
  return _internal_has_monitor()
      ? *_impl_.data_.monitor_
      : reinterpret_cast< ::datacapture::Monitor&>(::datacapture::_Monitor_default_instance_);
}
inline const ::datacapture::Monitor& Message::monitor() const {
  // @@protoc_insertion_point(field_get:datacapture.Message.monitor)
  return _internal_monitor();
}
inline ::datacapture::Monitor* Message::unsafe_arena_release_monitor() {
  // @@protoc_insertion_point(field_unsafe_arena_release:datacapture.Message.monitor)
  if (_internal_has_monitor()) {
    clear_has_data();
    ::datacapture::Monitor* temp = _impl_.data_.monitor_;
    _impl_.data_.monitor_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Message::unsafe_arena_set_allocated_monitor(::datacapture::Monitor* monitor) {
  clear_data();
  if (monitor) {
    set_has_monitor();
    _impl_.data_.monitor_ = monitor;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:datacapture.Message.monitor)
}
inline ::datacapture::Monitor* Message::_internal_mutable_monitor() {
  if (!_internal_has_monitor()) {
    clear_data();
    set_has_monitor();
    _impl_.data_.monitor_ = CreateMaybeMessage< ::datacapture::Monitor >(GetArenaForAllocation());
  }
  return _impl_.data_.monitor_;
}
inline ::datacapture::Monitor* Message::mutable_monitor() {
  ::datacapture::Monitor* _msg = _internal_mutable_monitor();
  // @@protoc_insertion_point(field_mutable:datacapture.Message.monitor)
  return _msg;
}

// .datacapture.RoadInfoList roadInfoList = 10;
inline bool Message::_internal_has_roadinfolist() const {
  return data_case() == kRoadInfoList;
}
inline bool Message::has_roadinfolist() const {
  return _internal_has_roadinfolist();
}
inline void Message::set_has_roadinfolist() {
  _impl_._oneof_case_[0] = kRoadInfoList;
}
inline void Message::clear_roadinfolist() {
  if (_internal_has_roadinfolist()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.data_.roadinfolist_;
    }
    clear_has_data();
  }
}
inline ::datacapture::RoadInfoList* Message::release_roadinfolist() {
  // @@protoc_insertion_point(field_release:datacapture.Message.roadInfoList)
  if (_internal_has_roadinfolist()) {
    clear_has_data();
    ::datacapture::RoadInfoList* temp = _impl_.data_.roadinfolist_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.data_.roadinfolist_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::datacapture::RoadInfoList& Message::_internal_roadinfolist() const {
  return _internal_has_roadinfolist()
      ? *_impl_.data_.roadinfolist_
      : reinterpret_cast< ::datacapture::RoadInfoList&>(::datacapture::_RoadInfoList_default_instance_);
}
inline const ::datacapture::RoadInfoList& Message::roadinfolist() const {
  // @@protoc_insertion_point(field_get:datacapture.Message.roadInfoList)
  return _internal_roadinfolist();
}
inline ::datacapture::RoadInfoList* Message::unsafe_arena_release_roadinfolist() {
  // @@protoc_insertion_point(field_unsafe_arena_release:datacapture.Message.roadInfoList)
  if (_internal_has_roadinfolist()) {
    clear_has_data();
    ::datacapture::RoadInfoList* temp = _impl_.data_.roadinfolist_;
    _impl_.data_.roadinfolist_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Message::unsafe_arena_set_allocated_roadinfolist(::datacapture::RoadInfoList* roadinfolist) {
  clear_data();
  if (roadinfolist) {
    set_has_roadinfolist();
    _impl_.data_.roadinfolist_ = roadinfolist;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:datacapture.Message.roadInfoList)
}
inline ::datacapture::RoadInfoList* Message::_internal_mutable_roadinfolist() {
  if (!_internal_has_roadinfolist()) {
    clear_data();
    set_has_roadinfolist();
    _impl_.data_.roadinfolist_ = CreateMaybeMessage< ::datacapture::RoadInfoList >(GetArenaForAllocation());
  }
  return _impl_.data_.roadinfolist_;
}
inline ::datacapture::RoadInfoList* Message::mutable_roadinfolist() {
  ::datacapture::RoadInfoList* _msg = _internal_mutable_roadinfolist();
  // @@protoc_insertion_point(field_mutable:datacapture.Message.roadInfoList)
  return _msg;
}

// .datacapture.SystemStatus systemStatus = 11;
inline bool Message::_internal_has_systemstatus() const {
  return data_case() == kSystemStatus;
}
inline bool Message::has_systemstatus() const {
  return _internal_has_systemstatus();
}
inline void Message::set_has_systemstatus() {
  _impl_._oneof_case_[0] = kSystemStatus;
}
inline void Message::clear_systemstatus() {
  if (_internal_has_systemstatus()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.data_.systemstatus_;
    }
    clear_has_data();
  }
}
inline ::datacapture::SystemStatus* Message::release_systemstatus() {
  // @@protoc_insertion_point(field_release:datacapture.Message.systemStatus)
  if (_internal_has_systemstatus()) {
    clear_has_data();
    ::datacapture::SystemStatus* temp = _impl_.data_.systemstatus_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.data_.systemstatus_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::datacapture::SystemStatus& Message::_internal_systemstatus() const {
  return _internal_has_systemstatus()
      ? *_impl_.data_.systemstatus_
      : reinterpret_cast< ::datacapture::SystemStatus&>(::datacapture::_SystemStatus_default_instance_);
}
inline const ::datacapture::SystemStatus& Message::systemstatus() const {
  // @@protoc_insertion_point(field_get:datacapture.Message.systemStatus)
  return _internal_systemstatus();
}
inline ::datacapture::SystemStatus* Message::unsafe_arena_release_systemstatus() {
  // @@protoc_insertion_point(field_unsafe_arena_release:datacapture.Message.systemStatus)
  if (_internal_has_systemstatus()) {
    clear_has_data();
    ::datacapture::SystemStatus* temp = _impl_.data_.systemstatus_;
    _impl_.data_.systemstatus_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Message::unsafe_arena_set_allocated_systemstatus(::datacapture::SystemStatus* systemstatus) {
  clear_data();
  if (systemstatus) {
    set_has_systemstatus();
    _impl_.data_.systemstatus_ = systemstatus;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:datacapture.Message.systemStatus)
}
inline ::datacapture::SystemStatus* Message::_internal_mutable_systemstatus() {
  if (!_internal_has_systemstatus()) {
    clear_data();
    set_has_systemstatus();
    _impl_.data_.systemstatus_ = CreateMaybeMessage< ::datacapture::SystemStatus >(GetArenaForAllocation());
  }
  return _impl_.data_.systemstatus_;
}
inline ::datacapture::SystemStatus* Message::mutable_systemstatus() {
  ::datacapture::SystemStatus* _msg = _internal_mutable_systemstatus();
  // @@protoc_insertion_point(field_mutable:datacapture.Message.systemStatus)
  return _msg;
}

// .datacapture.DataQueryBatchesReq dataQueryBatchesReq = 12;
inline bool Message::_internal_has_dataquerybatchesreq() const {
  return data_case() == kDataQueryBatchesReq;
}
inline bool Message::has_dataquerybatchesreq() const {
  return _internal_has_dataquerybatchesreq();
}
inline void Message::set_has_dataquerybatchesreq() {
  _impl_._oneof_case_[0] = kDataQueryBatchesReq;
}
inline void Message::clear_dataquerybatchesreq() {
  if (_internal_has_dataquerybatchesreq()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.data_.dataquerybatchesreq_;
    }
    clear_has_data();
  }
}
inline ::datacapture::DataQueryBatchesReq* Message::release_dataquerybatchesreq() {
  // @@protoc_insertion_point(field_release:datacapture.Message.dataQueryBatchesReq)
  if (_internal_has_dataquerybatchesreq()) {
    clear_has_data();
    ::datacapture::DataQueryBatchesReq* temp = _impl_.data_.dataquerybatchesreq_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.data_.dataquerybatchesreq_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::datacapture::DataQueryBatchesReq& Message::_internal_dataquerybatchesreq() const {
  return _internal_has_dataquerybatchesreq()
      ? *_impl_.data_.dataquerybatchesreq_
      : reinterpret_cast< ::datacapture::DataQueryBatchesReq&>(::datacapture::_DataQueryBatchesReq_default_instance_);
}
inline const ::datacapture::DataQueryBatchesReq& Message::dataquerybatchesreq() const {
  // @@protoc_insertion_point(field_get:datacapture.Message.dataQueryBatchesReq)
  return _internal_dataquerybatchesreq();
}
inline ::datacapture::DataQueryBatchesReq* Message::unsafe_arena_release_dataquerybatchesreq() {
  // @@protoc_insertion_point(field_unsafe_arena_release:datacapture.Message.dataQueryBatchesReq)
  if (_internal_has_dataquerybatchesreq()) {
    clear_has_data();
    ::datacapture::DataQueryBatchesReq* temp = _impl_.data_.dataquerybatchesreq_;
    _impl_.data_.dataquerybatchesreq_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Message::unsafe_arena_set_allocated_dataquerybatchesreq(::datacapture::DataQueryBatchesReq* dataquerybatchesreq) {
  clear_data();
  if (dataquerybatchesreq) {
    set_has_dataquerybatchesreq();
    _impl_.data_.dataquerybatchesreq_ = dataquerybatchesreq;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:datacapture.Message.dataQueryBatchesReq)
}
inline ::datacapture::DataQueryBatchesReq* Message::_internal_mutable_dataquerybatchesreq() {
  if (!_internal_has_dataquerybatchesreq()) {
    clear_data();
    set_has_dataquerybatchesreq();
    _impl_.data_.dataquerybatchesreq_ = CreateMaybeMessage< ::datacapture::DataQueryBatchesReq >(GetArenaForAllocation());
  }
  return _impl_.data_.dataquerybatchesreq_;
}
inline ::datacapture::DataQueryBatchesReq* Message::mutable_dataquerybatchesreq() {
  ::datacapture::DataQueryBatchesReq* _msg = _internal_mutable_dataquerybatchesreq();
  // @@protoc_insertion_point(field_mutable:datacapture.Message.dataQueryBatchesReq)
  return _msg;
}

// .datacapture.DataQueryBatchesRes dataQueryBatchesRes = 13;
inline bool Message::_internal_has_dataquerybatchesres() const {
  return data_case() == kDataQueryBatchesRes;
}
inline bool Message::has_dataquerybatchesres() const {
  return _internal_has_dataquerybatchesres();
}
inline void Message::set_has_dataquerybatchesres() {
  _impl_._oneof_case_[0] = kDataQueryBatchesRes;
}
inline void Message::clear_dataquerybatchesres() {
  if (_internal_has_dataquerybatchesres()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.data_.dataquerybatchesres_;
    }
    clear_has_data();
  }
}
inline ::datacapture::DataQueryBatchesRes* Message::release_dataquerybatchesres() {
  // @@protoc_insertion_point(field_release:datacapture.Message.dataQueryBatchesRes)
  if (_internal_has_dataquerybatchesres()) {
    clear_has_data();
    ::datacapture::DataQueryBatchesRes* temp = _impl_.data_.dataquerybatchesres_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.data_.dataquerybatchesres_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::datacapture::DataQueryBatchesRes& Message::_internal_dataquerybatchesres() const {
  return _internal_has_dataquerybatchesres()
      ? *_impl_.data_.dataquerybatchesres_
      : reinterpret_cast< ::datacapture::DataQueryBatchesRes&>(::datacapture::_DataQueryBatchesRes_default_instance_);
}
inline const ::datacapture::DataQueryBatchesRes& Message::dataquerybatchesres() const {
  // @@protoc_insertion_point(field_get:datacapture.Message.dataQueryBatchesRes)
  return _internal_dataquerybatchesres();
}
inline ::datacapture::DataQueryBatchesRes* Message::unsafe_arena_release_dataquerybatchesres() {
  // @@protoc_insertion_point(field_unsafe_arena_release:datacapture.Message.dataQueryBatchesRes)
  if (_internal_has_dataquerybatchesres()) {
    clear_has_data();
    ::datacapture::DataQueryBatchesRes* temp = _impl_.data_.dataquerybatchesres_;
    _impl_.data_.dataquerybatchesres_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Message::unsafe_arena_set_allocated_dataquerybatchesres(::datacapture::DataQueryBatchesRes* dataquerybatchesres) {
  clear_data();
  if (dataquerybatchesres) {
    set_has_dataquerybatchesres();
    _impl_.data_.dataquerybatchesres_ = dataquerybatchesres;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:datacapture.Message.dataQueryBatchesRes)
}
inline ::datacapture::DataQueryBatchesRes* Message::_internal_mutable_dataquerybatchesres() {
  if (!_internal_has_dataquerybatchesres()) {
    clear_data();
    set_has_dataquerybatchesres();
    _impl_.data_.dataquerybatchesres_ = CreateMaybeMessage< ::datacapture::DataQueryBatchesRes >(GetArenaForAllocation());
  }
  return _impl_.data_.dataquerybatchesres_;
}
inline ::datacapture::DataQueryBatchesRes* Message::mutable_dataquerybatchesres() {
  ::datacapture::DataQueryBatchesRes* _msg = _internal_mutable_dataquerybatchesres();
  // @@protoc_insertion_point(field_mutable:datacapture.Message.dataQueryBatchesRes)
  return _msg;
}

// .datacapture.SystemInfo systemInfo = 14;
inline bool Message::_internal_has_systeminfo() const {
  return data_case() == kSystemInfo;
}
inline bool Message::has_systeminfo() const {
  return _internal_has_systeminfo();
}
inline void Message::set_has_systeminfo() {
  _impl_._oneof_case_[0] = kSystemInfo;
}
inline void Message::clear_systeminfo() {
  if (_internal_has_systeminfo()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.data_.systeminfo_;
    }
    clear_has_data();
  }
}
inline ::datacapture::SystemInfo* Message::release_systeminfo() {
  // @@protoc_insertion_point(field_release:datacapture.Message.systemInfo)
  if (_internal_has_systeminfo()) {
    clear_has_data();
    ::datacapture::SystemInfo* temp = _impl_.data_.systeminfo_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.data_.systeminfo_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::datacapture::SystemInfo& Message::_internal_systeminfo() const {
  return _internal_has_systeminfo()
      ? *_impl_.data_.systeminfo_
      : reinterpret_cast< ::datacapture::SystemInfo&>(::datacapture::_SystemInfo_default_instance_);
}
inline const ::datacapture::SystemInfo& Message::systeminfo() const {
  // @@protoc_insertion_point(field_get:datacapture.Message.systemInfo)
  return _internal_systeminfo();
}
inline ::datacapture::SystemInfo* Message::unsafe_arena_release_systeminfo() {
  // @@protoc_insertion_point(field_unsafe_arena_release:datacapture.Message.systemInfo)
  if (_internal_has_systeminfo()) {
    clear_has_data();
    ::datacapture::SystemInfo* temp = _impl_.data_.systeminfo_;
    _impl_.data_.systeminfo_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Message::unsafe_arena_set_allocated_systeminfo(::datacapture::SystemInfo* systeminfo) {
  clear_data();
  if (systeminfo) {
    set_has_systeminfo();
    _impl_.data_.systeminfo_ = systeminfo;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:datacapture.Message.systemInfo)
}
inline ::datacapture::SystemInfo* Message::_internal_mutable_systeminfo() {
  if (!_internal_has_systeminfo()) {
    clear_data();
    set_has_systeminfo();
    _impl_.data_.systeminfo_ = CreateMaybeMessage< ::datacapture::SystemInfo >(GetArenaForAllocation());
  }
  return _impl_.data_.systeminfo_;
}
inline ::datacapture::SystemInfo* Message::mutable_systeminfo() {
  ::datacapture::SystemInfo* _msg = _internal_mutable_systeminfo();
  // @@protoc_insertion_point(field_mutable:datacapture.Message.systemInfo)
  return _msg;
}

// .datacapture.ClientInfo clientInfo = 15;
inline bool Message::_internal_has_clientinfo() const {
  return data_case() == kClientInfo;
}
inline bool Message::has_clientinfo() const {
  return _internal_has_clientinfo();
}
inline void Message::set_has_clientinfo() {
  _impl_._oneof_case_[0] = kClientInfo;
}
inline void Message::clear_clientinfo() {
  if (_internal_has_clientinfo()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.data_.clientinfo_;
    }
    clear_has_data();
  }
}
inline ::datacapture::ClientInfo* Message::release_clientinfo() {
  // @@protoc_insertion_point(field_release:datacapture.Message.clientInfo)
  if (_internal_has_clientinfo()) {
    clear_has_data();
    ::datacapture::ClientInfo* temp = _impl_.data_.clientinfo_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.data_.clientinfo_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::datacapture::ClientInfo& Message::_internal_clientinfo() const {
  return _internal_has_clientinfo()
      ? *_impl_.data_.clientinfo_
      : reinterpret_cast< ::datacapture::ClientInfo&>(::datacapture::_ClientInfo_default_instance_);
}
inline const ::datacapture::ClientInfo& Message::clientinfo() const {
  // @@protoc_insertion_point(field_get:datacapture.Message.clientInfo)
  return _internal_clientinfo();
}
inline ::datacapture::ClientInfo* Message::unsafe_arena_release_clientinfo() {
  // @@protoc_insertion_point(field_unsafe_arena_release:datacapture.Message.clientInfo)
  if (_internal_has_clientinfo()) {
    clear_has_data();
    ::datacapture::ClientInfo* temp = _impl_.data_.clientinfo_;
    _impl_.data_.clientinfo_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Message::unsafe_arena_set_allocated_clientinfo(::datacapture::ClientInfo* clientinfo) {
  clear_data();
  if (clientinfo) {
    set_has_clientinfo();
    _impl_.data_.clientinfo_ = clientinfo;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:datacapture.Message.clientInfo)
}
inline ::datacapture::ClientInfo* Message::_internal_mutable_clientinfo() {
  if (!_internal_has_clientinfo()) {
    clear_data();
    set_has_clientinfo();
    _impl_.data_.clientinfo_ = CreateMaybeMessage< ::datacapture::ClientInfo >(GetArenaForAllocation());
  }
  return _impl_.data_.clientinfo_;
}
inline ::datacapture::ClientInfo* Message::mutable_clientinfo() {
  ::datacapture::ClientInfo* _msg = _internal_mutable_clientinfo();
  // @@protoc_insertion_point(field_mutable:datacapture.Message.clientInfo)
  return _msg;
}

// .datacapture.SystemCmd systemcmd = 16;
inline bool Message::_internal_has_systemcmd() const {
  return data_case() == kSystemcmd;
}
inline bool Message::has_systemcmd() const {
  return _internal_has_systemcmd();
}
inline void Message::set_has_systemcmd() {
  _impl_._oneof_case_[0] = kSystemcmd;
}
inline void Message::clear_systemcmd() {
  if (_internal_has_systemcmd()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.data_.systemcmd_;
    }
    clear_has_data();
  }
}
inline ::datacapture::SystemCmd* Message::release_systemcmd() {
  // @@protoc_insertion_point(field_release:datacapture.Message.systemcmd)
  if (_internal_has_systemcmd()) {
    clear_has_data();
    ::datacapture::SystemCmd* temp = _impl_.data_.systemcmd_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.data_.systemcmd_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::datacapture::SystemCmd& Message::_internal_systemcmd() const {
  return _internal_has_systemcmd()
      ? *_impl_.data_.systemcmd_
      : reinterpret_cast< ::datacapture::SystemCmd&>(::datacapture::_SystemCmd_default_instance_);
}
inline const ::datacapture::SystemCmd& Message::systemcmd() const {
  // @@protoc_insertion_point(field_get:datacapture.Message.systemcmd)
  return _internal_systemcmd();
}
inline ::datacapture::SystemCmd* Message::unsafe_arena_release_systemcmd() {
  // @@protoc_insertion_point(field_unsafe_arena_release:datacapture.Message.systemcmd)
  if (_internal_has_systemcmd()) {
    clear_has_data();
    ::datacapture::SystemCmd* temp = _impl_.data_.systemcmd_;
    _impl_.data_.systemcmd_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Message::unsafe_arena_set_allocated_systemcmd(::datacapture::SystemCmd* systemcmd) {
  clear_data();
  if (systemcmd) {
    set_has_systemcmd();
    _impl_.data_.systemcmd_ = systemcmd;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:datacapture.Message.systemcmd)
}
inline ::datacapture::SystemCmd* Message::_internal_mutable_systemcmd() {
  if (!_internal_has_systemcmd()) {
    clear_data();
    set_has_systemcmd();
    _impl_.data_.systemcmd_ = CreateMaybeMessage< ::datacapture::SystemCmd >(GetArenaForAllocation());
  }
  return _impl_.data_.systemcmd_;
}
inline ::datacapture::SystemCmd* Message::mutable_systemcmd() {
  ::datacapture::SystemCmd* _msg = _internal_mutable_systemcmd();
  // @@protoc_insertion_point(field_mutable:datacapture.Message.systemcmd)
  return _msg;
}

// .datacapture.storeInfo storeinfo = 17;
inline bool Message::_internal_has_storeinfo() const {
  return data_case() == kStoreinfo;
}
inline bool Message::has_storeinfo() const {
  return _internal_has_storeinfo();
}
inline void Message::set_has_storeinfo() {
  _impl_._oneof_case_[0] = kStoreinfo;
}
inline void Message::clear_storeinfo() {
  if (_internal_has_storeinfo()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.data_.storeinfo_;
    }
    clear_has_data();
  }
}
inline ::datacapture::storeInfo* Message::release_storeinfo() {
  // @@protoc_insertion_point(field_release:datacapture.Message.storeinfo)
  if (_internal_has_storeinfo()) {
    clear_has_data();
    ::datacapture::storeInfo* temp = _impl_.data_.storeinfo_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.data_.storeinfo_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::datacapture::storeInfo& Message::_internal_storeinfo() const {
  return _internal_has_storeinfo()
      ? *_impl_.data_.storeinfo_
      : reinterpret_cast< ::datacapture::storeInfo&>(::datacapture::_storeInfo_default_instance_);
}
inline const ::datacapture::storeInfo& Message::storeinfo() const {
  // @@protoc_insertion_point(field_get:datacapture.Message.storeinfo)
  return _internal_storeinfo();
}
inline ::datacapture::storeInfo* Message::unsafe_arena_release_storeinfo() {
  // @@protoc_insertion_point(field_unsafe_arena_release:datacapture.Message.storeinfo)
  if (_internal_has_storeinfo()) {
    clear_has_data();
    ::datacapture::storeInfo* temp = _impl_.data_.storeinfo_;
    _impl_.data_.storeinfo_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Message::unsafe_arena_set_allocated_storeinfo(::datacapture::storeInfo* storeinfo) {
  clear_data();
  if (storeinfo) {
    set_has_storeinfo();
    _impl_.data_.storeinfo_ = storeinfo;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:datacapture.Message.storeinfo)
}
inline ::datacapture::storeInfo* Message::_internal_mutable_storeinfo() {
  if (!_internal_has_storeinfo()) {
    clear_data();
    set_has_storeinfo();
    _impl_.data_.storeinfo_ = CreateMaybeMessage< ::datacapture::storeInfo >(GetArenaForAllocation());
  }
  return _impl_.data_.storeinfo_;
}
inline ::datacapture::storeInfo* Message::mutable_storeinfo() {
  ::datacapture::storeInfo* _msg = _internal_mutable_storeinfo();
  // @@protoc_insertion_point(field_mutable:datacapture.Message.storeinfo)
  return _msg;
}

// .datacapture.NetConfig netconfig = 18;
inline bool Message::_internal_has_netconfig() const {
  return data_case() == kNetconfig;
}
inline bool Message::has_netconfig() const {
  return _internal_has_netconfig();
}
inline void Message::set_has_netconfig() {
  _impl_._oneof_case_[0] = kNetconfig;
}
inline void Message::clear_netconfig() {
  if (_internal_has_netconfig()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.data_.netconfig_;
    }
    clear_has_data();
  }
}
inline ::datacapture::NetConfig* Message::release_netconfig() {
  // @@protoc_insertion_point(field_release:datacapture.Message.netconfig)
  if (_internal_has_netconfig()) {
    clear_has_data();
    ::datacapture::NetConfig* temp = _impl_.data_.netconfig_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.data_.netconfig_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::datacapture::NetConfig& Message::_internal_netconfig() const {
  return _internal_has_netconfig()
      ? *_impl_.data_.netconfig_
      : reinterpret_cast< ::datacapture::NetConfig&>(::datacapture::_NetConfig_default_instance_);
}
inline const ::datacapture::NetConfig& Message::netconfig() const {
  // @@protoc_insertion_point(field_get:datacapture.Message.netconfig)
  return _internal_netconfig();
}
inline ::datacapture::NetConfig* Message::unsafe_arena_release_netconfig() {
  // @@protoc_insertion_point(field_unsafe_arena_release:datacapture.Message.netconfig)
  if (_internal_has_netconfig()) {
    clear_has_data();
    ::datacapture::NetConfig* temp = _impl_.data_.netconfig_;
    _impl_.data_.netconfig_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Message::unsafe_arena_set_allocated_netconfig(::datacapture::NetConfig* netconfig) {
  clear_data();
  if (netconfig) {
    set_has_netconfig();
    _impl_.data_.netconfig_ = netconfig;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:datacapture.Message.netconfig)
}
inline ::datacapture::NetConfig* Message::_internal_mutable_netconfig() {
  if (!_internal_has_netconfig()) {
    clear_data();
    set_has_netconfig();
    _impl_.data_.netconfig_ = CreateMaybeMessage< ::datacapture::NetConfig >(GetArenaForAllocation());
  }
  return _impl_.data_.netconfig_;
}
inline ::datacapture::NetConfig* Message::mutable_netconfig() {
  ::datacapture::NetConfig* _msg = _internal_mutable_netconfig();
  // @@protoc_insertion_point(field_mutable:datacapture.Message.netconfig)
  return _msg;
}

// .datacapture.SystemLog systemLog = 19;
inline bool Message::_internal_has_systemlog() const {
  return data_case() == kSystemLog;
}
inline bool Message::has_systemlog() const {
  return _internal_has_systemlog();
}
inline void Message::set_has_systemlog() {
  _impl_._oneof_case_[0] = kSystemLog;
}
inline void Message::clear_systemlog() {
  if (_internal_has_systemlog()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.data_.systemlog_;
    }
    clear_has_data();
  }
}
inline ::datacapture::SystemLog* Message::release_systemlog() {
  // @@protoc_insertion_point(field_release:datacapture.Message.systemLog)
  if (_internal_has_systemlog()) {
    clear_has_data();
    ::datacapture::SystemLog* temp = _impl_.data_.systemlog_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.data_.systemlog_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::datacapture::SystemLog& Message::_internal_systemlog() const {
  return _internal_has_systemlog()
      ? *_impl_.data_.systemlog_
      : reinterpret_cast< ::datacapture::SystemLog&>(::datacapture::_SystemLog_default_instance_);
}
inline const ::datacapture::SystemLog& Message::systemlog() const {
  // @@protoc_insertion_point(field_get:datacapture.Message.systemLog)
  return _internal_systemlog();
}
inline ::datacapture::SystemLog* Message::unsafe_arena_release_systemlog() {
  // @@protoc_insertion_point(field_unsafe_arena_release:datacapture.Message.systemLog)
  if (_internal_has_systemlog()) {
    clear_has_data();
    ::datacapture::SystemLog* temp = _impl_.data_.systemlog_;
    _impl_.data_.systemlog_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Message::unsafe_arena_set_allocated_systemlog(::datacapture::SystemLog* systemlog) {
  clear_data();
  if (systemlog) {
    set_has_systemlog();
    _impl_.data_.systemlog_ = systemlog;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:datacapture.Message.systemLog)
}
inline ::datacapture::SystemLog* Message::_internal_mutable_systemlog() {
  if (!_internal_has_systemlog()) {
    clear_data();
    set_has_systemlog();
    _impl_.data_.systemlog_ = CreateMaybeMessage< ::datacapture::SystemLog >(GetArenaForAllocation());
  }
  return _impl_.data_.systemlog_;
}
inline ::datacapture::SystemLog* Message::mutable_systemlog() {
  ::datacapture::SystemLog* _msg = _internal_mutable_systemlog();
  // @@protoc_insertion_point(field_mutable:datacapture.Message.systemLog)
  return _msg;
}

inline bool Message::has_data() const {
  return data_case() != DATA_NOT_SET;
}
inline void Message::clear_has_data() {
  _impl_._oneof_case_[0] = DATA_NOT_SET;
}
inline Message::DataCase Message::data_case() const {
  return Message::DataCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// RawData

// string strCrossroadId = 1;
inline void RawData::clear_strcrossroadid() {
  _impl_.strcrossroadid_.ClearToEmpty();
}
inline const std::string& RawData::strcrossroadid() const {
  // @@protoc_insertion_point(field_get:datacapture.RawData.strCrossroadId)
  return _internal_strcrossroadid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RawData::set_strcrossroadid(ArgT0&& arg0, ArgT... args) {
 
 _impl_.strcrossroadid_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:datacapture.RawData.strCrossroadId)
}
inline std::string* RawData::mutable_strcrossroadid() {
  std::string* _s = _internal_mutable_strcrossroadid();
  // @@protoc_insertion_point(field_mutable:datacapture.RawData.strCrossroadId)
  return _s;
}
inline const std::string& RawData::_internal_strcrossroadid() const {
  return _impl_.strcrossroadid_.Get();
}
inline void RawData::_internal_set_strcrossroadid(const std::string& value) {
  
  _impl_.strcrossroadid_.Set(value, GetArenaForAllocation());
}
inline std::string* RawData::_internal_mutable_strcrossroadid() {
  
  return _impl_.strcrossroadid_.Mutable(GetArenaForAllocation());
}
inline std::string* RawData::release_strcrossroadid() {
  // @@protoc_insertion_point(field_release:datacapture.RawData.strCrossroadId)
  return _impl_.strcrossroadid_.Release();
}
inline void RawData::set_allocated_strcrossroadid(std::string* strcrossroadid) {
  if (strcrossroadid != nullptr) {
    
  } else {
    
  }
  _impl_.strcrossroadid_.SetAllocated(strcrossroadid, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.strcrossroadid_.IsDefault()) {
    _impl_.strcrossroadid_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:datacapture.RawData.strCrossroadId)
}

// uint64 llRecvTime = 2;
inline void RawData::clear_llrecvtime() {
  _impl_.llrecvtime_ = uint64_t{0u};
}
inline uint64_t RawData::_internal_llrecvtime() const {
  return _impl_.llrecvtime_;
}
inline uint64_t RawData::llrecvtime() const {
  // @@protoc_insertion_point(field_get:datacapture.RawData.llRecvTime)
  return _internal_llrecvtime();
}
inline void RawData::_internal_set_llrecvtime(uint64_t value) {
  
  _impl_.llrecvtime_ = value;
}
inline void RawData::set_llrecvtime(uint64_t value) {
  _internal_set_llrecvtime(value);
  // @@protoc_insertion_point(field_set:datacapture.RawData.llRecvTime)
}

// uint64 llDataTime = 3;
inline void RawData::clear_lldatatime() {
  _impl_.lldatatime_ = uint64_t{0u};
}
inline uint64_t RawData::_internal_lldatatime() const {
  return _impl_.lldatatime_;
}
inline uint64_t RawData::lldatatime() const {
  // @@protoc_insertion_point(field_get:datacapture.RawData.llDataTime)
  return _internal_lldatatime();
}
inline void RawData::_internal_set_lldatatime(uint64_t value) {
  
  _impl_.lldatatime_ = value;
}
inline void RawData::set_lldatatime(uint64_t value) {
  _internal_set_lldatatime(value);
  // @@protoc_insertion_point(field_set:datacapture.RawData.llDataTime)
}

// uint32 dwDatalength = 4;
inline void RawData::clear_dwdatalength() {
  _impl_.dwdatalength_ = 0u;
}
inline uint32_t RawData::_internal_dwdatalength() const {
  return _impl_.dwdatalength_;
}
inline uint32_t RawData::dwdatalength() const {
  // @@protoc_insertion_point(field_get:datacapture.RawData.dwDatalength)
  return _internal_dwdatalength();
}
inline void RawData::_internal_set_dwdatalength(uint32_t value) {
  
  _impl_.dwdatalength_ = value;
}
inline void RawData::set_dwdatalength(uint32_t value) {
  _internal_set_dwdatalength(value);
  // @@protoc_insertion_point(field_set:datacapture.RawData.dwDatalength)
}

// string strData = 5;
inline void RawData::clear_strdata() {
  _impl_.strdata_.ClearToEmpty();
}
inline const std::string& RawData::strdata() const {
  // @@protoc_insertion_point(field_get:datacapture.RawData.strData)
  return _internal_strdata();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RawData::set_strdata(ArgT0&& arg0, ArgT... args) {
 
 _impl_.strdata_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:datacapture.RawData.strData)
}
inline std::string* RawData::mutable_strdata() {
  std::string* _s = _internal_mutable_strdata();
  // @@protoc_insertion_point(field_mutable:datacapture.RawData.strData)
  return _s;
}
inline const std::string& RawData::_internal_strdata() const {
  return _impl_.strdata_.Get();
}
inline void RawData::_internal_set_strdata(const std::string& value) {
  
  _impl_.strdata_.Set(value, GetArenaForAllocation());
}
inline std::string* RawData::_internal_mutable_strdata() {
  
  return _impl_.strdata_.Mutable(GetArenaForAllocation());
}
inline std::string* RawData::release_strdata() {
  // @@protoc_insertion_point(field_release:datacapture.RawData.strData)
  return _impl_.strdata_.Release();
}
inline void RawData::set_allocated_strdata(std::string* strdata) {
  if (strdata != nullptr) {
    
  } else {
    
  }
  _impl_.strdata_.SetAllocated(strdata, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.strdata_.IsDefault()) {
    _impl_.strdata_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:datacapture.RawData.strData)
}

// -------------------------------------------------------------------

// CapStatus

// string strCrossroadId = 1;
inline void CapStatus::clear_strcrossroadid() {
  _impl_.strcrossroadid_.ClearToEmpty();
}
inline const std::string& CapStatus::strcrossroadid() const {
  // @@protoc_insertion_point(field_get:datacapture.CapStatus.strCrossroadId)
  return _internal_strcrossroadid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CapStatus::set_strcrossroadid(ArgT0&& arg0, ArgT... args) {
 
 _impl_.strcrossroadid_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:datacapture.CapStatus.strCrossroadId)
}
inline std::string* CapStatus::mutable_strcrossroadid() {
  std::string* _s = _internal_mutable_strcrossroadid();
  // @@protoc_insertion_point(field_mutable:datacapture.CapStatus.strCrossroadId)
  return _s;
}
inline const std::string& CapStatus::_internal_strcrossroadid() const {
  return _impl_.strcrossroadid_.Get();
}
inline void CapStatus::_internal_set_strcrossroadid(const std::string& value) {
  
  _impl_.strcrossroadid_.Set(value, GetArenaForAllocation());
}
inline std::string* CapStatus::_internal_mutable_strcrossroadid() {
  
  return _impl_.strcrossroadid_.Mutable(GetArenaForAllocation());
}
inline std::string* CapStatus::release_strcrossroadid() {
  // @@protoc_insertion_point(field_release:datacapture.CapStatus.strCrossroadId)
  return _impl_.strcrossroadid_.Release();
}
inline void CapStatus::set_allocated_strcrossroadid(std::string* strcrossroadid) {
  if (strcrossroadid != nullptr) {
    
  } else {
    
  }
  _impl_.strcrossroadid_.SetAllocated(strcrossroadid, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.strcrossroadid_.IsDefault()) {
    _impl_.strcrossroadid_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:datacapture.CapStatus.strCrossroadId)
}

// string strDescribe = 2;
inline void CapStatus::clear_strdescribe() {
  _impl_.strdescribe_.ClearToEmpty();
}
inline const std::string& CapStatus::strdescribe() const {
  // @@protoc_insertion_point(field_get:datacapture.CapStatus.strDescribe)
  return _internal_strdescribe();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CapStatus::set_strdescribe(ArgT0&& arg0, ArgT... args) {
 
 _impl_.strdescribe_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:datacapture.CapStatus.strDescribe)
}
inline std::string* CapStatus::mutable_strdescribe() {
  std::string* _s = _internal_mutable_strdescribe();
  // @@protoc_insertion_point(field_mutable:datacapture.CapStatus.strDescribe)
  return _s;
}
inline const std::string& CapStatus::_internal_strdescribe() const {
  return _impl_.strdescribe_.Get();
}
inline void CapStatus::_internal_set_strdescribe(const std::string& value) {
  
  _impl_.strdescribe_.Set(value, GetArenaForAllocation());
}
inline std::string* CapStatus::_internal_mutable_strdescribe() {
  
  return _impl_.strdescribe_.Mutable(GetArenaForAllocation());
}
inline std::string* CapStatus::release_strdescribe() {
  // @@protoc_insertion_point(field_release:datacapture.CapStatus.strDescribe)
  return _impl_.strdescribe_.Release();
}
inline void CapStatus::set_allocated_strdescribe(std::string* strdescribe) {
  if (strdescribe != nullptr) {
    
  } else {
    
  }
  _impl_.strdescribe_.SetAllocated(strdescribe, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.strdescribe_.IsDefault()) {
    _impl_.strdescribe_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:datacapture.CapStatus.strDescribe)
}

// uint64 llTimestamp = 3;
inline void CapStatus::clear_lltimestamp() {
  _impl_.lltimestamp_ = uint64_t{0u};
}
inline uint64_t CapStatus::_internal_lltimestamp() const {
  return _impl_.lltimestamp_;
}
inline uint64_t CapStatus::lltimestamp() const {
  // @@protoc_insertion_point(field_get:datacapture.CapStatus.llTimestamp)
  return _internal_lltimestamp();
}
inline void CapStatus::_internal_set_lltimestamp(uint64_t value) {
  
  _impl_.lltimestamp_ = value;
}
inline void CapStatus::set_lltimestamp(uint64_t value) {
  _internal_set_lltimestamp(value);
  // @@protoc_insertion_point(field_set:datacapture.CapStatus.llTimestamp)
}

// bool bIsCaptruing = 4;
inline void CapStatus::clear_biscaptruing() {
  _impl_.biscaptruing_ = false;
}
inline bool CapStatus::_internal_biscaptruing() const {
  return _impl_.biscaptruing_;
}
inline bool CapStatus::biscaptruing() const {
  // @@protoc_insertion_point(field_get:datacapture.CapStatus.bIsCaptruing)
  return _internal_biscaptruing();
}
inline void CapStatus::_internal_set_biscaptruing(bool value) {
  
  _impl_.biscaptruing_ = value;
}
inline void CapStatus::set_biscaptruing(bool value) {
  _internal_set_biscaptruing(value);
  // @@protoc_insertion_point(field_set:datacapture.CapStatus.bIsCaptruing)
}

// float fFreq = 5;
inline void CapStatus::clear_ffreq() {
  _impl_.ffreq_ = 0;
}
inline float CapStatus::_internal_ffreq() const {
  return _impl_.ffreq_;
}
inline float CapStatus::ffreq() const {
  // @@protoc_insertion_point(field_get:datacapture.CapStatus.fFreq)
  return _internal_ffreq();
}
inline void CapStatus::_internal_set_ffreq(float value) {
  
  _impl_.ffreq_ = value;
}
inline void CapStatus::set_ffreq(float value) {
  _internal_set_ffreq(value);
  // @@protoc_insertion_point(field_set:datacapture.CapStatus.fFreq)
}

// uint32 dwRecvDataCnt = 6;
inline void CapStatus::clear_dwrecvdatacnt() {
  _impl_.dwrecvdatacnt_ = 0u;
}
inline uint32_t CapStatus::_internal_dwrecvdatacnt() const {
  return _impl_.dwrecvdatacnt_;
}
inline uint32_t CapStatus::dwrecvdatacnt() const {
  // @@protoc_insertion_point(field_get:datacapture.CapStatus.dwRecvDataCnt)
  return _internal_dwrecvdatacnt();
}
inline void CapStatus::_internal_set_dwrecvdatacnt(uint32_t value) {
  
  _impl_.dwrecvdatacnt_ = value;
}
inline void CapStatus::set_dwrecvdatacnt(uint32_t value) {
  _internal_set_dwrecvdatacnt(value);
  // @@protoc_insertion_point(field_set:datacapture.CapStatus.dwRecvDataCnt)
}

// -------------------------------------------------------------------

// Monitor

// uint32 dwStatusCnt = 1;
inline void Monitor::clear_dwstatuscnt() {
  _impl_.dwstatuscnt_ = 0u;
}
inline uint32_t Monitor::_internal_dwstatuscnt() const {
  return _impl_.dwstatuscnt_;
}
inline uint32_t Monitor::dwstatuscnt() const {
  // @@protoc_insertion_point(field_get:datacapture.Monitor.dwStatusCnt)
  return _internal_dwstatuscnt();
}
inline void Monitor::_internal_set_dwstatuscnt(uint32_t value) {
  
  _impl_.dwstatuscnt_ = value;
}
inline void Monitor::set_dwstatuscnt(uint32_t value) {
  _internal_set_dwstatuscnt(value);
  // @@protoc_insertion_point(field_set:datacapture.Monitor.dwStatusCnt)
}

// uint64 llUpdateTime = 2;
inline void Monitor::clear_llupdatetime() {
  _impl_.llupdatetime_ = uint64_t{0u};
}
inline uint64_t Monitor::_internal_llupdatetime() const {
  return _impl_.llupdatetime_;
}
inline uint64_t Monitor::llupdatetime() const {
  // @@protoc_insertion_point(field_get:datacapture.Monitor.llUpdateTime)
  return _internal_llupdatetime();
}
inline void Monitor::_internal_set_llupdatetime(uint64_t value) {
  
  _impl_.llupdatetime_ = value;
}
inline void Monitor::set_llupdatetime(uint64_t value) {
  _internal_set_llupdatetime(value);
  // @@protoc_insertion_point(field_set:datacapture.Monitor.llUpdateTime)
}

// uint64 llStartTime = 3;
inline void Monitor::clear_llstarttime() {
  _impl_.llstarttime_ = uint64_t{0u};
}
inline uint64_t Monitor::_internal_llstarttime() const {
  return _impl_.llstarttime_;
}
inline uint64_t Monitor::llstarttime() const {
  // @@protoc_insertion_point(field_get:datacapture.Monitor.llStartTime)
  return _internal_llstarttime();
}
inline void Monitor::_internal_set_llstarttime(uint64_t value) {
  
  _impl_.llstarttime_ = value;
}
inline void Monitor::set_llstarttime(uint64_t value) {
  _internal_set_llstarttime(value);
  // @@protoc_insertion_point(field_set:datacapture.Monitor.llStartTime)
}

// uint64 llDurationTime = 4;
inline void Monitor::clear_lldurationtime() {
  _impl_.lldurationtime_ = uint64_t{0u};
}
inline uint64_t Monitor::_internal_lldurationtime() const {
  return _impl_.lldurationtime_;
}
inline uint64_t Monitor::lldurationtime() const {
  // @@protoc_insertion_point(field_get:datacapture.Monitor.llDurationTime)
  return _internal_lldurationtime();
}
inline void Monitor::_internal_set_lldurationtime(uint64_t value) {
  
  _impl_.lldurationtime_ = value;
}
inline void Monitor::set_lldurationtime(uint64_t value) {
  _internal_set_lldurationtime(value);
  // @@protoc_insertion_point(field_set:datacapture.Monitor.llDurationTime)
}

// repeated .datacapture.CapStatus statusList = 5;
inline int Monitor::_internal_statuslist_size() const {
  return _impl_.statuslist_.size();
}
inline int Monitor::statuslist_size() const {
  return _internal_statuslist_size();
}
inline void Monitor::clear_statuslist() {
  _impl_.statuslist_.Clear();
}
inline ::datacapture::CapStatus* Monitor::mutable_statuslist(int index) {
  // @@protoc_insertion_point(field_mutable:datacapture.Monitor.statusList)
  return _impl_.statuslist_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::datacapture::CapStatus >*
Monitor::mutable_statuslist() {
  // @@protoc_insertion_point(field_mutable_list:datacapture.Monitor.statusList)
  return &_impl_.statuslist_;
}
inline const ::datacapture::CapStatus& Monitor::_internal_statuslist(int index) const {
  return _impl_.statuslist_.Get(index);
}
inline const ::datacapture::CapStatus& Monitor::statuslist(int index) const {
  // @@protoc_insertion_point(field_get:datacapture.Monitor.statusList)
  return _internal_statuslist(index);
}
inline ::datacapture::CapStatus* Monitor::_internal_add_statuslist() {
  return _impl_.statuslist_.Add();
}
inline ::datacapture::CapStatus* Monitor::add_statuslist() {
  ::datacapture::CapStatus* _add = _internal_add_statuslist();
  // @@protoc_insertion_point(field_add:datacapture.Monitor.statusList)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::datacapture::CapStatus >&
Monitor::statuslist() const {
  // @@protoc_insertion_point(field_list:datacapture.Monitor.statusList)
  return _impl_.statuslist_;
}

// -------------------------------------------------------------------

// DataQuery

// string strCrossroadId = 1;
inline void DataQuery::clear_strcrossroadid() {
  _impl_.strcrossroadid_.ClearToEmpty();
}
inline const std::string& DataQuery::strcrossroadid() const {
  // @@protoc_insertion_point(field_get:datacapture.DataQuery.strCrossroadId)
  return _internal_strcrossroadid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DataQuery::set_strcrossroadid(ArgT0&& arg0, ArgT... args) {
 
 _impl_.strcrossroadid_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:datacapture.DataQuery.strCrossroadId)
}
inline std::string* DataQuery::mutable_strcrossroadid() {
  std::string* _s = _internal_mutable_strcrossroadid();
  // @@protoc_insertion_point(field_mutable:datacapture.DataQuery.strCrossroadId)
  return _s;
}
inline const std::string& DataQuery::_internal_strcrossroadid() const {
  return _impl_.strcrossroadid_.Get();
}
inline void DataQuery::_internal_set_strcrossroadid(const std::string& value) {
  
  _impl_.strcrossroadid_.Set(value, GetArenaForAllocation());
}
inline std::string* DataQuery::_internal_mutable_strcrossroadid() {
  
  return _impl_.strcrossroadid_.Mutable(GetArenaForAllocation());
}
inline std::string* DataQuery::release_strcrossroadid() {
  // @@protoc_insertion_point(field_release:datacapture.DataQuery.strCrossroadId)
  return _impl_.strcrossroadid_.Release();
}
inline void DataQuery::set_allocated_strcrossroadid(std::string* strcrossroadid) {
  if (strcrossroadid != nullptr) {
    
  } else {
    
  }
  _impl_.strcrossroadid_.SetAllocated(strcrossroadid, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.strcrossroadid_.IsDefault()) {
    _impl_.strcrossroadid_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:datacapture.DataQuery.strCrossroadId)
}

// .datacapture.DataQuery.dataType type = 2;
inline void DataQuery::clear_type() {
  _impl_.type_ = 0;
}
inline ::datacapture::DataQuery_dataType DataQuery::_internal_type() const {
  return static_cast< ::datacapture::DataQuery_dataType >(_impl_.type_);
}
inline ::datacapture::DataQuery_dataType DataQuery::type() const {
  // @@protoc_insertion_point(field_get:datacapture.DataQuery.type)
  return _internal_type();
}
inline void DataQuery::_internal_set_type(::datacapture::DataQuery_dataType value) {
  
  _impl_.type_ = value;
}
inline void DataQuery::set_type(::datacapture::DataQuery_dataType value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:datacapture.DataQuery.type)
}

// uint64 llStartTime = 3;
inline void DataQuery::clear_llstarttime() {
  _impl_.llstarttime_ = uint64_t{0u};
}
inline uint64_t DataQuery::_internal_llstarttime() const {
  return _impl_.llstarttime_;
}
inline uint64_t DataQuery::llstarttime() const {
  // @@protoc_insertion_point(field_get:datacapture.DataQuery.llStartTime)
  return _internal_llstarttime();
}
inline void DataQuery::_internal_set_llstarttime(uint64_t value) {
  
  _impl_.llstarttime_ = value;
}
inline void DataQuery::set_llstarttime(uint64_t value) {
  _internal_set_llstarttime(value);
  // @@protoc_insertion_point(field_set:datacapture.DataQuery.llStartTime)
}

// uint64 llEndTime = 4;
inline void DataQuery::clear_llendtime() {
  _impl_.llendtime_ = uint64_t{0u};
}
inline uint64_t DataQuery::_internal_llendtime() const {
  return _impl_.llendtime_;
}
inline uint64_t DataQuery::llendtime() const {
  // @@protoc_insertion_point(field_get:datacapture.DataQuery.llEndTime)
  return _internal_llendtime();
}
inline void DataQuery::_internal_set_llendtime(uint64_t value) {
  
  _impl_.llendtime_ = value;
}
inline void DataQuery::set_llendtime(uint64_t value) {
  _internal_set_llendtime(value);
  // @@protoc_insertion_point(field_set:datacapture.DataQuery.llEndTime)
}

// -------------------------------------------------------------------

// DataQueryRes

// uint32 dwCnt = 1;
inline void DataQueryRes::clear_dwcnt() {
  _impl_.dwcnt_ = 0u;
}
inline uint32_t DataQueryRes::_internal_dwcnt() const {
  return _impl_.dwcnt_;
}
inline uint32_t DataQueryRes::dwcnt() const {
  // @@protoc_insertion_point(field_get:datacapture.DataQueryRes.dwCnt)
  return _internal_dwcnt();
}
inline void DataQueryRes::_internal_set_dwcnt(uint32_t value) {
  
  _impl_.dwcnt_ = value;
}
inline void DataQueryRes::set_dwcnt(uint32_t value) {
  _internal_set_dwcnt(value);
  // @@protoc_insertion_point(field_set:datacapture.DataQueryRes.dwCnt)
}

// bool bIsSucceed = 2;
inline void DataQueryRes::clear_bissucceed() {
  _impl_.bissucceed_ = false;
}
inline bool DataQueryRes::_internal_bissucceed() const {
  return _impl_.bissucceed_;
}
inline bool DataQueryRes::bissucceed() const {
  // @@protoc_insertion_point(field_get:datacapture.DataQueryRes.bIsSucceed)
  return _internal_bissucceed();
}
inline void DataQueryRes::_internal_set_bissucceed(bool value) {
  
  _impl_.bissucceed_ = value;
}
inline void DataQueryRes::set_bissucceed(bool value) {
  _internal_set_bissucceed(value);
  // @@protoc_insertion_point(field_set:datacapture.DataQueryRes.bIsSucceed)
}

// string strErr = 3;
inline void DataQueryRes::clear_strerr() {
  _impl_.strerr_.ClearToEmpty();
}
inline const std::string& DataQueryRes::strerr() const {
  // @@protoc_insertion_point(field_get:datacapture.DataQueryRes.strErr)
  return _internal_strerr();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DataQueryRes::set_strerr(ArgT0&& arg0, ArgT... args) {
 
 _impl_.strerr_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:datacapture.DataQueryRes.strErr)
}
inline std::string* DataQueryRes::mutable_strerr() {
  std::string* _s = _internal_mutable_strerr();
  // @@protoc_insertion_point(field_mutable:datacapture.DataQueryRes.strErr)
  return _s;
}
inline const std::string& DataQueryRes::_internal_strerr() const {
  return _impl_.strerr_.Get();
}
inline void DataQueryRes::_internal_set_strerr(const std::string& value) {
  
  _impl_.strerr_.Set(value, GetArenaForAllocation());
}
inline std::string* DataQueryRes::_internal_mutable_strerr() {
  
  return _impl_.strerr_.Mutable(GetArenaForAllocation());
}
inline std::string* DataQueryRes::release_strerr() {
  // @@protoc_insertion_point(field_release:datacapture.DataQueryRes.strErr)
  return _impl_.strerr_.Release();
}
inline void DataQueryRes::set_allocated_strerr(std::string* strerr) {
  if (strerr != nullptr) {
    
  } else {
    
  }
  _impl_.strerr_.SetAllocated(strerr, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.strerr_.IsDefault()) {
    _impl_.strerr_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:datacapture.DataQueryRes.strErr)
}

// repeated .datacapture.RawData dataList = 4;
inline int DataQueryRes::_internal_datalist_size() const {
  return _impl_.datalist_.size();
}
inline int DataQueryRes::datalist_size() const {
  return _internal_datalist_size();
}
inline void DataQueryRes::clear_datalist() {
  _impl_.datalist_.Clear();
}
inline ::datacapture::RawData* DataQueryRes::mutable_datalist(int index) {
  // @@protoc_insertion_point(field_mutable:datacapture.DataQueryRes.dataList)
  return _impl_.datalist_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::datacapture::RawData >*
DataQueryRes::mutable_datalist() {
  // @@protoc_insertion_point(field_mutable_list:datacapture.DataQueryRes.dataList)
  return &_impl_.datalist_;
}
inline const ::datacapture::RawData& DataQueryRes::_internal_datalist(int index) const {
  return _impl_.datalist_.Get(index);
}
inline const ::datacapture::RawData& DataQueryRes::datalist(int index) const {
  // @@protoc_insertion_point(field_get:datacapture.DataQueryRes.dataList)
  return _internal_datalist(index);
}
inline ::datacapture::RawData* DataQueryRes::_internal_add_datalist() {
  return _impl_.datalist_.Add();
}
inline ::datacapture::RawData* DataQueryRes::add_datalist() {
  ::datacapture::RawData* _add = _internal_add_datalist();
  // @@protoc_insertion_point(field_add:datacapture.DataQueryRes.dataList)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::datacapture::RawData >&
DataQueryRes::datalist() const {
  // @@protoc_insertion_point(field_list:datacapture.DataQueryRes.dataList)
  return _impl_.datalist_;
}

// -------------------------------------------------------------------

// DataQueryBatchesReq

// string strCrossID = 1;
inline void DataQueryBatchesReq::clear_strcrossid() {
  _impl_.strcrossid_.ClearToEmpty();
}
inline const std::string& DataQueryBatchesReq::strcrossid() const {
  // @@protoc_insertion_point(field_get:datacapture.DataQueryBatchesReq.strCrossID)
  return _internal_strcrossid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DataQueryBatchesReq::set_strcrossid(ArgT0&& arg0, ArgT... args) {
 
 _impl_.strcrossid_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:datacapture.DataQueryBatchesReq.strCrossID)
}
inline std::string* DataQueryBatchesReq::mutable_strcrossid() {
  std::string* _s = _internal_mutable_strcrossid();
  // @@protoc_insertion_point(field_mutable:datacapture.DataQueryBatchesReq.strCrossID)
  return _s;
}
inline const std::string& DataQueryBatchesReq::_internal_strcrossid() const {
  return _impl_.strcrossid_.Get();
}
inline void DataQueryBatchesReq::_internal_set_strcrossid(const std::string& value) {
  
  _impl_.strcrossid_.Set(value, GetArenaForAllocation());
}
inline std::string* DataQueryBatchesReq::_internal_mutable_strcrossid() {
  
  return _impl_.strcrossid_.Mutable(GetArenaForAllocation());
}
inline std::string* DataQueryBatchesReq::release_strcrossid() {
  // @@protoc_insertion_point(field_release:datacapture.DataQueryBatchesReq.strCrossID)
  return _impl_.strcrossid_.Release();
}
inline void DataQueryBatchesReq::set_allocated_strcrossid(std::string* strcrossid) {
  if (strcrossid != nullptr) {
    
  } else {
    
  }
  _impl_.strcrossid_.SetAllocated(strcrossid, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.strcrossid_.IsDefault()) {
    _impl_.strcrossid_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:datacapture.DataQueryBatchesReq.strCrossID)
}

// uint64 llStartTime = 2;
inline void DataQueryBatchesReq::clear_llstarttime() {
  _impl_.llstarttime_ = uint64_t{0u};
}
inline uint64_t DataQueryBatchesReq::_internal_llstarttime() const {
  return _impl_.llstarttime_;
}
inline uint64_t DataQueryBatchesReq::llstarttime() const {
  // @@protoc_insertion_point(field_get:datacapture.DataQueryBatchesReq.llStartTime)
  return _internal_llstarttime();
}
inline void DataQueryBatchesReq::_internal_set_llstarttime(uint64_t value) {
  
  _impl_.llstarttime_ = value;
}
inline void DataQueryBatchesReq::set_llstarttime(uint64_t value) {
  _internal_set_llstarttime(value);
  // @@protoc_insertion_point(field_set:datacapture.DataQueryBatchesReq.llStartTime)
}

// uint64 llEndTime = 3;
inline void DataQueryBatchesReq::clear_llendtime() {
  _impl_.llendtime_ = uint64_t{0u};
}
inline uint64_t DataQueryBatchesReq::_internal_llendtime() const {
  return _impl_.llendtime_;
}
inline uint64_t DataQueryBatchesReq::llendtime() const {
  // @@protoc_insertion_point(field_get:datacapture.DataQueryBatchesReq.llEndTime)
  return _internal_llendtime();
}
inline void DataQueryBatchesReq::_internal_set_llendtime(uint64_t value) {
  
  _impl_.llendtime_ = value;
}
inline void DataQueryBatchesReq::set_llendtime(uint64_t value) {
  _internal_set_llendtime(value);
  // @@protoc_insertion_point(field_set:datacapture.DataQueryBatchesReq.llEndTime)
}

// uint32 dwNowPage = 4;
inline void DataQueryBatchesReq::clear_dwnowpage() {
  _impl_.dwnowpage_ = 0u;
}
inline uint32_t DataQueryBatchesReq::_internal_dwnowpage() const {
  return _impl_.dwnowpage_;
}
inline uint32_t DataQueryBatchesReq::dwnowpage() const {
  // @@protoc_insertion_point(field_get:datacapture.DataQueryBatchesReq.dwNowPage)
  return _internal_dwnowpage();
}
inline void DataQueryBatchesReq::_internal_set_dwnowpage(uint32_t value) {
  
  _impl_.dwnowpage_ = value;
}
inline void DataQueryBatchesReq::set_dwnowpage(uint32_t value) {
  _internal_set_dwnowpage(value);
  // @@protoc_insertion_point(field_set:datacapture.DataQueryBatchesReq.dwNowPage)
}

// uint32 dwPageSize = 5;
inline void DataQueryBatchesReq::clear_dwpagesize() {
  _impl_.dwpagesize_ = 0u;
}
inline uint32_t DataQueryBatchesReq::_internal_dwpagesize() const {
  return _impl_.dwpagesize_;
}
inline uint32_t DataQueryBatchesReq::dwpagesize() const {
  // @@protoc_insertion_point(field_get:datacapture.DataQueryBatchesReq.dwPageSize)
  return _internal_dwpagesize();
}
inline void DataQueryBatchesReq::_internal_set_dwpagesize(uint32_t value) {
  
  _impl_.dwpagesize_ = value;
}
inline void DataQueryBatchesReq::set_dwpagesize(uint32_t value) {
  _internal_set_dwpagesize(value);
  // @@protoc_insertion_point(field_set:datacapture.DataQueryBatchesReq.dwPageSize)
}

// -------------------------------------------------------------------

// DataQueryBatchesRes

// bool bIsSucceed = 1;
inline void DataQueryBatchesRes::clear_bissucceed() {
  _impl_.bissucceed_ = false;
}
inline bool DataQueryBatchesRes::_internal_bissucceed() const {
  return _impl_.bissucceed_;
}
inline bool DataQueryBatchesRes::bissucceed() const {
  // @@protoc_insertion_point(field_get:datacapture.DataQueryBatchesRes.bIsSucceed)
  return _internal_bissucceed();
}
inline void DataQueryBatchesRes::_internal_set_bissucceed(bool value) {
  
  _impl_.bissucceed_ = value;
}
inline void DataQueryBatchesRes::set_bissucceed(bool value) {
  _internal_set_bissucceed(value);
  // @@protoc_insertion_point(field_set:datacapture.DataQueryBatchesRes.bIsSucceed)
}

// uint32 dwNowPage = 2;
inline void DataQueryBatchesRes::clear_dwnowpage() {
  _impl_.dwnowpage_ = 0u;
}
inline uint32_t DataQueryBatchesRes::_internal_dwnowpage() const {
  return _impl_.dwnowpage_;
}
inline uint32_t DataQueryBatchesRes::dwnowpage() const {
  // @@protoc_insertion_point(field_get:datacapture.DataQueryBatchesRes.dwNowPage)
  return _internal_dwnowpage();
}
inline void DataQueryBatchesRes::_internal_set_dwnowpage(uint32_t value) {
  
  _impl_.dwnowpage_ = value;
}
inline void DataQueryBatchesRes::set_dwnowpage(uint32_t value) {
  _internal_set_dwnowpage(value);
  // @@protoc_insertion_point(field_set:datacapture.DataQueryBatchesRes.dwNowPage)
}

// uint32 dwPageSize = 3;
inline void DataQueryBatchesRes::clear_dwpagesize() {
  _impl_.dwpagesize_ = 0u;
}
inline uint32_t DataQueryBatchesRes::_internal_dwpagesize() const {
  return _impl_.dwpagesize_;
}
inline uint32_t DataQueryBatchesRes::dwpagesize() const {
  // @@protoc_insertion_point(field_get:datacapture.DataQueryBatchesRes.dwPageSize)
  return _internal_dwpagesize();
}
inline void DataQueryBatchesRes::_internal_set_dwpagesize(uint32_t value) {
  
  _impl_.dwpagesize_ = value;
}
inline void DataQueryBatchesRes::set_dwpagesize(uint32_t value) {
  _internal_set_dwpagesize(value);
  // @@protoc_insertion_point(field_set:datacapture.DataQueryBatchesRes.dwPageSize)
}

// uint32 dwTotalPages = 4;
inline void DataQueryBatchesRes::clear_dwtotalpages() {
  _impl_.dwtotalpages_ = 0u;
}
inline uint32_t DataQueryBatchesRes::_internal_dwtotalpages() const {
  return _impl_.dwtotalpages_;
}
inline uint32_t DataQueryBatchesRes::dwtotalpages() const {
  // @@protoc_insertion_point(field_get:datacapture.DataQueryBatchesRes.dwTotalPages)
  return _internal_dwtotalpages();
}
inline void DataQueryBatchesRes::_internal_set_dwtotalpages(uint32_t value) {
  
  _impl_.dwtotalpages_ = value;
}
inline void DataQueryBatchesRes::set_dwtotalpages(uint32_t value) {
  _internal_set_dwtotalpages(value);
  // @@protoc_insertion_point(field_set:datacapture.DataQueryBatchesRes.dwTotalPages)
}

// uint32 dwTotalDatas = 5;
inline void DataQueryBatchesRes::clear_dwtotaldatas() {
  _impl_.dwtotaldatas_ = 0u;
}
inline uint32_t DataQueryBatchesRes::_internal_dwtotaldatas() const {
  return _impl_.dwtotaldatas_;
}
inline uint32_t DataQueryBatchesRes::dwtotaldatas() const {
  // @@protoc_insertion_point(field_get:datacapture.DataQueryBatchesRes.dwTotalDatas)
  return _internal_dwtotaldatas();
}
inline void DataQueryBatchesRes::_internal_set_dwtotaldatas(uint32_t value) {
  
  _impl_.dwtotaldatas_ = value;
}
inline void DataQueryBatchesRes::set_dwtotaldatas(uint32_t value) {
  _internal_set_dwtotaldatas(value);
  // @@protoc_insertion_point(field_set:datacapture.DataQueryBatchesRes.dwTotalDatas)
}

// string strErr = 6;
inline void DataQueryBatchesRes::clear_strerr() {
  _impl_.strerr_.ClearToEmpty();
}
inline const std::string& DataQueryBatchesRes::strerr() const {
  // @@protoc_insertion_point(field_get:datacapture.DataQueryBatchesRes.strErr)
  return _internal_strerr();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DataQueryBatchesRes::set_strerr(ArgT0&& arg0, ArgT... args) {
 
 _impl_.strerr_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:datacapture.DataQueryBatchesRes.strErr)
}
inline std::string* DataQueryBatchesRes::mutable_strerr() {
  std::string* _s = _internal_mutable_strerr();
  // @@protoc_insertion_point(field_mutable:datacapture.DataQueryBatchesRes.strErr)
  return _s;
}
inline const std::string& DataQueryBatchesRes::_internal_strerr() const {
  return _impl_.strerr_.Get();
}
inline void DataQueryBatchesRes::_internal_set_strerr(const std::string& value) {
  
  _impl_.strerr_.Set(value, GetArenaForAllocation());
}
inline std::string* DataQueryBatchesRes::_internal_mutable_strerr() {
  
  return _impl_.strerr_.Mutable(GetArenaForAllocation());
}
inline std::string* DataQueryBatchesRes::release_strerr() {
  // @@protoc_insertion_point(field_release:datacapture.DataQueryBatchesRes.strErr)
  return _impl_.strerr_.Release();
}
inline void DataQueryBatchesRes::set_allocated_strerr(std::string* strerr) {
  if (strerr != nullptr) {
    
  } else {
    
  }
  _impl_.strerr_.SetAllocated(strerr, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.strerr_.IsDefault()) {
    _impl_.strerr_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:datacapture.DataQueryBatchesRes.strErr)
}

// uint32 dwCnt = 7;
inline void DataQueryBatchesRes::clear_dwcnt() {
  _impl_.dwcnt_ = 0u;
}
inline uint32_t DataQueryBatchesRes::_internal_dwcnt() const {
  return _impl_.dwcnt_;
}
inline uint32_t DataQueryBatchesRes::dwcnt() const {
  // @@protoc_insertion_point(field_get:datacapture.DataQueryBatchesRes.dwCnt)
  return _internal_dwcnt();
}
inline void DataQueryBatchesRes::_internal_set_dwcnt(uint32_t value) {
  
  _impl_.dwcnt_ = value;
}
inline void DataQueryBatchesRes::set_dwcnt(uint32_t value) {
  _internal_set_dwcnt(value);
  // @@protoc_insertion_point(field_set:datacapture.DataQueryBatchesRes.dwCnt)
}

// repeated .datacapture.RawData dataList = 8;
inline int DataQueryBatchesRes::_internal_datalist_size() const {
  return _impl_.datalist_.size();
}
inline int DataQueryBatchesRes::datalist_size() const {
  return _internal_datalist_size();
}
inline void DataQueryBatchesRes::clear_datalist() {
  _impl_.datalist_.Clear();
}
inline ::datacapture::RawData* DataQueryBatchesRes::mutable_datalist(int index) {
  // @@protoc_insertion_point(field_mutable:datacapture.DataQueryBatchesRes.dataList)
  return _impl_.datalist_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::datacapture::RawData >*
DataQueryBatchesRes::mutable_datalist() {
  // @@protoc_insertion_point(field_mutable_list:datacapture.DataQueryBatchesRes.dataList)
  return &_impl_.datalist_;
}
inline const ::datacapture::RawData& DataQueryBatchesRes::_internal_datalist(int index) const {
  return _impl_.datalist_.Get(index);
}
inline const ::datacapture::RawData& DataQueryBatchesRes::datalist(int index) const {
  // @@protoc_insertion_point(field_get:datacapture.DataQueryBatchesRes.dataList)
  return _internal_datalist(index);
}
inline ::datacapture::RawData* DataQueryBatchesRes::_internal_add_datalist() {
  return _impl_.datalist_.Add();
}
inline ::datacapture::RawData* DataQueryBatchesRes::add_datalist() {
  ::datacapture::RawData* _add = _internal_add_datalist();
  // @@protoc_insertion_point(field_add:datacapture.DataQueryBatchesRes.dataList)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::datacapture::RawData >&
DataQueryBatchesRes::datalist() const {
  // @@protoc_insertion_point(field_list:datacapture.DataQueryBatchesRes.dataList)
  return _impl_.datalist_;
}

// -------------------------------------------------------------------

// CompressReqList

// uint32 compressType = 1;
inline void CompressReqList::clear_compresstype() {
  _impl_.compresstype_ = 0u;
}
inline uint32_t CompressReqList::_internal_compresstype() const {
  return _impl_.compresstype_;
}
inline uint32_t CompressReqList::compresstype() const {
  // @@protoc_insertion_point(field_get:datacapture.CompressReqList.compressType)
  return _internal_compresstype();
}
inline void CompressReqList::_internal_set_compresstype(uint32_t value) {
  
  _impl_.compresstype_ = value;
}
inline void CompressReqList::set_compresstype(uint32_t value) {
  _internal_set_compresstype(value);
  // @@protoc_insertion_point(field_set:datacapture.CompressReqList.compressType)
}

// uint32 dwCnt = 2;
inline void CompressReqList::clear_dwcnt() {
  _impl_.dwcnt_ = 0u;
}
inline uint32_t CompressReqList::_internal_dwcnt() const {
  return _impl_.dwcnt_;
}
inline uint32_t CompressReqList::dwcnt() const {
  // @@protoc_insertion_point(field_get:datacapture.CompressReqList.dwCnt)
  return _internal_dwcnt();
}
inline void CompressReqList::_internal_set_dwcnt(uint32_t value) {
  
  _impl_.dwcnt_ = value;
}
inline void CompressReqList::set_dwcnt(uint32_t value) {
  _internal_set_dwcnt(value);
  // @@protoc_insertion_point(field_set:datacapture.CompressReqList.dwCnt)
}

// string strHttpUrl = 3;
inline void CompressReqList::clear_strhttpurl() {
  _impl_.strhttpurl_.ClearToEmpty();
}
inline const std::string& CompressReqList::strhttpurl() const {
  // @@protoc_insertion_point(field_get:datacapture.CompressReqList.strHttpUrl)
  return _internal_strhttpurl();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CompressReqList::set_strhttpurl(ArgT0&& arg0, ArgT... args) {
 
 _impl_.strhttpurl_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:datacapture.CompressReqList.strHttpUrl)
}
inline std::string* CompressReqList::mutable_strhttpurl() {
  std::string* _s = _internal_mutable_strhttpurl();
  // @@protoc_insertion_point(field_mutable:datacapture.CompressReqList.strHttpUrl)
  return _s;
}
inline const std::string& CompressReqList::_internal_strhttpurl() const {
  return _impl_.strhttpurl_.Get();
}
inline void CompressReqList::_internal_set_strhttpurl(const std::string& value) {
  
  _impl_.strhttpurl_.Set(value, GetArenaForAllocation());
}
inline std::string* CompressReqList::_internal_mutable_strhttpurl() {
  
  return _impl_.strhttpurl_.Mutable(GetArenaForAllocation());
}
inline std::string* CompressReqList::release_strhttpurl() {
  // @@protoc_insertion_point(field_release:datacapture.CompressReqList.strHttpUrl)
  return _impl_.strhttpurl_.Release();
}
inline void CompressReqList::set_allocated_strhttpurl(std::string* strhttpurl) {
  if (strhttpurl != nullptr) {
    
  } else {
    
  }
  _impl_.strhttpurl_.SetAllocated(strhttpurl, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.strhttpurl_.IsDefault()) {
    _impl_.strhttpurl_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:datacapture.CompressReqList.strHttpUrl)
}

// string strPackageName = 4;
inline void CompressReqList::clear_strpackagename() {
  _impl_.strpackagename_.ClearToEmpty();
}
inline const std::string& CompressReqList::strpackagename() const {
  // @@protoc_insertion_point(field_get:datacapture.CompressReqList.strPackageName)
  return _internal_strpackagename();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CompressReqList::set_strpackagename(ArgT0&& arg0, ArgT... args) {
 
 _impl_.strpackagename_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:datacapture.CompressReqList.strPackageName)
}
inline std::string* CompressReqList::mutable_strpackagename() {
  std::string* _s = _internal_mutable_strpackagename();
  // @@protoc_insertion_point(field_mutable:datacapture.CompressReqList.strPackageName)
  return _s;
}
inline const std::string& CompressReqList::_internal_strpackagename() const {
  return _impl_.strpackagename_.Get();
}
inline void CompressReqList::_internal_set_strpackagename(const std::string& value) {
  
  _impl_.strpackagename_.Set(value, GetArenaForAllocation());
}
inline std::string* CompressReqList::_internal_mutable_strpackagename() {
  
  return _impl_.strpackagename_.Mutable(GetArenaForAllocation());
}
inline std::string* CompressReqList::release_strpackagename() {
  // @@protoc_insertion_point(field_release:datacapture.CompressReqList.strPackageName)
  return _impl_.strpackagename_.Release();
}
inline void CompressReqList::set_allocated_strpackagename(std::string* strpackagename) {
  if (strpackagename != nullptr) {
    
  } else {
    
  }
  _impl_.strpackagename_.SetAllocated(strpackagename, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.strpackagename_.IsDefault()) {
    _impl_.strpackagename_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:datacapture.CompressReqList.strPackageName)
}

// string strSerialNum = 5;
inline void CompressReqList::clear_strserialnum() {
  _impl_.strserialnum_.ClearToEmpty();
}
inline const std::string& CompressReqList::strserialnum() const {
  // @@protoc_insertion_point(field_get:datacapture.CompressReqList.strSerialNum)
  return _internal_strserialnum();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CompressReqList::set_strserialnum(ArgT0&& arg0, ArgT... args) {
 
 _impl_.strserialnum_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:datacapture.CompressReqList.strSerialNum)
}
inline std::string* CompressReqList::mutable_strserialnum() {
  std::string* _s = _internal_mutable_strserialnum();
  // @@protoc_insertion_point(field_mutable:datacapture.CompressReqList.strSerialNum)
  return _s;
}
inline const std::string& CompressReqList::_internal_strserialnum() const {
  return _impl_.strserialnum_.Get();
}
inline void CompressReqList::_internal_set_strserialnum(const std::string& value) {
  
  _impl_.strserialnum_.Set(value, GetArenaForAllocation());
}
inline std::string* CompressReqList::_internal_mutable_strserialnum() {
  
  return _impl_.strserialnum_.Mutable(GetArenaForAllocation());
}
inline std::string* CompressReqList::release_strserialnum() {
  // @@protoc_insertion_point(field_release:datacapture.CompressReqList.strSerialNum)
  return _impl_.strserialnum_.Release();
}
inline void CompressReqList::set_allocated_strserialnum(std::string* strserialnum) {
  if (strserialnum != nullptr) {
    
  } else {
    
  }
  _impl_.strserialnum_.SetAllocated(strserialnum, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.strserialnum_.IsDefault()) {
    _impl_.strserialnum_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:datacapture.CompressReqList.strSerialNum)
}

// repeated .datacapture.DataQuery queryList = 6;
inline int CompressReqList::_internal_querylist_size() const {
  return _impl_.querylist_.size();
}
inline int CompressReqList::querylist_size() const {
  return _internal_querylist_size();
}
inline void CompressReqList::clear_querylist() {
  _impl_.querylist_.Clear();
}
inline ::datacapture::DataQuery* CompressReqList::mutable_querylist(int index) {
  // @@protoc_insertion_point(field_mutable:datacapture.CompressReqList.queryList)
  return _impl_.querylist_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::datacapture::DataQuery >*
CompressReqList::mutable_querylist() {
  // @@protoc_insertion_point(field_mutable_list:datacapture.CompressReqList.queryList)
  return &_impl_.querylist_;
}
inline const ::datacapture::DataQuery& CompressReqList::_internal_querylist(int index) const {
  return _impl_.querylist_.Get(index);
}
inline const ::datacapture::DataQuery& CompressReqList::querylist(int index) const {
  // @@protoc_insertion_point(field_get:datacapture.CompressReqList.queryList)
  return _internal_querylist(index);
}
inline ::datacapture::DataQuery* CompressReqList::_internal_add_querylist() {
  return _impl_.querylist_.Add();
}
inline ::datacapture::DataQuery* CompressReqList::add_querylist() {
  ::datacapture::DataQuery* _add = _internal_add_querylist();
  // @@protoc_insertion_point(field_add:datacapture.CompressReqList.queryList)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::datacapture::DataQuery >&
CompressReqList::querylist() const {
  // @@protoc_insertion_point(field_list:datacapture.CompressReqList.queryList)
  return _impl_.querylist_;
}

// -------------------------------------------------------------------

// CompressRes

// uint32 compressType = 1;
inline void CompressRes::clear_compresstype() {
  _impl_.compresstype_ = 0u;
}
inline uint32_t CompressRes::_internal_compresstype() const {
  return _impl_.compresstype_;
}
inline uint32_t CompressRes::compresstype() const {
  // @@protoc_insertion_point(field_get:datacapture.CompressRes.compressType)
  return _internal_compresstype();
}
inline void CompressRes::_internal_set_compresstype(uint32_t value) {
  
  _impl_.compresstype_ = value;
}
inline void CompressRes::set_compresstype(uint32_t value) {
  _internal_set_compresstype(value);
  // @@protoc_insertion_point(field_set:datacapture.CompressRes.compressType)
}

// bool bIsSucceed = 2;
inline void CompressRes::clear_bissucceed() {
  _impl_.bissucceed_ = false;
}
inline bool CompressRes::_internal_bissucceed() const {
  return _impl_.bissucceed_;
}
inline bool CompressRes::bissucceed() const {
  // @@protoc_insertion_point(field_get:datacapture.CompressRes.bIsSucceed)
  return _internal_bissucceed();
}
inline void CompressRes::_internal_set_bissucceed(bool value) {
  
  _impl_.bissucceed_ = value;
}
inline void CompressRes::set_bissucceed(bool value) {
  _internal_set_bissucceed(value);
  // @@protoc_insertion_point(field_set:datacapture.CompressRes.bIsSucceed)
}

// string filepath = 3;
inline void CompressRes::clear_filepath() {
  _impl_.filepath_.ClearToEmpty();
}
inline const std::string& CompressRes::filepath() const {
  // @@protoc_insertion_point(field_get:datacapture.CompressRes.filepath)
  return _internal_filepath();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CompressRes::set_filepath(ArgT0&& arg0, ArgT... args) {
 
 _impl_.filepath_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:datacapture.CompressRes.filepath)
}
inline std::string* CompressRes::mutable_filepath() {
  std::string* _s = _internal_mutable_filepath();
  // @@protoc_insertion_point(field_mutable:datacapture.CompressRes.filepath)
  return _s;
}
inline const std::string& CompressRes::_internal_filepath() const {
  return _impl_.filepath_.Get();
}
inline void CompressRes::_internal_set_filepath(const std::string& value) {
  
  _impl_.filepath_.Set(value, GetArenaForAllocation());
}
inline std::string* CompressRes::_internal_mutable_filepath() {
  
  return _impl_.filepath_.Mutable(GetArenaForAllocation());
}
inline std::string* CompressRes::release_filepath() {
  // @@protoc_insertion_point(field_release:datacapture.CompressRes.filepath)
  return _impl_.filepath_.Release();
}
inline void CompressRes::set_allocated_filepath(std::string* filepath) {
  if (filepath != nullptr) {
    
  } else {
    
  }
  _impl_.filepath_.SetAllocated(filepath, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.filepath_.IsDefault()) {
    _impl_.filepath_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:datacapture.CompressRes.filepath)
}

// string strErr = 4;
inline void CompressRes::clear_strerr() {
  _impl_.strerr_.ClearToEmpty();
}
inline const std::string& CompressRes::strerr() const {
  // @@protoc_insertion_point(field_get:datacapture.CompressRes.strErr)
  return _internal_strerr();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CompressRes::set_strerr(ArgT0&& arg0, ArgT... args) {
 
 _impl_.strerr_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:datacapture.CompressRes.strErr)
}
inline std::string* CompressRes::mutable_strerr() {
  std::string* _s = _internal_mutable_strerr();
  // @@protoc_insertion_point(field_mutable:datacapture.CompressRes.strErr)
  return _s;
}
inline const std::string& CompressRes::_internal_strerr() const {
  return _impl_.strerr_.Get();
}
inline void CompressRes::_internal_set_strerr(const std::string& value) {
  
  _impl_.strerr_.Set(value, GetArenaForAllocation());
}
inline std::string* CompressRes::_internal_mutable_strerr() {
  
  return _impl_.strerr_.Mutable(GetArenaForAllocation());
}
inline std::string* CompressRes::release_strerr() {
  // @@protoc_insertion_point(field_release:datacapture.CompressRes.strErr)
  return _impl_.strerr_.Release();
}
inline void CompressRes::set_allocated_strerr(std::string* strerr) {
  if (strerr != nullptr) {
    
  } else {
    
  }
  _impl_.strerr_.SetAllocated(strerr, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.strerr_.IsDefault()) {
    _impl_.strerr_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:datacapture.CompressRes.strErr)
}

// string strSerialNum = 5;
inline void CompressRes::clear_strserialnum() {
  _impl_.strserialnum_.ClearToEmpty();
}
inline const std::string& CompressRes::strserialnum() const {
  // @@protoc_insertion_point(field_get:datacapture.CompressRes.strSerialNum)
  return _internal_strserialnum();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CompressRes::set_strserialnum(ArgT0&& arg0, ArgT... args) {
 
 _impl_.strserialnum_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:datacapture.CompressRes.strSerialNum)
}
inline std::string* CompressRes::mutable_strserialnum() {
  std::string* _s = _internal_mutable_strserialnum();
  // @@protoc_insertion_point(field_mutable:datacapture.CompressRes.strSerialNum)
  return _s;
}
inline const std::string& CompressRes::_internal_strserialnum() const {
  return _impl_.strserialnum_.Get();
}
inline void CompressRes::_internal_set_strserialnum(const std::string& value) {
  
  _impl_.strserialnum_.Set(value, GetArenaForAllocation());
}
inline std::string* CompressRes::_internal_mutable_strserialnum() {
  
  return _impl_.strserialnum_.Mutable(GetArenaForAllocation());
}
inline std::string* CompressRes::release_strserialnum() {
  // @@protoc_insertion_point(field_release:datacapture.CompressRes.strSerialNum)
  return _impl_.strserialnum_.Release();
}
inline void CompressRes::set_allocated_strserialnum(std::string* strserialnum) {
  if (strserialnum != nullptr) {
    
  } else {
    
  }
  _impl_.strserialnum_.SetAllocated(strserialnum, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.strserialnum_.IsDefault()) {
    _impl_.strserialnum_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:datacapture.CompressRes.strSerialNum)
}

// string strPackageName = 6;
inline void CompressRes::clear_strpackagename() {
  _impl_.strpackagename_.ClearToEmpty();
}
inline const std::string& CompressRes::strpackagename() const {
  // @@protoc_insertion_point(field_get:datacapture.CompressRes.strPackageName)
  return _internal_strpackagename();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CompressRes::set_strpackagename(ArgT0&& arg0, ArgT... args) {
 
 _impl_.strpackagename_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:datacapture.CompressRes.strPackageName)
}
inline std::string* CompressRes::mutable_strpackagename() {
  std::string* _s = _internal_mutable_strpackagename();
  // @@protoc_insertion_point(field_mutable:datacapture.CompressRes.strPackageName)
  return _s;
}
inline const std::string& CompressRes::_internal_strpackagename() const {
  return _impl_.strpackagename_.Get();
}
inline void CompressRes::_internal_set_strpackagename(const std::string& value) {
  
  _impl_.strpackagename_.Set(value, GetArenaForAllocation());
}
inline std::string* CompressRes::_internal_mutable_strpackagename() {
  
  return _impl_.strpackagename_.Mutable(GetArenaForAllocation());
}
inline std::string* CompressRes::release_strpackagename() {
  // @@protoc_insertion_point(field_release:datacapture.CompressRes.strPackageName)
  return _impl_.strpackagename_.Release();
}
inline void CompressRes::set_allocated_strpackagename(std::string* strpackagename) {
  if (strpackagename != nullptr) {
    
  } else {
    
  }
  _impl_.strpackagename_.SetAllocated(strpackagename, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.strpackagename_.IsDefault()) {
    _impl_.strpackagename_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:datacapture.CompressRes.strPackageName)
}

// -------------------------------------------------------------------

// ConnectArgs

// uint32 dwNo = 1;
inline void ConnectArgs::clear_dwno() {
  _impl_.dwno_ = 0u;
}
inline uint32_t ConnectArgs::_internal_dwno() const {
  return _impl_.dwno_;
}
inline uint32_t ConnectArgs::dwno() const {
  // @@protoc_insertion_point(field_get:datacapture.ConnectArgs.dwNo)
  return _internal_dwno();
}
inline void ConnectArgs::_internal_set_dwno(uint32_t value) {
  
  _impl_.dwno_ = value;
}
inline void ConnectArgs::set_dwno(uint32_t value) {
  _internal_set_dwno(value);
  // @@protoc_insertion_point(field_set:datacapture.ConnectArgs.dwNo)
}

// bool isEnable = 2;
inline void ConnectArgs::clear_isenable() {
  _impl_.isenable_ = false;
}
inline bool ConnectArgs::_internal_isenable() const {
  return _impl_.isenable_;
}
inline bool ConnectArgs::isenable() const {
  // @@protoc_insertion_point(field_get:datacapture.ConnectArgs.isEnable)
  return _internal_isenable();
}
inline void ConnectArgs::_internal_set_isenable(bool value) {
  
  _impl_.isenable_ = value;
}
inline void ConnectArgs::set_isenable(bool value) {
  _internal_set_isenable(value);
  // @@protoc_insertion_point(field_set:datacapture.ConnectArgs.isEnable)
}

// .datacapture.ConnectArgs.ConnectType type = 3;
inline void ConnectArgs::clear_type() {
  _impl_.type_ = 0;
}
inline ::datacapture::ConnectArgs_ConnectType ConnectArgs::_internal_type() const {
  return static_cast< ::datacapture::ConnectArgs_ConnectType >(_impl_.type_);
}
inline ::datacapture::ConnectArgs_ConnectType ConnectArgs::type() const {
  // @@protoc_insertion_point(field_get:datacapture.ConnectArgs.type)
  return _internal_type();
}
inline void ConnectArgs::_internal_set_type(::datacapture::ConnectArgs_ConnectType value) {
  
  _impl_.type_ = value;
}
inline void ConnectArgs::set_type(::datacapture::ConnectArgs_ConnectType value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:datacapture.ConnectArgs.type)
}

// string strCrossroadId = 4;
inline void ConnectArgs::clear_strcrossroadid() {
  _impl_.strcrossroadid_.ClearToEmpty();
}
inline const std::string& ConnectArgs::strcrossroadid() const {
  // @@protoc_insertion_point(field_get:datacapture.ConnectArgs.strCrossroadId)
  return _internal_strcrossroadid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ConnectArgs::set_strcrossroadid(ArgT0&& arg0, ArgT... args) {
 
 _impl_.strcrossroadid_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:datacapture.ConnectArgs.strCrossroadId)
}
inline std::string* ConnectArgs::mutable_strcrossroadid() {
  std::string* _s = _internal_mutable_strcrossroadid();
  // @@protoc_insertion_point(field_mutable:datacapture.ConnectArgs.strCrossroadId)
  return _s;
}
inline const std::string& ConnectArgs::_internal_strcrossroadid() const {
  return _impl_.strcrossroadid_.Get();
}
inline void ConnectArgs::_internal_set_strcrossroadid(const std::string& value) {
  
  _impl_.strcrossroadid_.Set(value, GetArenaForAllocation());
}
inline std::string* ConnectArgs::_internal_mutable_strcrossroadid() {
  
  return _impl_.strcrossroadid_.Mutable(GetArenaForAllocation());
}
inline std::string* ConnectArgs::release_strcrossroadid() {
  // @@protoc_insertion_point(field_release:datacapture.ConnectArgs.strCrossroadId)
  return _impl_.strcrossroadid_.Release();
}
inline void ConnectArgs::set_allocated_strcrossroadid(std::string* strcrossroadid) {
  if (strcrossroadid != nullptr) {
    
  } else {
    
  }
  _impl_.strcrossroadid_.SetAllocated(strcrossroadid, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.strcrossroadid_.IsDefault()) {
    _impl_.strcrossroadid_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:datacapture.ConnectArgs.strCrossroadId)
}

// string strTopice = 5;
inline void ConnectArgs::clear_strtopice() {
  _impl_.strtopice_.ClearToEmpty();
}
inline const std::string& ConnectArgs::strtopice() const {
  // @@protoc_insertion_point(field_get:datacapture.ConnectArgs.strTopice)
  return _internal_strtopice();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ConnectArgs::set_strtopice(ArgT0&& arg0, ArgT... args) {
 
 _impl_.strtopice_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:datacapture.ConnectArgs.strTopice)
}
inline std::string* ConnectArgs::mutable_strtopice() {
  std::string* _s = _internal_mutable_strtopice();
  // @@protoc_insertion_point(field_mutable:datacapture.ConnectArgs.strTopice)
  return _s;
}
inline const std::string& ConnectArgs::_internal_strtopice() const {
  return _impl_.strtopice_.Get();
}
inline void ConnectArgs::_internal_set_strtopice(const std::string& value) {
  
  _impl_.strtopice_.Set(value, GetArenaForAllocation());
}
inline std::string* ConnectArgs::_internal_mutable_strtopice() {
  
  return _impl_.strtopice_.Mutable(GetArenaForAllocation());
}
inline std::string* ConnectArgs::release_strtopice() {
  // @@protoc_insertion_point(field_release:datacapture.ConnectArgs.strTopice)
  return _impl_.strtopice_.Release();
}
inline void ConnectArgs::set_allocated_strtopice(std::string* strtopice) {
  if (strtopice != nullptr) {
    
  } else {
    
  }
  _impl_.strtopice_.SetAllocated(strtopice, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.strtopice_.IsDefault()) {
    _impl_.strtopice_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:datacapture.ConnectArgs.strTopice)
}

// string strPassword = 6;
inline void ConnectArgs::clear_strpassword() {
  _impl_.strpassword_.ClearToEmpty();
}
inline const std::string& ConnectArgs::strpassword() const {
  // @@protoc_insertion_point(field_get:datacapture.ConnectArgs.strPassword)
  return _internal_strpassword();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ConnectArgs::set_strpassword(ArgT0&& arg0, ArgT... args) {
 
 _impl_.strpassword_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:datacapture.ConnectArgs.strPassword)
}
inline std::string* ConnectArgs::mutable_strpassword() {
  std::string* _s = _internal_mutable_strpassword();
  // @@protoc_insertion_point(field_mutable:datacapture.ConnectArgs.strPassword)
  return _s;
}
inline const std::string& ConnectArgs::_internal_strpassword() const {
  return _impl_.strpassword_.Get();
}
inline void ConnectArgs::_internal_set_strpassword(const std::string& value) {
  
  _impl_.strpassword_.Set(value, GetArenaForAllocation());
}
inline std::string* ConnectArgs::_internal_mutable_strpassword() {
  
  return _impl_.strpassword_.Mutable(GetArenaForAllocation());
}
inline std::string* ConnectArgs::release_strpassword() {
  // @@protoc_insertion_point(field_release:datacapture.ConnectArgs.strPassword)
  return _impl_.strpassword_.Release();
}
inline void ConnectArgs::set_allocated_strpassword(std::string* strpassword) {
  if (strpassword != nullptr) {
    
  } else {
    
  }
  _impl_.strpassword_.SetAllocated(strpassword, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.strpassword_.IsDefault()) {
    _impl_.strpassword_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:datacapture.ConnectArgs.strPassword)
}

// string strClientId = 7;
inline void ConnectArgs::clear_strclientid() {
  _impl_.strclientid_.ClearToEmpty();
}
inline const std::string& ConnectArgs::strclientid() const {
  // @@protoc_insertion_point(field_get:datacapture.ConnectArgs.strClientId)
  return _internal_strclientid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ConnectArgs::set_strclientid(ArgT0&& arg0, ArgT... args) {
 
 _impl_.strclientid_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:datacapture.ConnectArgs.strClientId)
}
inline std::string* ConnectArgs::mutable_strclientid() {
  std::string* _s = _internal_mutable_strclientid();
  // @@protoc_insertion_point(field_mutable:datacapture.ConnectArgs.strClientId)
  return _s;
}
inline const std::string& ConnectArgs::_internal_strclientid() const {
  return _impl_.strclientid_.Get();
}
inline void ConnectArgs::_internal_set_strclientid(const std::string& value) {
  
  _impl_.strclientid_.Set(value, GetArenaForAllocation());
}
inline std::string* ConnectArgs::_internal_mutable_strclientid() {
  
  return _impl_.strclientid_.Mutable(GetArenaForAllocation());
}
inline std::string* ConnectArgs::release_strclientid() {
  // @@protoc_insertion_point(field_release:datacapture.ConnectArgs.strClientId)
  return _impl_.strclientid_.Release();
}
inline void ConnectArgs::set_allocated_strclientid(std::string* strclientid) {
  if (strclientid != nullptr) {
    
  } else {
    
  }
  _impl_.strclientid_.SetAllocated(strclientid, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.strclientid_.IsDefault()) {
    _impl_.strclientid_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:datacapture.ConnectArgs.strClientId)
}

// string strAddr = 8;
inline void ConnectArgs::clear_straddr() {
  _impl_.straddr_.ClearToEmpty();
}
inline const std::string& ConnectArgs::straddr() const {
  // @@protoc_insertion_point(field_get:datacapture.ConnectArgs.strAddr)
  return _internal_straddr();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ConnectArgs::set_straddr(ArgT0&& arg0, ArgT... args) {
 
 _impl_.straddr_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:datacapture.ConnectArgs.strAddr)
}
inline std::string* ConnectArgs::mutable_straddr() {
  std::string* _s = _internal_mutable_straddr();
  // @@protoc_insertion_point(field_mutable:datacapture.ConnectArgs.strAddr)
  return _s;
}
inline const std::string& ConnectArgs::_internal_straddr() const {
  return _impl_.straddr_.Get();
}
inline void ConnectArgs::_internal_set_straddr(const std::string& value) {
  
  _impl_.straddr_.Set(value, GetArenaForAllocation());
}
inline std::string* ConnectArgs::_internal_mutable_straddr() {
  
  return _impl_.straddr_.Mutable(GetArenaForAllocation());
}
inline std::string* ConnectArgs::release_straddr() {
  // @@protoc_insertion_point(field_release:datacapture.ConnectArgs.strAddr)
  return _impl_.straddr_.Release();
}
inline void ConnectArgs::set_allocated_straddr(std::string* straddr) {
  if (straddr != nullptr) {
    
  } else {
    
  }
  _impl_.straddr_.SetAllocated(straddr, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.straddr_.IsDefault()) {
    _impl_.straddr_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:datacapture.ConnectArgs.strAddr)
}

// string strUsername = 9;
inline void ConnectArgs::clear_strusername() {
  _impl_.strusername_.ClearToEmpty();
}
inline const std::string& ConnectArgs::strusername() const {
  // @@protoc_insertion_point(field_get:datacapture.ConnectArgs.strUsername)
  return _internal_strusername();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ConnectArgs::set_strusername(ArgT0&& arg0, ArgT... args) {
 
 _impl_.strusername_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:datacapture.ConnectArgs.strUsername)
}
inline std::string* ConnectArgs::mutable_strusername() {
  std::string* _s = _internal_mutable_strusername();
  // @@protoc_insertion_point(field_mutable:datacapture.ConnectArgs.strUsername)
  return _s;
}
inline const std::string& ConnectArgs::_internal_strusername() const {
  return _impl_.strusername_.Get();
}
inline void ConnectArgs::_internal_set_strusername(const std::string& value) {
  
  _impl_.strusername_.Set(value, GetArenaForAllocation());
}
inline std::string* ConnectArgs::_internal_mutable_strusername() {
  
  return _impl_.strusername_.Mutable(GetArenaForAllocation());
}
inline std::string* ConnectArgs::release_strusername() {
  // @@protoc_insertion_point(field_release:datacapture.ConnectArgs.strUsername)
  return _impl_.strusername_.Release();
}
inline void ConnectArgs::set_allocated_strusername(std::string* strusername) {
  if (strusername != nullptr) {
    
  } else {
    
  }
  _impl_.strusername_.SetAllocated(strusername, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.strusername_.IsDefault()) {
    _impl_.strusername_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:datacapture.ConnectArgs.strUsername)
}

// string strDescribe = 10;
inline void ConnectArgs::clear_strdescribe() {
  _impl_.strdescribe_.ClearToEmpty();
}
inline const std::string& ConnectArgs::strdescribe() const {
  // @@protoc_insertion_point(field_get:datacapture.ConnectArgs.strDescribe)
  return _internal_strdescribe();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ConnectArgs::set_strdescribe(ArgT0&& arg0, ArgT... args) {
 
 _impl_.strdescribe_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:datacapture.ConnectArgs.strDescribe)
}
inline std::string* ConnectArgs::mutable_strdescribe() {
  std::string* _s = _internal_mutable_strdescribe();
  // @@protoc_insertion_point(field_mutable:datacapture.ConnectArgs.strDescribe)
  return _s;
}
inline const std::string& ConnectArgs::_internal_strdescribe() const {
  return _impl_.strdescribe_.Get();
}
inline void ConnectArgs::_internal_set_strdescribe(const std::string& value) {
  
  _impl_.strdescribe_.Set(value, GetArenaForAllocation());
}
inline std::string* ConnectArgs::_internal_mutable_strdescribe() {
  
  return _impl_.strdescribe_.Mutable(GetArenaForAllocation());
}
inline std::string* ConnectArgs::release_strdescribe() {
  // @@protoc_insertion_point(field_release:datacapture.ConnectArgs.strDescribe)
  return _impl_.strdescribe_.Release();
}
inline void ConnectArgs::set_allocated_strdescribe(std::string* strdescribe) {
  if (strdescribe != nullptr) {
    
  } else {
    
  }
  _impl_.strdescribe_.SetAllocated(strdescribe, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.strdescribe_.IsDefault()) {
    _impl_.strdescribe_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:datacapture.ConnectArgs.strDescribe)
}

// uint32 dwFactory = 11;
inline void ConnectArgs::clear_dwfactory() {
  _impl_.dwfactory_ = 0u;
}
inline uint32_t ConnectArgs::_internal_dwfactory() const {
  return _impl_.dwfactory_;
}
inline uint32_t ConnectArgs::dwfactory() const {
  // @@protoc_insertion_point(field_get:datacapture.ConnectArgs.dwFactory)
  return _internal_dwfactory();
}
inline void ConnectArgs::_internal_set_dwfactory(uint32_t value) {
  
  _impl_.dwfactory_ = value;
}
inline void ConnectArgs::set_dwfactory(uint32_t value) {
  _internal_set_dwfactory(value);
  // @@protoc_insertion_point(field_set:datacapture.ConnectArgs.dwFactory)
}

// -------------------------------------------------------------------

// ArgsList

// uint32 dwCnt = 1;
inline void ArgsList::clear_dwcnt() {
  _impl_.dwcnt_ = 0u;
}
inline uint32_t ArgsList::_internal_dwcnt() const {
  return _impl_.dwcnt_;
}
inline uint32_t ArgsList::dwcnt() const {
  // @@protoc_insertion_point(field_get:datacapture.ArgsList.dwCnt)
  return _internal_dwcnt();
}
inline void ArgsList::_internal_set_dwcnt(uint32_t value) {
  
  _impl_.dwcnt_ = value;
}
inline void ArgsList::set_dwcnt(uint32_t value) {
  _internal_set_dwcnt(value);
  // @@protoc_insertion_point(field_set:datacapture.ArgsList.dwCnt)
}

// repeated .datacapture.ConnectArgs argsList = 2;
inline int ArgsList::_internal_argslist_size() const {
  return _impl_.argslist_.size();
}
inline int ArgsList::argslist_size() const {
  return _internal_argslist_size();
}
inline void ArgsList::clear_argslist() {
  _impl_.argslist_.Clear();
}
inline ::datacapture::ConnectArgs* ArgsList::mutable_argslist(int index) {
  // @@protoc_insertion_point(field_mutable:datacapture.ArgsList.argsList)
  return _impl_.argslist_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::datacapture::ConnectArgs >*
ArgsList::mutable_argslist() {
  // @@protoc_insertion_point(field_mutable_list:datacapture.ArgsList.argsList)
  return &_impl_.argslist_;
}
inline const ::datacapture::ConnectArgs& ArgsList::_internal_argslist(int index) const {
  return _impl_.argslist_.Get(index);
}
inline const ::datacapture::ConnectArgs& ArgsList::argslist(int index) const {
  // @@protoc_insertion_point(field_get:datacapture.ArgsList.argsList)
  return _internal_argslist(index);
}
inline ::datacapture::ConnectArgs* ArgsList::_internal_add_argslist() {
  return _impl_.argslist_.Add();
}
inline ::datacapture::ConnectArgs* ArgsList::add_argslist() {
  ::datacapture::ConnectArgs* _add = _internal_add_argslist();
  // @@protoc_insertion_point(field_add:datacapture.ArgsList.argsList)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::datacapture::ConnectArgs >&
ArgsList::argslist() const {
  // @@protoc_insertion_point(field_list:datacapture.ArgsList.argsList)
  return _impl_.argslist_;
}

// -------------------------------------------------------------------

// CommonCMD

// .datacapture.CommonCMD.cmdType type = 1;
inline void CommonCMD::clear_type() {
  _impl_.type_ = 0;
}
inline ::datacapture::CommonCMD_cmdType CommonCMD::_internal_type() const {
  return static_cast< ::datacapture::CommonCMD_cmdType >(_impl_.type_);
}
inline ::datacapture::CommonCMD_cmdType CommonCMD::type() const {
  // @@protoc_insertion_point(field_get:datacapture.CommonCMD.type)
  return _internal_type();
}
inline void CommonCMD::_internal_set_type(::datacapture::CommonCMD_cmdType value) {
  
  _impl_.type_ = value;
}
inline void CommonCMD::set_type(::datacapture::CommonCMD_cmdType value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:datacapture.CommonCMD.type)
}

// -------------------------------------------------------------------

// RoadInfo

// string strID = 1;
inline void RoadInfo::clear_strid() {
  _impl_.strid_.ClearToEmpty();
}
inline const std::string& RoadInfo::strid() const {
  // @@protoc_insertion_point(field_get:datacapture.RoadInfo.strID)
  return _internal_strid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RoadInfo::set_strid(ArgT0&& arg0, ArgT... args) {
 
 _impl_.strid_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:datacapture.RoadInfo.strID)
}
inline std::string* RoadInfo::mutable_strid() {
  std::string* _s = _internal_mutable_strid();
  // @@protoc_insertion_point(field_mutable:datacapture.RoadInfo.strID)
  return _s;
}
inline const std::string& RoadInfo::_internal_strid() const {
  return _impl_.strid_.Get();
}
inline void RoadInfo::_internal_set_strid(const std::string& value) {
  
  _impl_.strid_.Set(value, GetArenaForAllocation());
}
inline std::string* RoadInfo::_internal_mutable_strid() {
  
  return _impl_.strid_.Mutable(GetArenaForAllocation());
}
inline std::string* RoadInfo::release_strid() {
  // @@protoc_insertion_point(field_release:datacapture.RoadInfo.strID)
  return _impl_.strid_.Release();
}
inline void RoadInfo::set_allocated_strid(std::string* strid) {
  if (strid != nullptr) {
    
  } else {
    
  }
  _impl_.strid_.SetAllocated(strid, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.strid_.IsDefault()) {
    _impl_.strid_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:datacapture.RoadInfo.strID)
}

// string strDescribe = 2;
inline void RoadInfo::clear_strdescribe() {
  _impl_.strdescribe_.ClearToEmpty();
}
inline const std::string& RoadInfo::strdescribe() const {
  // @@protoc_insertion_point(field_get:datacapture.RoadInfo.strDescribe)
  return _internal_strdescribe();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RoadInfo::set_strdescribe(ArgT0&& arg0, ArgT... args) {
 
 _impl_.strdescribe_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:datacapture.RoadInfo.strDescribe)
}
inline std::string* RoadInfo::mutable_strdescribe() {
  std::string* _s = _internal_mutable_strdescribe();
  // @@protoc_insertion_point(field_mutable:datacapture.RoadInfo.strDescribe)
  return _s;
}
inline const std::string& RoadInfo::_internal_strdescribe() const {
  return _impl_.strdescribe_.Get();
}
inline void RoadInfo::_internal_set_strdescribe(const std::string& value) {
  
  _impl_.strdescribe_.Set(value, GetArenaForAllocation());
}
inline std::string* RoadInfo::_internal_mutable_strdescribe() {
  
  return _impl_.strdescribe_.Mutable(GetArenaForAllocation());
}
inline std::string* RoadInfo::release_strdescribe() {
  // @@protoc_insertion_point(field_release:datacapture.RoadInfo.strDescribe)
  return _impl_.strdescribe_.Release();
}
inline void RoadInfo::set_allocated_strdescribe(std::string* strdescribe) {
  if (strdescribe != nullptr) {
    
  } else {
    
  }
  _impl_.strdescribe_.SetAllocated(strdescribe, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.strdescribe_.IsDefault()) {
    _impl_.strdescribe_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:datacapture.RoadInfo.strDescribe)
}

// -------------------------------------------------------------------

// RoadInfoList

// uint32 dwCnt = 1;
inline void RoadInfoList::clear_dwcnt() {
  _impl_.dwcnt_ = 0u;
}
inline uint32_t RoadInfoList::_internal_dwcnt() const {
  return _impl_.dwcnt_;
}
inline uint32_t RoadInfoList::dwcnt() const {
  // @@protoc_insertion_point(field_get:datacapture.RoadInfoList.dwCnt)
  return _internal_dwcnt();
}
inline void RoadInfoList::_internal_set_dwcnt(uint32_t value) {
  
  _impl_.dwcnt_ = value;
}
inline void RoadInfoList::set_dwcnt(uint32_t value) {
  _internal_set_dwcnt(value);
  // @@protoc_insertion_point(field_set:datacapture.RoadInfoList.dwCnt)
}

// repeated .datacapture.RoadInfo list = 2;
inline int RoadInfoList::_internal_list_size() const {
  return _impl_.list_.size();
}
inline int RoadInfoList::list_size() const {
  return _internal_list_size();
}
inline void RoadInfoList::clear_list() {
  _impl_.list_.Clear();
}
inline ::datacapture::RoadInfo* RoadInfoList::mutable_list(int index) {
  // @@protoc_insertion_point(field_mutable:datacapture.RoadInfoList.list)
  return _impl_.list_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::datacapture::RoadInfo >*
RoadInfoList::mutable_list() {
  // @@protoc_insertion_point(field_mutable_list:datacapture.RoadInfoList.list)
  return &_impl_.list_;
}
inline const ::datacapture::RoadInfo& RoadInfoList::_internal_list(int index) const {
  return _impl_.list_.Get(index);
}
inline const ::datacapture::RoadInfo& RoadInfoList::list(int index) const {
  // @@protoc_insertion_point(field_get:datacapture.RoadInfoList.list)
  return _internal_list(index);
}
inline ::datacapture::RoadInfo* RoadInfoList::_internal_add_list() {
  return _impl_.list_.Add();
}
inline ::datacapture::RoadInfo* RoadInfoList::add_list() {
  ::datacapture::RoadInfo* _add = _internal_add_list();
  // @@protoc_insertion_point(field_add:datacapture.RoadInfoList.list)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::datacapture::RoadInfo >&
RoadInfoList::list() const {
  // @@protoc_insertion_point(field_list:datacapture.RoadInfoList.list)
  return _impl_.list_;
}

// -------------------------------------------------------------------

// SystemStatus

// uint64 llTimestamp = 1;
inline void SystemStatus::clear_lltimestamp() {
  _impl_.lltimestamp_ = uint64_t{0u};
}
inline uint64_t SystemStatus::_internal_lltimestamp() const {
  return _impl_.lltimestamp_;
}
inline uint64_t SystemStatus::lltimestamp() const {
  // @@protoc_insertion_point(field_get:datacapture.SystemStatus.llTimestamp)
  return _internal_lltimestamp();
}
inline void SystemStatus::_internal_set_lltimestamp(uint64_t value) {
  
  _impl_.lltimestamp_ = value;
}
inline void SystemStatus::set_lltimestamp(uint64_t value) {
  _internal_set_lltimestamp(value);
  // @@protoc_insertion_point(field_set:datacapture.SystemStatus.llTimestamp)
}

// .datacapture.SystemStatus.statusType type = 2;
inline void SystemStatus::clear_type() {
  _impl_.type_ = 0;
}
inline ::datacapture::SystemStatus_statusType SystemStatus::_internal_type() const {
  return static_cast< ::datacapture::SystemStatus_statusType >(_impl_.type_);
}
inline ::datacapture::SystemStatus_statusType SystemStatus::type() const {
  // @@protoc_insertion_point(field_get:datacapture.SystemStatus.type)
  return _internal_type();
}
inline void SystemStatus::_internal_set_type(::datacapture::SystemStatus_statusType value) {
  
  _impl_.type_ = value;
}
inline void SystemStatus::set_type(::datacapture::SystemStatus_statusType value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:datacapture.SystemStatus.type)
}

// -------------------------------------------------------------------

// SystemInfo

// float cpuUsed = 1;
inline void SystemInfo::clear_cpuused() {
  _impl_.cpuused_ = 0;
}
inline float SystemInfo::_internal_cpuused() const {
  return _impl_.cpuused_;
}
inline float SystemInfo::cpuused() const {
  // @@protoc_insertion_point(field_get:datacapture.SystemInfo.cpuUsed)
  return _internal_cpuused();
}
inline void SystemInfo::_internal_set_cpuused(float value) {
  
  _impl_.cpuused_ = value;
}
inline void SystemInfo::set_cpuused(float value) {
  _internal_set_cpuused(value);
  // @@protoc_insertion_point(field_set:datacapture.SystemInfo.cpuUsed)
}

// float cpuTemp = 2;
inline void SystemInfo::clear_cputemp() {
  _impl_.cputemp_ = 0;
}
inline float SystemInfo::_internal_cputemp() const {
  return _impl_.cputemp_;
}
inline float SystemInfo::cputemp() const {
  // @@protoc_insertion_point(field_get:datacapture.SystemInfo.cpuTemp)
  return _internal_cputemp();
}
inline void SystemInfo::_internal_set_cputemp(float value) {
  
  _impl_.cputemp_ = value;
}
inline void SystemInfo::set_cputemp(float value) {
  _internal_set_cputemp(value);
  // @@protoc_insertion_point(field_set:datacapture.SystemInfo.cpuTemp)
}

// float cpuFreq = 3;
inline void SystemInfo::clear_cpufreq() {
  _impl_.cpufreq_ = 0;
}
inline float SystemInfo::_internal_cpufreq() const {
  return _impl_.cpufreq_;
}
inline float SystemInfo::cpufreq() const {
  // @@protoc_insertion_point(field_get:datacapture.SystemInfo.cpuFreq)
  return _internal_cpufreq();
}
inline void SystemInfo::_internal_set_cpufreq(float value) {
  
  _impl_.cpufreq_ = value;
}
inline void SystemInfo::set_cpufreq(float value) {
  _internal_set_cpufreq(value);
  // @@protoc_insertion_point(field_set:datacapture.SystemInfo.cpuFreq)
}

// float cpuCoreNum = 4;
inline void SystemInfo::clear_cpucorenum() {
  _impl_.cpucorenum_ = 0;
}
inline float SystemInfo::_internal_cpucorenum() const {
  return _impl_.cpucorenum_;
}
inline float SystemInfo::cpucorenum() const {
  // @@protoc_insertion_point(field_get:datacapture.SystemInfo.cpuCoreNum)
  return _internal_cpucorenum();
}
inline void SystemInfo::_internal_set_cpucorenum(float value) {
  
  _impl_.cpucorenum_ = value;
}
inline void SystemInfo::set_cpucorenum(float value) {
  _internal_set_cpucorenum(value);
  // @@protoc_insertion_point(field_set:datacapture.SystemInfo.cpuCoreNum)
}

// float memoryUsed = 5;
inline void SystemInfo::clear_memoryused() {
  _impl_.memoryused_ = 0;
}
inline float SystemInfo::_internal_memoryused() const {
  return _impl_.memoryused_;
}
inline float SystemInfo::memoryused() const {
  // @@protoc_insertion_point(field_get:datacapture.SystemInfo.memoryUsed)
  return _internal_memoryused();
}
inline void SystemInfo::_internal_set_memoryused(float value) {
  
  _impl_.memoryused_ = value;
}
inline void SystemInfo::set_memoryused(float value) {
  _internal_set_memoryused(value);
  // @@protoc_insertion_point(field_set:datacapture.SystemInfo.memoryUsed)
}

// float memoryTotal = 6;
inline void SystemInfo::clear_memorytotal() {
  _impl_.memorytotal_ = 0;
}
inline float SystemInfo::_internal_memorytotal() const {
  return _impl_.memorytotal_;
}
inline float SystemInfo::memorytotal() const {
  // @@protoc_insertion_point(field_get:datacapture.SystemInfo.memoryTotal)
  return _internal_memorytotal();
}
inline void SystemInfo::_internal_set_memorytotal(float value) {
  
  _impl_.memorytotal_ = value;
}
inline void SystemInfo::set_memorytotal(float value) {
  _internal_set_memorytotal(value);
  // @@protoc_insertion_point(field_set:datacapture.SystemInfo.memoryTotal)
}

// float diskUsed = 7;
inline void SystemInfo::clear_diskused() {
  _impl_.diskused_ = 0;
}
inline float SystemInfo::_internal_diskused() const {
  return _impl_.diskused_;
}
inline float SystemInfo::diskused() const {
  // @@protoc_insertion_point(field_get:datacapture.SystemInfo.diskUsed)
  return _internal_diskused();
}
inline void SystemInfo::_internal_set_diskused(float value) {
  
  _impl_.diskused_ = value;
}
inline void SystemInfo::set_diskused(float value) {
  _internal_set_diskused(value);
  // @@protoc_insertion_point(field_set:datacapture.SystemInfo.diskUsed)
}

// -------------------------------------------------------------------

// ClientInfo

// string ip = 1;
inline void ClientInfo::clear_ip() {
  _impl_.ip_.ClearToEmpty();
}
inline const std::string& ClientInfo::ip() const {
  // @@protoc_insertion_point(field_get:datacapture.ClientInfo.ip)
  return _internal_ip();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ClientInfo::set_ip(ArgT0&& arg0, ArgT... args) {
 
 _impl_.ip_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:datacapture.ClientInfo.ip)
}
inline std::string* ClientInfo::mutable_ip() {
  std::string* _s = _internal_mutable_ip();
  // @@protoc_insertion_point(field_mutable:datacapture.ClientInfo.ip)
  return _s;
}
inline const std::string& ClientInfo::_internal_ip() const {
  return _impl_.ip_.Get();
}
inline void ClientInfo::_internal_set_ip(const std::string& value) {
  
  _impl_.ip_.Set(value, GetArenaForAllocation());
}
inline std::string* ClientInfo::_internal_mutable_ip() {
  
  return _impl_.ip_.Mutable(GetArenaForAllocation());
}
inline std::string* ClientInfo::release_ip() {
  // @@protoc_insertion_point(field_release:datacapture.ClientInfo.ip)
  return _impl_.ip_.Release();
}
inline void ClientInfo::set_allocated_ip(std::string* ip) {
  if (ip != nullptr) {
    
  } else {
    
  }
  _impl_.ip_.SetAllocated(ip, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.ip_.IsDefault()) {
    _impl_.ip_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:datacapture.ClientInfo.ip)
}

// uint32 port = 2;
inline void ClientInfo::clear_port() {
  _impl_.port_ = 0u;
}
inline uint32_t ClientInfo::_internal_port() const {
  return _impl_.port_;
}
inline uint32_t ClientInfo::port() const {
  // @@protoc_insertion_point(field_get:datacapture.ClientInfo.port)
  return _internal_port();
}
inline void ClientInfo::_internal_set_port(uint32_t value) {
  
  _impl_.port_ = value;
}
inline void ClientInfo::set_port(uint32_t value) {
  _internal_set_port(value);
  // @@protoc_insertion_point(field_set:datacapture.ClientInfo.port)
}

// -------------------------------------------------------------------

// NetConfig

// string localIp = 1;
inline void NetConfig::clear_localip() {
  _impl_.localip_.ClearToEmpty();
}
inline const std::string& NetConfig::localip() const {
  // @@protoc_insertion_point(field_get:datacapture.NetConfig.localIp)
  return _internal_localip();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void NetConfig::set_localip(ArgT0&& arg0, ArgT... args) {
 
 _impl_.localip_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:datacapture.NetConfig.localIp)
}
inline std::string* NetConfig::mutable_localip() {
  std::string* _s = _internal_mutable_localip();
  // @@protoc_insertion_point(field_mutable:datacapture.NetConfig.localIp)
  return _s;
}
inline const std::string& NetConfig::_internal_localip() const {
  return _impl_.localip_.Get();
}
inline void NetConfig::_internal_set_localip(const std::string& value) {
  
  _impl_.localip_.Set(value, GetArenaForAllocation());
}
inline std::string* NetConfig::_internal_mutable_localip() {
  
  return _impl_.localip_.Mutable(GetArenaForAllocation());
}
inline std::string* NetConfig::release_localip() {
  // @@protoc_insertion_point(field_release:datacapture.NetConfig.localIp)
  return _impl_.localip_.Release();
}
inline void NetConfig::set_allocated_localip(std::string* localip) {
  if (localip != nullptr) {
    
  } else {
    
  }
  _impl_.localip_.SetAllocated(localip, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.localip_.IsDefault()) {
    _impl_.localip_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:datacapture.NetConfig.localIp)
}

// string mask = 2;
inline void NetConfig::clear_mask() {
  _impl_.mask_.ClearToEmpty();
}
inline const std::string& NetConfig::mask() const {
  // @@protoc_insertion_point(field_get:datacapture.NetConfig.mask)
  return _internal_mask();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void NetConfig::set_mask(ArgT0&& arg0, ArgT... args) {
 
 _impl_.mask_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:datacapture.NetConfig.mask)
}
inline std::string* NetConfig::mutable_mask() {
  std::string* _s = _internal_mutable_mask();
  // @@protoc_insertion_point(field_mutable:datacapture.NetConfig.mask)
  return _s;
}
inline const std::string& NetConfig::_internal_mask() const {
  return _impl_.mask_.Get();
}
inline void NetConfig::_internal_set_mask(const std::string& value) {
  
  _impl_.mask_.Set(value, GetArenaForAllocation());
}
inline std::string* NetConfig::_internal_mutable_mask() {
  
  return _impl_.mask_.Mutable(GetArenaForAllocation());
}
inline std::string* NetConfig::release_mask() {
  // @@protoc_insertion_point(field_release:datacapture.NetConfig.mask)
  return _impl_.mask_.Release();
}
inline void NetConfig::set_allocated_mask(std::string* mask) {
  if (mask != nullptr) {
    
  } else {
    
  }
  _impl_.mask_.SetAllocated(mask, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.mask_.IsDefault()) {
    _impl_.mask_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:datacapture.NetConfig.mask)
}

// string gateway = 3;
inline void NetConfig::clear_gateway() {
  _impl_.gateway_.ClearToEmpty();
}
inline const std::string& NetConfig::gateway() const {
  // @@protoc_insertion_point(field_get:datacapture.NetConfig.gateway)
  return _internal_gateway();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void NetConfig::set_gateway(ArgT0&& arg0, ArgT... args) {
 
 _impl_.gateway_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:datacapture.NetConfig.gateway)
}
inline std::string* NetConfig::mutable_gateway() {
  std::string* _s = _internal_mutable_gateway();
  // @@protoc_insertion_point(field_mutable:datacapture.NetConfig.gateway)
  return _s;
}
inline const std::string& NetConfig::_internal_gateway() const {
  return _impl_.gateway_.Get();
}
inline void NetConfig::_internal_set_gateway(const std::string& value) {
  
  _impl_.gateway_.Set(value, GetArenaForAllocation());
}
inline std::string* NetConfig::_internal_mutable_gateway() {
  
  return _impl_.gateway_.Mutable(GetArenaForAllocation());
}
inline std::string* NetConfig::release_gateway() {
  // @@protoc_insertion_point(field_release:datacapture.NetConfig.gateway)
  return _impl_.gateway_.Release();
}
inline void NetConfig::set_allocated_gateway(std::string* gateway) {
  if (gateway != nullptr) {
    
  } else {
    
  }
  _impl_.gateway_.SetAllocated(gateway, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.gateway_.IsDefault()) {
    _impl_.gateway_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:datacapture.NetConfig.gateway)
}

// string targetIp = 4;
inline void NetConfig::clear_targetip() {
  _impl_.targetip_.ClearToEmpty();
}
inline const std::string& NetConfig::targetip() const {
  // @@protoc_insertion_point(field_get:datacapture.NetConfig.targetIp)
  return _internal_targetip();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void NetConfig::set_targetip(ArgT0&& arg0, ArgT... args) {
 
 _impl_.targetip_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:datacapture.NetConfig.targetIp)
}
inline std::string* NetConfig::mutable_targetip() {
  std::string* _s = _internal_mutable_targetip();
  // @@protoc_insertion_point(field_mutable:datacapture.NetConfig.targetIp)
  return _s;
}
inline const std::string& NetConfig::_internal_targetip() const {
  return _impl_.targetip_.Get();
}
inline void NetConfig::_internal_set_targetip(const std::string& value) {
  
  _impl_.targetip_.Set(value, GetArenaForAllocation());
}
inline std::string* NetConfig::_internal_mutable_targetip() {
  
  return _impl_.targetip_.Mutable(GetArenaForAllocation());
}
inline std::string* NetConfig::release_targetip() {
  // @@protoc_insertion_point(field_release:datacapture.NetConfig.targetIp)
  return _impl_.targetip_.Release();
}
inline void NetConfig::set_allocated_targetip(std::string* targetip) {
  if (targetip != nullptr) {
    
  } else {
    
  }
  _impl_.targetip_.SetAllocated(targetip, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.targetip_.IsDefault()) {
    _impl_.targetip_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:datacapture.NetConfig.targetIp)
}

// string route = 5;
inline void NetConfig::clear_route() {
  _impl_.route_.ClearToEmpty();
}
inline const std::string& NetConfig::route() const {
  // @@protoc_insertion_point(field_get:datacapture.NetConfig.route)
  return _internal_route();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void NetConfig::set_route(ArgT0&& arg0, ArgT... args) {
 
 _impl_.route_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:datacapture.NetConfig.route)
}
inline std::string* NetConfig::mutable_route() {
  std::string* _s = _internal_mutable_route();
  // @@protoc_insertion_point(field_mutable:datacapture.NetConfig.route)
  return _s;
}
inline const std::string& NetConfig::_internal_route() const {
  return _impl_.route_.Get();
}
inline void NetConfig::_internal_set_route(const std::string& value) {
  
  _impl_.route_.Set(value, GetArenaForAllocation());
}
inline std::string* NetConfig::_internal_mutable_route() {
  
  return _impl_.route_.Mutable(GetArenaForAllocation());
}
inline std::string* NetConfig::release_route() {
  // @@protoc_insertion_point(field_release:datacapture.NetConfig.route)
  return _impl_.route_.Release();
}
inline void NetConfig::set_allocated_route(std::string* route) {
  if (route != nullptr) {
    
  } else {
    
  }
  _impl_.route_.SetAllocated(route, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.route_.IsDefault()) {
    _impl_.route_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:datacapture.NetConfig.route)
}

// -------------------------------------------------------------------

// storeInfo

// string storePath = 1;
inline void storeInfo::clear_storepath() {
  _impl_.storepath_.ClearToEmpty();
}
inline const std::string& storeInfo::storepath() const {
  // @@protoc_insertion_point(field_get:datacapture.storeInfo.storePath)
  return _internal_storepath();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void storeInfo::set_storepath(ArgT0&& arg0, ArgT... args) {
 
 _impl_.storepath_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:datacapture.storeInfo.storePath)
}
inline std::string* storeInfo::mutable_storepath() {
  std::string* _s = _internal_mutable_storepath();
  // @@protoc_insertion_point(field_mutable:datacapture.storeInfo.storePath)
  return _s;
}
inline const std::string& storeInfo::_internal_storepath() const {
  return _impl_.storepath_.Get();
}
inline void storeInfo::_internal_set_storepath(const std::string& value) {
  
  _impl_.storepath_.Set(value, GetArenaForAllocation());
}
inline std::string* storeInfo::_internal_mutable_storepath() {
  
  return _impl_.storepath_.Mutable(GetArenaForAllocation());
}
inline std::string* storeInfo::release_storepath() {
  // @@protoc_insertion_point(field_release:datacapture.storeInfo.storePath)
  return _impl_.storepath_.Release();
}
inline void storeInfo::set_allocated_storepath(std::string* storepath) {
  if (storepath != nullptr) {
    
  } else {
    
  }
  _impl_.storepath_.SetAllocated(storepath, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.storepath_.IsDefault()) {
    _impl_.storepath_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:datacapture.storeInfo.storePath)
}

// -------------------------------------------------------------------

// SystemCmd

// uint32 port = 1;
inline void SystemCmd::clear_port() {
  _impl_.port_ = 0u;
}
inline uint32_t SystemCmd::_internal_port() const {
  return _impl_.port_;
}
inline uint32_t SystemCmd::port() const {
  // @@protoc_insertion_point(field_get:datacapture.SystemCmd.port)
  return _internal_port();
}
inline void SystemCmd::_internal_set_port(uint32_t value) {
  
  _impl_.port_ = value;
}
inline void SystemCmd::set_port(uint32_t value) {
  _internal_set_port(value);
  // @@protoc_insertion_point(field_set:datacapture.SystemCmd.port)
}

// .datacapture.SystemCmd.statusType type = 2;
inline void SystemCmd::clear_type() {
  _impl_.type_ = 0;
}
inline ::datacapture::SystemCmd_statusType SystemCmd::_internal_type() const {
  return static_cast< ::datacapture::SystemCmd_statusType >(_impl_.type_);
}
inline ::datacapture::SystemCmd_statusType SystemCmd::type() const {
  // @@protoc_insertion_point(field_get:datacapture.SystemCmd.type)
  return _internal_type();
}
inline void SystemCmd::_internal_set_type(::datacapture::SystemCmd_statusType value) {
  
  _impl_.type_ = value;
}
inline void SystemCmd::set_type(::datacapture::SystemCmd_statusType value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:datacapture.SystemCmd.type)
}

// -------------------------------------------------------------------

// SystemLog

// bool status = 1;
inline void SystemLog::clear_status() {
  _impl_.status_ = false;
}
inline bool SystemLog::_internal_status() const {
  return _impl_.status_;
}
inline bool SystemLog::status() const {
  // @@protoc_insertion_point(field_get:datacapture.SystemLog.status)
  return _internal_status();
}
inline void SystemLog::_internal_set_status(bool value) {
  
  _impl_.status_ = value;
}
inline void SystemLog::set_status(bool value) {
  _internal_set_status(value);
  // @@protoc_insertion_point(field_set:datacapture.SystemLog.status)
}

// string log = 2;
inline void SystemLog::clear_log() {
  _impl_.log_.ClearToEmpty();
}
inline const std::string& SystemLog::log() const {
  // @@protoc_insertion_point(field_get:datacapture.SystemLog.log)
  return _internal_log();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SystemLog::set_log(ArgT0&& arg0, ArgT... args) {
 
 _impl_.log_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:datacapture.SystemLog.log)
}
inline std::string* SystemLog::mutable_log() {
  std::string* _s = _internal_mutable_log();
  // @@protoc_insertion_point(field_mutable:datacapture.SystemLog.log)
  return _s;
}
inline const std::string& SystemLog::_internal_log() const {
  return _impl_.log_.Get();
}
inline void SystemLog::_internal_set_log(const std::string& value) {
  
  _impl_.log_.Set(value, GetArenaForAllocation());
}
inline std::string* SystemLog::_internal_mutable_log() {
  
  return _impl_.log_.Mutable(GetArenaForAllocation());
}
inline std::string* SystemLog::release_log() {
  // @@protoc_insertion_point(field_release:datacapture.SystemLog.log)
  return _impl_.log_.Release();
}
inline void SystemLog::set_allocated_log(std::string* log) {
  if (log != nullptr) {
    
  } else {
    
  }
  _impl_.log_.SetAllocated(log, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.log_.IsDefault()) {
    _impl_.log_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:datacapture.SystemLog.log)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace datacapture

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::datacapture::Message_MessageType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::datacapture::Message_MessageType>() {
  return ::datacapture::Message_MessageType_descriptor();
}
template <> struct is_proto_enum< ::datacapture::DataQuery_dataType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::datacapture::DataQuery_dataType>() {
  return ::datacapture::DataQuery_dataType_descriptor();
}
template <> struct is_proto_enum< ::datacapture::ConnectArgs_ConnectType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::datacapture::ConnectArgs_ConnectType>() {
  return ::datacapture::ConnectArgs_ConnectType_descriptor();
}
template <> struct is_proto_enum< ::datacapture::CommonCMD_cmdType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::datacapture::CommonCMD_cmdType>() {
  return ::datacapture::CommonCMD_cmdType_descriptor();
}
template <> struct is_proto_enum< ::datacapture::SystemStatus_statusType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::datacapture::SystemStatus_statusType>() {
  return ::datacapture::SystemStatus_statusType_descriptor();
}
template <> struct is_proto_enum< ::datacapture::SystemCmd_statusType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::datacapture::SystemCmd_statusType>() {
  return ::datacapture::SystemCmd_statusType_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_datacapture_2eCommunicate_2eproto
