// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: datacapture.Communicate.proto

#include "datacapture.Communicate.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG

namespace _pb = ::PROTOBUF_NAMESPACE_ID;
namespace _pbi = _pb::internal;

namespace datacapture {
PROTOBUF_CONSTEXPR Message::Message(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.type_)*/0
  , /*decltype(_impl_.data_)*/{}
  , /*decltype(_impl_._cached_size_)*/{}
  , /*decltype(_impl_._oneof_case_)*/{}} {}
struct MessageDefaultTypeInternal {
  PROTOBUF_CONSTEXPR MessageDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~MessageDefaultTypeInternal() {}
  union {
    Message _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 MessageDefaultTypeInternal _Message_default_instance_;
PROTOBUF_CONSTEXPR RawData::RawData(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.strcrossroadid_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.strdata_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.llrecvtime_)*/uint64_t{0u}
  , /*decltype(_impl_.lldatatime_)*/uint64_t{0u}
  , /*decltype(_impl_.dwdatalength_)*/0u
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct RawDataDefaultTypeInternal {
  PROTOBUF_CONSTEXPR RawDataDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~RawDataDefaultTypeInternal() {}
  union {
    RawData _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 RawDataDefaultTypeInternal _RawData_default_instance_;
PROTOBUF_CONSTEXPR CapStatus::CapStatus(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.strcrossroadid_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.strdescribe_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.lltimestamp_)*/uint64_t{0u}
  , /*decltype(_impl_.biscaptruing_)*/false
  , /*decltype(_impl_.ffreq_)*/0
  , /*decltype(_impl_.dwrecvdatacnt_)*/0u
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct CapStatusDefaultTypeInternal {
  PROTOBUF_CONSTEXPR CapStatusDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~CapStatusDefaultTypeInternal() {}
  union {
    CapStatus _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 CapStatusDefaultTypeInternal _CapStatus_default_instance_;
PROTOBUF_CONSTEXPR Monitor::Monitor(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.statuslist_)*/{}
  , /*decltype(_impl_.llupdatetime_)*/uint64_t{0u}
  , /*decltype(_impl_.llstarttime_)*/uint64_t{0u}
  , /*decltype(_impl_.lldurationtime_)*/uint64_t{0u}
  , /*decltype(_impl_.dwstatuscnt_)*/0u
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct MonitorDefaultTypeInternal {
  PROTOBUF_CONSTEXPR MonitorDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~MonitorDefaultTypeInternal() {}
  union {
    Monitor _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 MonitorDefaultTypeInternal _Monitor_default_instance_;
PROTOBUF_CONSTEXPR DataQuery::DataQuery(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.strcrossroadid_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.llstarttime_)*/uint64_t{0u}
  , /*decltype(_impl_.llendtime_)*/uint64_t{0u}
  , /*decltype(_impl_.type_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct DataQueryDefaultTypeInternal {
  PROTOBUF_CONSTEXPR DataQueryDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~DataQueryDefaultTypeInternal() {}
  union {
    DataQuery _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 DataQueryDefaultTypeInternal _DataQuery_default_instance_;
PROTOBUF_CONSTEXPR DataQueryRes::DataQueryRes(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.datalist_)*/{}
  , /*decltype(_impl_.strerr_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.dwcnt_)*/0u
  , /*decltype(_impl_.bissucceed_)*/false
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct DataQueryResDefaultTypeInternal {
  PROTOBUF_CONSTEXPR DataQueryResDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~DataQueryResDefaultTypeInternal() {}
  union {
    DataQueryRes _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 DataQueryResDefaultTypeInternal _DataQueryRes_default_instance_;
PROTOBUF_CONSTEXPR DataQueryBatchesReq::DataQueryBatchesReq(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.strcrossid_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.llstarttime_)*/uint64_t{0u}
  , /*decltype(_impl_.llendtime_)*/uint64_t{0u}
  , /*decltype(_impl_.dwnowpage_)*/0u
  , /*decltype(_impl_.dwpagesize_)*/0u
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct DataQueryBatchesReqDefaultTypeInternal {
  PROTOBUF_CONSTEXPR DataQueryBatchesReqDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~DataQueryBatchesReqDefaultTypeInternal() {}
  union {
    DataQueryBatchesReq _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 DataQueryBatchesReqDefaultTypeInternal _DataQueryBatchesReq_default_instance_;
PROTOBUF_CONSTEXPR DataQueryBatchesRes::DataQueryBatchesRes(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.datalist_)*/{}
  , /*decltype(_impl_.strerr_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.bissucceed_)*/false
  , /*decltype(_impl_.dwnowpage_)*/0u
  , /*decltype(_impl_.dwpagesize_)*/0u
  , /*decltype(_impl_.dwtotalpages_)*/0u
  , /*decltype(_impl_.dwtotaldatas_)*/0u
  , /*decltype(_impl_.dwcnt_)*/0u
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct DataQueryBatchesResDefaultTypeInternal {
  PROTOBUF_CONSTEXPR DataQueryBatchesResDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~DataQueryBatchesResDefaultTypeInternal() {}
  union {
    DataQueryBatchesRes _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 DataQueryBatchesResDefaultTypeInternal _DataQueryBatchesRes_default_instance_;
PROTOBUF_CONSTEXPR CompressReqList::CompressReqList(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.querylist_)*/{}
  , /*decltype(_impl_.strhttpurl_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.strpackagename_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.strserialnum_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.compresstype_)*/0u
  , /*decltype(_impl_.dwcnt_)*/0u
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct CompressReqListDefaultTypeInternal {
  PROTOBUF_CONSTEXPR CompressReqListDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~CompressReqListDefaultTypeInternal() {}
  union {
    CompressReqList _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 CompressReqListDefaultTypeInternal _CompressReqList_default_instance_;
PROTOBUF_CONSTEXPR CompressRes::CompressRes(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.filepath_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.strerr_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.strserialnum_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.strpackagename_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.compresstype_)*/0u
  , /*decltype(_impl_.bissucceed_)*/false
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct CompressResDefaultTypeInternal {
  PROTOBUF_CONSTEXPR CompressResDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~CompressResDefaultTypeInternal() {}
  union {
    CompressRes _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 CompressResDefaultTypeInternal _CompressRes_default_instance_;
PROTOBUF_CONSTEXPR ConnectArgs::ConnectArgs(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.strcrossroadid_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.strtopice_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.strpassword_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.strclientid_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.straddr_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.strusername_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.strdescribe_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.dwno_)*/0u
  , /*decltype(_impl_.isenable_)*/false
  , /*decltype(_impl_.type_)*/0
  , /*decltype(_impl_.dwfactory_)*/0u
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct ConnectArgsDefaultTypeInternal {
  PROTOBUF_CONSTEXPR ConnectArgsDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~ConnectArgsDefaultTypeInternal() {}
  union {
    ConnectArgs _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 ConnectArgsDefaultTypeInternal _ConnectArgs_default_instance_;
PROTOBUF_CONSTEXPR ArgsList::ArgsList(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.argslist_)*/{}
  , /*decltype(_impl_.dwcnt_)*/0u
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct ArgsListDefaultTypeInternal {
  PROTOBUF_CONSTEXPR ArgsListDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~ArgsListDefaultTypeInternal() {}
  union {
    ArgsList _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 ArgsListDefaultTypeInternal _ArgsList_default_instance_;
PROTOBUF_CONSTEXPR CommonCMD::CommonCMD(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.type_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct CommonCMDDefaultTypeInternal {
  PROTOBUF_CONSTEXPR CommonCMDDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~CommonCMDDefaultTypeInternal() {}
  union {
    CommonCMD _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 CommonCMDDefaultTypeInternal _CommonCMD_default_instance_;
PROTOBUF_CONSTEXPR RoadInfo::RoadInfo(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.strid_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.strdescribe_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct RoadInfoDefaultTypeInternal {
  PROTOBUF_CONSTEXPR RoadInfoDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~RoadInfoDefaultTypeInternal() {}
  union {
    RoadInfo _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 RoadInfoDefaultTypeInternal _RoadInfo_default_instance_;
PROTOBUF_CONSTEXPR RoadInfoList::RoadInfoList(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.list_)*/{}
  , /*decltype(_impl_.dwcnt_)*/0u
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct RoadInfoListDefaultTypeInternal {
  PROTOBUF_CONSTEXPR RoadInfoListDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~RoadInfoListDefaultTypeInternal() {}
  union {
    RoadInfoList _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 RoadInfoListDefaultTypeInternal _RoadInfoList_default_instance_;
PROTOBUF_CONSTEXPR SystemStatus::SystemStatus(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.lltimestamp_)*/uint64_t{0u}
  , /*decltype(_impl_.type_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct SystemStatusDefaultTypeInternal {
  PROTOBUF_CONSTEXPR SystemStatusDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~SystemStatusDefaultTypeInternal() {}
  union {
    SystemStatus _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 SystemStatusDefaultTypeInternal _SystemStatus_default_instance_;
PROTOBUF_CONSTEXPR SystemInfo::SystemInfo(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.cpuused_)*/0
  , /*decltype(_impl_.cputemp_)*/0
  , /*decltype(_impl_.cpufreq_)*/0
  , /*decltype(_impl_.cpucorenum_)*/0
  , /*decltype(_impl_.memoryused_)*/0
  , /*decltype(_impl_.memorytotal_)*/0
  , /*decltype(_impl_.diskused_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct SystemInfoDefaultTypeInternal {
  PROTOBUF_CONSTEXPR SystemInfoDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~SystemInfoDefaultTypeInternal() {}
  union {
    SystemInfo _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 SystemInfoDefaultTypeInternal _SystemInfo_default_instance_;
PROTOBUF_CONSTEXPR ClientInfo::ClientInfo(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.ip_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.port_)*/0u
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct ClientInfoDefaultTypeInternal {
  PROTOBUF_CONSTEXPR ClientInfoDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~ClientInfoDefaultTypeInternal() {}
  union {
    ClientInfo _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 ClientInfoDefaultTypeInternal _ClientInfo_default_instance_;
PROTOBUF_CONSTEXPR NetConfig::NetConfig(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.localip_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.mask_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.gateway_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.targetip_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.route_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct NetConfigDefaultTypeInternal {
  PROTOBUF_CONSTEXPR NetConfigDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~NetConfigDefaultTypeInternal() {}
  union {
    NetConfig _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 NetConfigDefaultTypeInternal _NetConfig_default_instance_;
PROTOBUF_CONSTEXPR storeInfo::storeInfo(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.storepath_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct storeInfoDefaultTypeInternal {
  PROTOBUF_CONSTEXPR storeInfoDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~storeInfoDefaultTypeInternal() {}
  union {
    storeInfo _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 storeInfoDefaultTypeInternal _storeInfo_default_instance_;
PROTOBUF_CONSTEXPR SystemCmd::SystemCmd(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.port_)*/0u
  , /*decltype(_impl_.type_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct SystemCmdDefaultTypeInternal {
  PROTOBUF_CONSTEXPR SystemCmdDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~SystemCmdDefaultTypeInternal() {}
  union {
    SystemCmd _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 SystemCmdDefaultTypeInternal _SystemCmd_default_instance_;
PROTOBUF_CONSTEXPR SystemLog::SystemLog(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.log_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.status_)*/false
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct SystemLogDefaultTypeInternal {
  PROTOBUF_CONSTEXPR SystemLogDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~SystemLogDefaultTypeInternal() {}
  union {
    SystemLog _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 SystemLogDefaultTypeInternal _SystemLog_default_instance_;
}  // namespace datacapture
static ::_pb::Metadata file_level_metadata_datacapture_2eCommunicate_2eproto[22];
static const ::_pb::EnumDescriptor* file_level_enum_descriptors_datacapture_2eCommunicate_2eproto[6];
static constexpr ::_pb::ServiceDescriptor const** file_level_service_descriptors_datacapture_2eCommunicate_2eproto = nullptr;

const uint32_t TableStruct_datacapture_2eCommunicate_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::datacapture::Message, _internal_metadata_),
  ~0u,  // no _extensions_
  PROTOBUF_FIELD_OFFSET(::datacapture::Message, _impl_._oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::datacapture::Message, _impl_.type_),
  ::_pbi::kInvalidFieldOffsetTag,
  ::_pbi::kInvalidFieldOffsetTag,
  ::_pbi::kInvalidFieldOffsetTag,
  ::_pbi::kInvalidFieldOffsetTag,
  ::_pbi::kInvalidFieldOffsetTag,
  ::_pbi::kInvalidFieldOffsetTag,
  ::_pbi::kInvalidFieldOffsetTag,
  ::_pbi::kInvalidFieldOffsetTag,
  ::_pbi::kInvalidFieldOffsetTag,
  ::_pbi::kInvalidFieldOffsetTag,
  ::_pbi::kInvalidFieldOffsetTag,
  ::_pbi::kInvalidFieldOffsetTag,
  ::_pbi::kInvalidFieldOffsetTag,
  ::_pbi::kInvalidFieldOffsetTag,
  ::_pbi::kInvalidFieldOffsetTag,
  ::_pbi::kInvalidFieldOffsetTag,
  ::_pbi::kInvalidFieldOffsetTag,
  ::_pbi::kInvalidFieldOffsetTag,
  PROTOBUF_FIELD_OFFSET(::datacapture::Message, _impl_.data_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::datacapture::RawData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::datacapture::RawData, _impl_.strcrossroadid_),
  PROTOBUF_FIELD_OFFSET(::datacapture::RawData, _impl_.llrecvtime_),
  PROTOBUF_FIELD_OFFSET(::datacapture::RawData, _impl_.lldatatime_),
  PROTOBUF_FIELD_OFFSET(::datacapture::RawData, _impl_.dwdatalength_),
  PROTOBUF_FIELD_OFFSET(::datacapture::RawData, _impl_.strdata_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::datacapture::CapStatus, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::datacapture::CapStatus, _impl_.strcrossroadid_),
  PROTOBUF_FIELD_OFFSET(::datacapture::CapStatus, _impl_.strdescribe_),
  PROTOBUF_FIELD_OFFSET(::datacapture::CapStatus, _impl_.lltimestamp_),
  PROTOBUF_FIELD_OFFSET(::datacapture::CapStatus, _impl_.biscaptruing_),
  PROTOBUF_FIELD_OFFSET(::datacapture::CapStatus, _impl_.ffreq_),
  PROTOBUF_FIELD_OFFSET(::datacapture::CapStatus, _impl_.dwrecvdatacnt_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::datacapture::Monitor, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::datacapture::Monitor, _impl_.dwstatuscnt_),
  PROTOBUF_FIELD_OFFSET(::datacapture::Monitor, _impl_.llupdatetime_),
  PROTOBUF_FIELD_OFFSET(::datacapture::Monitor, _impl_.llstarttime_),
  PROTOBUF_FIELD_OFFSET(::datacapture::Monitor, _impl_.lldurationtime_),
  PROTOBUF_FIELD_OFFSET(::datacapture::Monitor, _impl_.statuslist_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::datacapture::DataQuery, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::datacapture::DataQuery, _impl_.strcrossroadid_),
  PROTOBUF_FIELD_OFFSET(::datacapture::DataQuery, _impl_.type_),
  PROTOBUF_FIELD_OFFSET(::datacapture::DataQuery, _impl_.llstarttime_),
  PROTOBUF_FIELD_OFFSET(::datacapture::DataQuery, _impl_.llendtime_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::datacapture::DataQueryRes, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::datacapture::DataQueryRes, _impl_.dwcnt_),
  PROTOBUF_FIELD_OFFSET(::datacapture::DataQueryRes, _impl_.bissucceed_),
  PROTOBUF_FIELD_OFFSET(::datacapture::DataQueryRes, _impl_.strerr_),
  PROTOBUF_FIELD_OFFSET(::datacapture::DataQueryRes, _impl_.datalist_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::datacapture::DataQueryBatchesReq, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::datacapture::DataQueryBatchesReq, _impl_.strcrossid_),
  PROTOBUF_FIELD_OFFSET(::datacapture::DataQueryBatchesReq, _impl_.llstarttime_),
  PROTOBUF_FIELD_OFFSET(::datacapture::DataQueryBatchesReq, _impl_.llendtime_),
  PROTOBUF_FIELD_OFFSET(::datacapture::DataQueryBatchesReq, _impl_.dwnowpage_),
  PROTOBUF_FIELD_OFFSET(::datacapture::DataQueryBatchesReq, _impl_.dwpagesize_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::datacapture::DataQueryBatchesRes, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::datacapture::DataQueryBatchesRes, _impl_.bissucceed_),
  PROTOBUF_FIELD_OFFSET(::datacapture::DataQueryBatchesRes, _impl_.dwnowpage_),
  PROTOBUF_FIELD_OFFSET(::datacapture::DataQueryBatchesRes, _impl_.dwpagesize_),
  PROTOBUF_FIELD_OFFSET(::datacapture::DataQueryBatchesRes, _impl_.dwtotalpages_),
  PROTOBUF_FIELD_OFFSET(::datacapture::DataQueryBatchesRes, _impl_.dwtotaldatas_),
  PROTOBUF_FIELD_OFFSET(::datacapture::DataQueryBatchesRes, _impl_.strerr_),
  PROTOBUF_FIELD_OFFSET(::datacapture::DataQueryBatchesRes, _impl_.dwcnt_),
  PROTOBUF_FIELD_OFFSET(::datacapture::DataQueryBatchesRes, _impl_.datalist_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::datacapture::CompressReqList, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::datacapture::CompressReqList, _impl_.compresstype_),
  PROTOBUF_FIELD_OFFSET(::datacapture::CompressReqList, _impl_.dwcnt_),
  PROTOBUF_FIELD_OFFSET(::datacapture::CompressReqList, _impl_.strhttpurl_),
  PROTOBUF_FIELD_OFFSET(::datacapture::CompressReqList, _impl_.strpackagename_),
  PROTOBUF_FIELD_OFFSET(::datacapture::CompressReqList, _impl_.strserialnum_),
  PROTOBUF_FIELD_OFFSET(::datacapture::CompressReqList, _impl_.querylist_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::datacapture::CompressRes, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::datacapture::CompressRes, _impl_.compresstype_),
  PROTOBUF_FIELD_OFFSET(::datacapture::CompressRes, _impl_.bissucceed_),
  PROTOBUF_FIELD_OFFSET(::datacapture::CompressRes, _impl_.filepath_),
  PROTOBUF_FIELD_OFFSET(::datacapture::CompressRes, _impl_.strerr_),
  PROTOBUF_FIELD_OFFSET(::datacapture::CompressRes, _impl_.strserialnum_),
  PROTOBUF_FIELD_OFFSET(::datacapture::CompressRes, _impl_.strpackagename_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::datacapture::ConnectArgs, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::datacapture::ConnectArgs, _impl_.dwno_),
  PROTOBUF_FIELD_OFFSET(::datacapture::ConnectArgs, _impl_.isenable_),
  PROTOBUF_FIELD_OFFSET(::datacapture::ConnectArgs, _impl_.type_),
  PROTOBUF_FIELD_OFFSET(::datacapture::ConnectArgs, _impl_.strcrossroadid_),
  PROTOBUF_FIELD_OFFSET(::datacapture::ConnectArgs, _impl_.strtopice_),
  PROTOBUF_FIELD_OFFSET(::datacapture::ConnectArgs, _impl_.strpassword_),
  PROTOBUF_FIELD_OFFSET(::datacapture::ConnectArgs, _impl_.strclientid_),
  PROTOBUF_FIELD_OFFSET(::datacapture::ConnectArgs, _impl_.straddr_),
  PROTOBUF_FIELD_OFFSET(::datacapture::ConnectArgs, _impl_.strusername_),
  PROTOBUF_FIELD_OFFSET(::datacapture::ConnectArgs, _impl_.strdescribe_),
  PROTOBUF_FIELD_OFFSET(::datacapture::ConnectArgs, _impl_.dwfactory_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::datacapture::ArgsList, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::datacapture::ArgsList, _impl_.dwcnt_),
  PROTOBUF_FIELD_OFFSET(::datacapture::ArgsList, _impl_.argslist_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::datacapture::CommonCMD, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::datacapture::CommonCMD, _impl_.type_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::datacapture::RoadInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::datacapture::RoadInfo, _impl_.strid_),
  PROTOBUF_FIELD_OFFSET(::datacapture::RoadInfo, _impl_.strdescribe_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::datacapture::RoadInfoList, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::datacapture::RoadInfoList, _impl_.dwcnt_),
  PROTOBUF_FIELD_OFFSET(::datacapture::RoadInfoList, _impl_.list_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::datacapture::SystemStatus, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::datacapture::SystemStatus, _impl_.lltimestamp_),
  PROTOBUF_FIELD_OFFSET(::datacapture::SystemStatus, _impl_.type_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::datacapture::SystemInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::datacapture::SystemInfo, _impl_.cpuused_),
  PROTOBUF_FIELD_OFFSET(::datacapture::SystemInfo, _impl_.cputemp_),
  PROTOBUF_FIELD_OFFSET(::datacapture::SystemInfo, _impl_.cpufreq_),
  PROTOBUF_FIELD_OFFSET(::datacapture::SystemInfo, _impl_.cpucorenum_),
  PROTOBUF_FIELD_OFFSET(::datacapture::SystemInfo, _impl_.memoryused_),
  PROTOBUF_FIELD_OFFSET(::datacapture::SystemInfo, _impl_.memorytotal_),
  PROTOBUF_FIELD_OFFSET(::datacapture::SystemInfo, _impl_.diskused_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::datacapture::ClientInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::datacapture::ClientInfo, _impl_.ip_),
  PROTOBUF_FIELD_OFFSET(::datacapture::ClientInfo, _impl_.port_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::datacapture::NetConfig, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::datacapture::NetConfig, _impl_.localip_),
  PROTOBUF_FIELD_OFFSET(::datacapture::NetConfig, _impl_.mask_),
  PROTOBUF_FIELD_OFFSET(::datacapture::NetConfig, _impl_.gateway_),
  PROTOBUF_FIELD_OFFSET(::datacapture::NetConfig, _impl_.targetip_),
  PROTOBUF_FIELD_OFFSET(::datacapture::NetConfig, _impl_.route_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::datacapture::storeInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::datacapture::storeInfo, _impl_.storepath_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::datacapture::SystemCmd, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::datacapture::SystemCmd, _impl_.port_),
  PROTOBUF_FIELD_OFFSET(::datacapture::SystemCmd, _impl_.type_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::datacapture::SystemLog, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::datacapture::SystemLog, _impl_.status_),
  PROTOBUF_FIELD_OFFSET(::datacapture::SystemLog, _impl_.log_),
};
static const ::_pbi::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::datacapture::Message)},
  { 26, -1, -1, sizeof(::datacapture::RawData)},
  { 37, -1, -1, sizeof(::datacapture::CapStatus)},
  { 49, -1, -1, sizeof(::datacapture::Monitor)},
  { 60, -1, -1, sizeof(::datacapture::DataQuery)},
  { 70, -1, -1, sizeof(::datacapture::DataQueryRes)},
  { 80, -1, -1, sizeof(::datacapture::DataQueryBatchesReq)},
  { 91, -1, -1, sizeof(::datacapture::DataQueryBatchesRes)},
  { 105, -1, -1, sizeof(::datacapture::CompressReqList)},
  { 117, -1, -1, sizeof(::datacapture::CompressRes)},
  { 129, -1, -1, sizeof(::datacapture::ConnectArgs)},
  { 146, -1, -1, sizeof(::datacapture::ArgsList)},
  { 154, -1, -1, sizeof(::datacapture::CommonCMD)},
  { 161, -1, -1, sizeof(::datacapture::RoadInfo)},
  { 169, -1, -1, sizeof(::datacapture::RoadInfoList)},
  { 177, -1, -1, sizeof(::datacapture::SystemStatus)},
  { 185, -1, -1, sizeof(::datacapture::SystemInfo)},
  { 198, -1, -1, sizeof(::datacapture::ClientInfo)},
  { 206, -1, -1, sizeof(::datacapture::NetConfig)},
  { 217, -1, -1, sizeof(::datacapture::storeInfo)},
  { 224, -1, -1, sizeof(::datacapture::SystemCmd)},
  { 232, -1, -1, sizeof(::datacapture::SystemLog)},
};

static const ::_pb::Message* const file_default_instances[] = {
  &::datacapture::_Message_default_instance_._instance,
  &::datacapture::_RawData_default_instance_._instance,
  &::datacapture::_CapStatus_default_instance_._instance,
  &::datacapture::_Monitor_default_instance_._instance,
  &::datacapture::_DataQuery_default_instance_._instance,
  &::datacapture::_DataQueryRes_default_instance_._instance,
  &::datacapture::_DataQueryBatchesReq_default_instance_._instance,
  &::datacapture::_DataQueryBatchesRes_default_instance_._instance,
  &::datacapture::_CompressReqList_default_instance_._instance,
  &::datacapture::_CompressRes_default_instance_._instance,
  &::datacapture::_ConnectArgs_default_instance_._instance,
  &::datacapture::_ArgsList_default_instance_._instance,
  &::datacapture::_CommonCMD_default_instance_._instance,
  &::datacapture::_RoadInfo_default_instance_._instance,
  &::datacapture::_RoadInfoList_default_instance_._instance,
  &::datacapture::_SystemStatus_default_instance_._instance,
  &::datacapture::_SystemInfo_default_instance_._instance,
  &::datacapture::_ClientInfo_default_instance_._instance,
  &::datacapture::_NetConfig_default_instance_._instance,
  &::datacapture::_storeInfo_default_instance_._instance,
  &::datacapture::_SystemCmd_default_instance_._instance,
  &::datacapture::_SystemLog_default_instance_._instance,
};

const char descriptor_table_protodef_datacapture_2eCommunicate_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\035datacapture.Communicate.proto\022\013datacap"
  "ture\"\370\014\n\007Message\022.\n\004type\030\001 \001(\0162 .datacap"
  "ture.Message.MessageType\022+\n\tcommonCmd\030\002 "
  "\001(\0132\026.datacapture.CommonCMDH\000\022)\n\010argsLis"
  "t\030\003 \001(\0132\025.datacapture.ArgsListH\000\022+\n\tcapS"
  "tatus\030\004 \001(\0132\026.datacapture.CapStatusH\000\022.\n"
  "\014dataQueryReq\030\005 \001(\0132\026.datacapture.DataQu"
  "eryH\000\0221\n\014dataQueryRes\030\006 \001(\0132\031.datacaptur"
  "e.DataQueryResH\000\0223\n\013compressReq\030\007 \001(\0132\034."
  "datacapture.CompressReqListH\000\022/\n\013compres"
  "sRes\030\010 \001(\0132\030.datacapture.CompressResH\000\022\'"
  "\n\007monitor\030\t \001(\0132\024.datacapture.MonitorH\000\022"
  "1\n\014roadInfoList\030\n \001(\0132\031.datacapture.Road"
  "InfoListH\000\0221\n\014systemStatus\030\013 \001(\0132\031.datac"
  "apture.SystemStatusH\000\022\?\n\023dataQueryBatche"
  "sReq\030\014 \001(\0132 .datacapture.DataQueryBatche"
  "sReqH\000\022\?\n\023dataQueryBatchesRes\030\r \001(\0132 .da"
  "tacapture.DataQueryBatchesResH\000\022-\n\nsyste"
  "mInfo\030\016 \001(\0132\027.datacapture.SystemInfoH\000\022-"
  "\n\nclientInfo\030\017 \001(\0132\027.datacapture.ClientI"
  "nfoH\000\022+\n\tsystemcmd\030\020 \001(\0132\026.datacapture.S"
  "ystemCmdH\000\022+\n\tstoreinfo\030\021 \001(\0132\026.datacapt"
  "ure.storeInfoH\000\022+\n\tnetconfig\030\022 \001(\0132\026.dat"
  "acapture.NetConfigH\000\022+\n\tsystemLog\030\023 \001(\0132"
  "\026.datacapture.SystemLogH\000\"\303\005\n\013MessageTyp"
  "e\022\024\n\020PROTO_COMMON_CMD\020\000\022\027\n\023PROTO_UPDATE_"
  "CONFIG\020\001\022\027\n\023PROTO_UPDATE_STATUS\020\002\022\030\n\024PRO"
  "TO_DATA_QUERY_REQ\020\003\022\030\n\024PROTO_DATA_QUERY_"
  "RES\020\004\022\033\n\027PROTO_DATA_COMPRESS_REQ\020\005\022\033\n\027PR"
  "OTO_DATA_COMPRESS_RES\020\006\022\026\n\022PROTO_DATA_MO"
  "NITOR\020\007\022\024\n\020PROTO_CONFIG_REQ\020\010\022\024\n\020PROTO_C"
  "ONFIG_RES\020\t\022\031\n\025PROTO_ROADINFO_UPDATE\020\n\022\036"
  "\n\032PROTO_UPDATE_STSTEM_STATUS\020\013\022 \n\034PROTO_"
  "DATA_QUERY_BATCHES_REQ\020\014\022 \n\034PROTO_DATA_Q"
  "UERY_BATCHES_RES\020\r\022\031\n\025PROTO_SYSTEM_INFO_"
  "REQ\020\016\022\031\n\025PROTO_SYSTEM_INFO_RES\020\017\022\031\n\025PROT"
  "O_CLIENT_INFO_REQ\020\020\022\031\n\025PROTO_CLIENT_INFO"
  "_RES\020\021\022\030\n\024PROTO_SYSTEM_CMD_REQ\020\022\022\032\n\026PROT"
  "O_SYSTEM_STORE_REQ\020\023\022\030\n\024PROTO_SYSTEM_NET"
  "_REQ\020\024\022\030\n\024PROTO_SYSTEM_CMD_RES\020\025\022\032\n\026PROT"
  "O_SYSTEM_STORE_RES\020\026\022\030\n\024PROTO_SYSTEM_NET"
  "_RES\020\027\022\031\n\025PROTO_DATABASE_CLOSED\020\030\022\024\n\020PRO"
  "TO_COMMON_LOG\020\031B\006\n\004data\"p\n\007RawData\022\026\n\016st"
  "rCrossroadId\030\001 \001(\t\022\022\n\nllRecvTime\030\002 \001(\004\022\022"
  "\n\nllDataTime\030\003 \001(\004\022\024\n\014dwDatalength\030\004 \001(\r"
  "\022\017\n\007strData\030\005 \001(\t\"\211\001\n\tCapStatus\022\026\n\016strCr"
  "ossroadId\030\001 \001(\t\022\023\n\013strDescribe\030\002 \001(\t\022\023\n\013"
  "llTimestamp\030\003 \001(\004\022\024\n\014bIsCaptruing\030\004 \001(\010\022"
  "\r\n\005fFreq\030\005 \001(\002\022\025\n\rdwRecvDataCnt\030\006 \001(\r\"\215\001"
  "\n\007Monitor\022\023\n\013dwStatusCnt\030\001 \001(\r\022\024\n\014llUpda"
  "teTime\030\002 \001(\004\022\023\n\013llStartTime\030\003 \001(\004\022\026\n\016llD"
  "urationTime\030\004 \001(\004\022*\n\nstatusList\030\005 \003(\0132\026."
  "datacapture.CapStatus\"\307\001\n\tDataQuery\022\026\n\016s"
  "trCrossroadId\030\001 \001(\t\022-\n\004type\030\002 \001(\0162\037.data"
  "capture.DataQuery.dataType\022\023\n\013llStartTim"
  "e\030\003 \001(\004\022\021\n\tllEndTime\030\004 \001(\004\"K\n\010dataType\022\021"
  "\n\rDATA_TYPE_RAW\020\000\022\027\n\023DATA_TYPE_STABILITY"
  "\020\001\022\023\n\017DATA_TYPE_TSARI\020\002\"i\n\014DataQueryRes\022"
  "\r\n\005dwCnt\030\001 \001(\r\022\022\n\nbIsSucceed\030\002 \001(\010\022\016\n\006st"
  "rErr\030\003 \001(\t\022&\n\010dataList\030\004 \003(\0132\024.datacaptu"
  "re.RawData\"x\n\023DataQueryBatchesReq\022\022\n\nstr"
  "CrossID\030\001 \001(\t\022\023\n\013llStartTime\030\002 \001(\004\022\021\n\tll"
  "EndTime\030\003 \001(\004\022\021\n\tdwNowPage\030\004 \001(\r\022\022\n\ndwPa"
  "geSize\030\005 \001(\r\"\303\001\n\023DataQueryBatchesRes\022\022\n\n"
  "bIsSucceed\030\001 \001(\010\022\021\n\tdwNowPage\030\002 \001(\r\022\022\n\nd"
  "wPageSize\030\003 \001(\r\022\024\n\014dwTotalPages\030\004 \001(\r\022\024\n"
  "\014dwTotalDatas\030\005 \001(\r\022\016\n\006strErr\030\006 \001(\t\022\r\n\005d"
  "wCnt\030\007 \001(\r\022&\n\010dataList\030\010 \003(\0132\024.datacaptu"
  "re.RawData\"\243\001\n\017CompressReqList\022\024\n\014compre"
  "ssType\030\001 \001(\r\022\r\n\005dwCnt\030\002 \001(\r\022\022\n\nstrHttpUr"
  "l\030\003 \001(\t\022\026\n\016strPackageName\030\004 \001(\t\022\024\n\014strSe"
  "rialNum\030\005 \001(\t\022)\n\tqueryList\030\006 \003(\0132\026.datac"
  "apture.DataQuery\"\207\001\n\013CompressRes\022\024\n\014comp"
  "ressType\030\001 \001(\r\022\022\n\nbIsSucceed\030\002 \001(\010\022\020\n\010fi"
  "lepath\030\003 \001(\t\022\016\n\006strErr\030\004 \001(\t\022\024\n\014strSeria"
  "lNum\030\005 \001(\t\022\026\n\016strPackageName\030\006 \001(\t\"\332\002\n\013C"
  "onnectArgs\022\014\n\004dwNo\030\001 \001(\r\022\020\n\010isEnable\030\002 \001"
  "(\010\0222\n\004type\030\003 \001(\0162$.datacapture.ConnectAr"
  "gs.ConnectType\022\026\n\016strCrossroadId\030\004 \001(\t\022\021"
  "\n\tstrTopice\030\005 \001(\t\022\023\n\013strPassword\030\006 \001(\t\022\023"
  "\n\013strClientId\030\007 \001(\t\022\017\n\007strAddr\030\010 \001(\t\022\023\n\013"
  "strUsername\030\t \001(\t\022\023\n\013strDescribe\030\n \001(\t\022\021"
  "\n\tdwFactory\030\013 \001(\r\"T\n\013ConnectType\022\016\n\nPROT"
  "O_MQTT\020\000\022\016\n\nPROTO_HTTP\020\001\022\017\n\013PROTO_KAFKA\020"
  "\002\022\024\n\020PROTO_TCP_SERVER\020\003\"E\n\010ArgsList\022\r\n\005d"
  "wCnt\030\001 \001(\r\022*\n\010argsList\030\002 \003(\0132\030.datacaptu"
  "re.ConnectArgs\"\242\001\n\tCommonCMD\022,\n\004type\030\001 \001"
  "(\0162\036.datacapture.CommonCMD.cmdType\"g\n\007cm"
  "dType\022\025\n\021PROTO_SYSTEM_INIT\020\000\022\026\n\022PROTO_SY"
  "STEM_START\020\001\022\025\n\021PROTO_SYSTEM_STOP\020\002\022\026\n\022P"
  "ROTO_SYSTEM_PAUSE\020\003\".\n\010RoadInfo\022\r\n\005strID"
  "\030\001 \001(\t\022\023\n\013strDescribe\030\002 \001(\t\"B\n\014RoadInfoL"
  "ist\022\r\n\005dwCnt\030\001 \001(\r\022#\n\004list\030\002 \003(\0132\025.datac"
  "apture.RoadInfo\"\205\001\n\014SystemStatus\022\023\n\013llTi"
  "mestamp\030\001 \001(\004\0222\n\004type\030\002 \001(\0162$.datacaptur"
  "e.SystemStatus.statusType\",\n\nstatusType\022"
  "\t\n\005START\020\000\022\010\n\004STOP\020\001\022\t\n\005PAUSE\020\002\"\216\001\n\nSyst"
  "emInfo\022\017\n\007cpuUsed\030\001 \001(\002\022\017\n\007cpuTemp\030\002 \001(\002"
  "\022\017\n\007cpuFreq\030\003 \001(\002\022\022\n\ncpuCoreNum\030\004 \001(\002\022\022\n"
  "\nmemoryUsed\030\005 \001(\002\022\023\n\013memoryTotal\030\006 \001(\002\022\020"
  "\n\010diskUsed\030\007 \001(\002\"&\n\nClientInfo\022\n\n\002ip\030\001 \001"
  "(\t\022\014\n\004port\030\002 \001(\r\"\\\n\tNetConfig\022\017\n\007localIp"
  "\030\001 \001(\t\022\014\n\004mask\030\002 \001(\t\022\017\n\007gateway\030\003 \001(\t\022\020\n"
  "\010targetIp\030\004 \001(\t\022\r\n\005route\030\005 \001(\t\"\036\n\tstoreI"
  "nfo\022\021\n\tstorePath\030\001 \001(\t\"\227\001\n\tSystemCmd\022\014\n\004"
  "port\030\001 \001(\r\022/\n\004type\030\002 \001(\0162!.datacapture.S"
  "ystemCmd.statusType\"K\n\nstatusType\022\023\n\017REC"
  "ONNECTSERVER\020\000\022\021\n\rRESTARTSERVER\020\001\022\n\n\006REB"
  "OOT\020\002\022\t\n\005NOCMD\020\003\"(\n\tSystemLog\022\016\n\006status\030"
  "\001 \001(\010\022\013\n\003log\030\002 \001(\tb\006proto3"
  ;
static ::_pbi::once_flag descriptor_table_datacapture_2eCommunicate_2eproto_once;
const ::_pbi::DescriptorTable descriptor_table_datacapture_2eCommunicate_2eproto = {
    false, false, 4386, descriptor_table_protodef_datacapture_2eCommunicate_2eproto,
    "datacapture.Communicate.proto",
    &descriptor_table_datacapture_2eCommunicate_2eproto_once, nullptr, 0, 22,
    schemas, file_default_instances, TableStruct_datacapture_2eCommunicate_2eproto::offsets,
    file_level_metadata_datacapture_2eCommunicate_2eproto, file_level_enum_descriptors_datacapture_2eCommunicate_2eproto,
    file_level_service_descriptors_datacapture_2eCommunicate_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::_pbi::DescriptorTable* descriptor_table_datacapture_2eCommunicate_2eproto_getter() {
  return &descriptor_table_datacapture_2eCommunicate_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY2 static ::_pbi::AddDescriptorsRunner dynamic_init_dummy_datacapture_2eCommunicate_2eproto(&descriptor_table_datacapture_2eCommunicate_2eproto);
namespace datacapture {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Message_MessageType_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_datacapture_2eCommunicate_2eproto);
  return file_level_enum_descriptors_datacapture_2eCommunicate_2eproto[0];
}
bool Message_MessageType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
    case 8:
    case 9:
    case 10:
    case 11:
    case 12:
    case 13:
    case 14:
    case 15:
    case 16:
    case 17:
    case 18:
    case 19:
    case 20:
    case 21:
    case 22:
    case 23:
    case 24:
    case 25:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
constexpr Message_MessageType Message::PROTO_COMMON_CMD;
constexpr Message_MessageType Message::PROTO_UPDATE_CONFIG;
constexpr Message_MessageType Message::PROTO_UPDATE_STATUS;
constexpr Message_MessageType Message::PROTO_DATA_QUERY_REQ;
constexpr Message_MessageType Message::PROTO_DATA_QUERY_RES;
constexpr Message_MessageType Message::PROTO_DATA_COMPRESS_REQ;
constexpr Message_MessageType Message::PROTO_DATA_COMPRESS_RES;
constexpr Message_MessageType Message::PROTO_DATA_MONITOR;
constexpr Message_MessageType Message::PROTO_CONFIG_REQ;
constexpr Message_MessageType Message::PROTO_CONFIG_RES;
constexpr Message_MessageType Message::PROTO_ROADINFO_UPDATE;
constexpr Message_MessageType Message::PROTO_UPDATE_STSTEM_STATUS;
constexpr Message_MessageType Message::PROTO_DATA_QUERY_BATCHES_REQ;
constexpr Message_MessageType Message::PROTO_DATA_QUERY_BATCHES_RES;
constexpr Message_MessageType Message::PROTO_SYSTEM_INFO_REQ;
constexpr Message_MessageType Message::PROTO_SYSTEM_INFO_RES;
constexpr Message_MessageType Message::PROTO_CLIENT_INFO_REQ;
constexpr Message_MessageType Message::PROTO_CLIENT_INFO_RES;
constexpr Message_MessageType Message::PROTO_SYSTEM_CMD_REQ;
constexpr Message_MessageType Message::PROTO_SYSTEM_STORE_REQ;
constexpr Message_MessageType Message::PROTO_SYSTEM_NET_REQ;
constexpr Message_MessageType Message::PROTO_SYSTEM_CMD_RES;
constexpr Message_MessageType Message::PROTO_SYSTEM_STORE_RES;
constexpr Message_MessageType Message::PROTO_SYSTEM_NET_RES;
constexpr Message_MessageType Message::PROTO_DATABASE_CLOSED;
constexpr Message_MessageType Message::PROTO_COMMON_LOG;
constexpr Message_MessageType Message::MessageType_MIN;
constexpr Message_MessageType Message::MessageType_MAX;
constexpr int Message::MessageType_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* DataQuery_dataType_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_datacapture_2eCommunicate_2eproto);
  return file_level_enum_descriptors_datacapture_2eCommunicate_2eproto[1];
}
bool DataQuery_dataType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
constexpr DataQuery_dataType DataQuery::DATA_TYPE_RAW;
constexpr DataQuery_dataType DataQuery::DATA_TYPE_STABILITY;
constexpr DataQuery_dataType DataQuery::DATA_TYPE_TSARI;
constexpr DataQuery_dataType DataQuery::dataType_MIN;
constexpr DataQuery_dataType DataQuery::dataType_MAX;
constexpr int DataQuery::dataType_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ConnectArgs_ConnectType_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_datacapture_2eCommunicate_2eproto);
  return file_level_enum_descriptors_datacapture_2eCommunicate_2eproto[2];
}
bool ConnectArgs_ConnectType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
constexpr ConnectArgs_ConnectType ConnectArgs::PROTO_MQTT;
constexpr ConnectArgs_ConnectType ConnectArgs::PROTO_HTTP;
constexpr ConnectArgs_ConnectType ConnectArgs::PROTO_KAFKA;
constexpr ConnectArgs_ConnectType ConnectArgs::PROTO_TCP_SERVER;
constexpr ConnectArgs_ConnectType ConnectArgs::ConnectType_MIN;
constexpr ConnectArgs_ConnectType ConnectArgs::ConnectType_MAX;
constexpr int ConnectArgs::ConnectType_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* CommonCMD_cmdType_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_datacapture_2eCommunicate_2eproto);
  return file_level_enum_descriptors_datacapture_2eCommunicate_2eproto[3];
}
bool CommonCMD_cmdType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
constexpr CommonCMD_cmdType CommonCMD::PROTO_SYSTEM_INIT;
constexpr CommonCMD_cmdType CommonCMD::PROTO_SYSTEM_START;
constexpr CommonCMD_cmdType CommonCMD::PROTO_SYSTEM_STOP;
constexpr CommonCMD_cmdType CommonCMD::PROTO_SYSTEM_PAUSE;
constexpr CommonCMD_cmdType CommonCMD::cmdType_MIN;
constexpr CommonCMD_cmdType CommonCMD::cmdType_MAX;
constexpr int CommonCMD::cmdType_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* SystemStatus_statusType_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_datacapture_2eCommunicate_2eproto);
  return file_level_enum_descriptors_datacapture_2eCommunicate_2eproto[4];
}
bool SystemStatus_statusType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
constexpr SystemStatus_statusType SystemStatus::START;
constexpr SystemStatus_statusType SystemStatus::STOP;
constexpr SystemStatus_statusType SystemStatus::PAUSE;
constexpr SystemStatus_statusType SystemStatus::statusType_MIN;
constexpr SystemStatus_statusType SystemStatus::statusType_MAX;
constexpr int SystemStatus::statusType_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* SystemCmd_statusType_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_datacapture_2eCommunicate_2eproto);
  return file_level_enum_descriptors_datacapture_2eCommunicate_2eproto[5];
}
bool SystemCmd_statusType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
constexpr SystemCmd_statusType SystemCmd::RECONNECTSERVER;
constexpr SystemCmd_statusType SystemCmd::RESTARTSERVER;
constexpr SystemCmd_statusType SystemCmd::REBOOT;
constexpr SystemCmd_statusType SystemCmd::NOCMD;
constexpr SystemCmd_statusType SystemCmd::statusType_MIN;
constexpr SystemCmd_statusType SystemCmd::statusType_MAX;
constexpr int SystemCmd::statusType_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))

// ===================================================================

class Message::_Internal {
 public:
  static const ::datacapture::CommonCMD& commoncmd(const Message* msg);
  static const ::datacapture::ArgsList& argslist(const Message* msg);
  static const ::datacapture::CapStatus& capstatus(const Message* msg);
  static const ::datacapture::DataQuery& dataqueryreq(const Message* msg);
  static const ::datacapture::DataQueryRes& dataqueryres(const Message* msg);
  static const ::datacapture::CompressReqList& compressreq(const Message* msg);
  static const ::datacapture::CompressRes& compressres(const Message* msg);
  static const ::datacapture::Monitor& monitor(const Message* msg);
  static const ::datacapture::RoadInfoList& roadinfolist(const Message* msg);
  static const ::datacapture::SystemStatus& systemstatus(const Message* msg);
  static const ::datacapture::DataQueryBatchesReq& dataquerybatchesreq(const Message* msg);
  static const ::datacapture::DataQueryBatchesRes& dataquerybatchesres(const Message* msg);
  static const ::datacapture::SystemInfo& systeminfo(const Message* msg);
  static const ::datacapture::ClientInfo& clientinfo(const Message* msg);
  static const ::datacapture::SystemCmd& systemcmd(const Message* msg);
  static const ::datacapture::storeInfo& storeinfo(const Message* msg);
  static const ::datacapture::NetConfig& netconfig(const Message* msg);
  static const ::datacapture::SystemLog& systemlog(const Message* msg);
};

const ::datacapture::CommonCMD&
Message::_Internal::commoncmd(const Message* msg) {
  return *msg->_impl_.data_.commoncmd_;
}
const ::datacapture::ArgsList&
Message::_Internal::argslist(const Message* msg) {
  return *msg->_impl_.data_.argslist_;
}
const ::datacapture::CapStatus&
Message::_Internal::capstatus(const Message* msg) {
  return *msg->_impl_.data_.capstatus_;
}
const ::datacapture::DataQuery&
Message::_Internal::dataqueryreq(const Message* msg) {
  return *msg->_impl_.data_.dataqueryreq_;
}
const ::datacapture::DataQueryRes&
Message::_Internal::dataqueryres(const Message* msg) {
  return *msg->_impl_.data_.dataqueryres_;
}
const ::datacapture::CompressReqList&
Message::_Internal::compressreq(const Message* msg) {
  return *msg->_impl_.data_.compressreq_;
}
const ::datacapture::CompressRes&
Message::_Internal::compressres(const Message* msg) {
  return *msg->_impl_.data_.compressres_;
}
const ::datacapture::Monitor&
Message::_Internal::monitor(const Message* msg) {
  return *msg->_impl_.data_.monitor_;
}
const ::datacapture::RoadInfoList&
Message::_Internal::roadinfolist(const Message* msg) {
  return *msg->_impl_.data_.roadinfolist_;
}
const ::datacapture::SystemStatus&
Message::_Internal::systemstatus(const Message* msg) {
  return *msg->_impl_.data_.systemstatus_;
}
const ::datacapture::DataQueryBatchesReq&
Message::_Internal::dataquerybatchesreq(const Message* msg) {
  return *msg->_impl_.data_.dataquerybatchesreq_;
}
const ::datacapture::DataQueryBatchesRes&
Message::_Internal::dataquerybatchesres(const Message* msg) {
  return *msg->_impl_.data_.dataquerybatchesres_;
}
const ::datacapture::SystemInfo&
Message::_Internal::systeminfo(const Message* msg) {
  return *msg->_impl_.data_.systeminfo_;
}
const ::datacapture::ClientInfo&
Message::_Internal::clientinfo(const Message* msg) {
  return *msg->_impl_.data_.clientinfo_;
}
const ::datacapture::SystemCmd&
Message::_Internal::systemcmd(const Message* msg) {
  return *msg->_impl_.data_.systemcmd_;
}
const ::datacapture::storeInfo&
Message::_Internal::storeinfo(const Message* msg) {
  return *msg->_impl_.data_.storeinfo_;
}
const ::datacapture::NetConfig&
Message::_Internal::netconfig(const Message* msg) {
  return *msg->_impl_.data_.netconfig_;
}
const ::datacapture::SystemLog&
Message::_Internal::systemlog(const Message* msg) {
  return *msg->_impl_.data_.systemlog_;
}
void Message::set_allocated_commoncmd(::datacapture::CommonCMD* commoncmd) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_data();
  if (commoncmd) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(commoncmd);
    if (message_arena != submessage_arena) {
      commoncmd = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, commoncmd, submessage_arena);
    }
    set_has_commoncmd();
    _impl_.data_.commoncmd_ = commoncmd;
  }
  // @@protoc_insertion_point(field_set_allocated:datacapture.Message.commonCmd)
}
void Message::set_allocated_argslist(::datacapture::ArgsList* argslist) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_data();
  if (argslist) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(argslist);
    if (message_arena != submessage_arena) {
      argslist = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, argslist, submessage_arena);
    }
    set_has_argslist();
    _impl_.data_.argslist_ = argslist;
  }
  // @@protoc_insertion_point(field_set_allocated:datacapture.Message.argsList)
}
void Message::set_allocated_capstatus(::datacapture::CapStatus* capstatus) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_data();
  if (capstatus) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(capstatus);
    if (message_arena != submessage_arena) {
      capstatus = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, capstatus, submessage_arena);
    }
    set_has_capstatus();
    _impl_.data_.capstatus_ = capstatus;
  }
  // @@protoc_insertion_point(field_set_allocated:datacapture.Message.capStatus)
}
void Message::set_allocated_dataqueryreq(::datacapture::DataQuery* dataqueryreq) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_data();
  if (dataqueryreq) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(dataqueryreq);
    if (message_arena != submessage_arena) {
      dataqueryreq = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, dataqueryreq, submessage_arena);
    }
    set_has_dataqueryreq();
    _impl_.data_.dataqueryreq_ = dataqueryreq;
  }
  // @@protoc_insertion_point(field_set_allocated:datacapture.Message.dataQueryReq)
}
void Message::set_allocated_dataqueryres(::datacapture::DataQueryRes* dataqueryres) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_data();
  if (dataqueryres) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(dataqueryres);
    if (message_arena != submessage_arena) {
      dataqueryres = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, dataqueryres, submessage_arena);
    }
    set_has_dataqueryres();
    _impl_.data_.dataqueryres_ = dataqueryres;
  }
  // @@protoc_insertion_point(field_set_allocated:datacapture.Message.dataQueryRes)
}
void Message::set_allocated_compressreq(::datacapture::CompressReqList* compressreq) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_data();
  if (compressreq) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(compressreq);
    if (message_arena != submessage_arena) {
      compressreq = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, compressreq, submessage_arena);
    }
    set_has_compressreq();
    _impl_.data_.compressreq_ = compressreq;
  }
  // @@protoc_insertion_point(field_set_allocated:datacapture.Message.compressReq)
}
void Message::set_allocated_compressres(::datacapture::CompressRes* compressres) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_data();
  if (compressres) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(compressres);
    if (message_arena != submessage_arena) {
      compressres = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, compressres, submessage_arena);
    }
    set_has_compressres();
    _impl_.data_.compressres_ = compressres;
  }
  // @@protoc_insertion_point(field_set_allocated:datacapture.Message.compressRes)
}
void Message::set_allocated_monitor(::datacapture::Monitor* monitor) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_data();
  if (monitor) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(monitor);
    if (message_arena != submessage_arena) {
      monitor = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, monitor, submessage_arena);
    }
    set_has_monitor();
    _impl_.data_.monitor_ = monitor;
  }
  // @@protoc_insertion_point(field_set_allocated:datacapture.Message.monitor)
}
void Message::set_allocated_roadinfolist(::datacapture::RoadInfoList* roadinfolist) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_data();
  if (roadinfolist) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(roadinfolist);
    if (message_arena != submessage_arena) {
      roadinfolist = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, roadinfolist, submessage_arena);
    }
    set_has_roadinfolist();
    _impl_.data_.roadinfolist_ = roadinfolist;
  }
  // @@protoc_insertion_point(field_set_allocated:datacapture.Message.roadInfoList)
}
void Message::set_allocated_systemstatus(::datacapture::SystemStatus* systemstatus) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_data();
  if (systemstatus) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(systemstatus);
    if (message_arena != submessage_arena) {
      systemstatus = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, systemstatus, submessage_arena);
    }
    set_has_systemstatus();
    _impl_.data_.systemstatus_ = systemstatus;
  }
  // @@protoc_insertion_point(field_set_allocated:datacapture.Message.systemStatus)
}
void Message::set_allocated_dataquerybatchesreq(::datacapture::DataQueryBatchesReq* dataquerybatchesreq) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_data();
  if (dataquerybatchesreq) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(dataquerybatchesreq);
    if (message_arena != submessage_arena) {
      dataquerybatchesreq = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, dataquerybatchesreq, submessage_arena);
    }
    set_has_dataquerybatchesreq();
    _impl_.data_.dataquerybatchesreq_ = dataquerybatchesreq;
  }
  // @@protoc_insertion_point(field_set_allocated:datacapture.Message.dataQueryBatchesReq)
}
void Message::set_allocated_dataquerybatchesres(::datacapture::DataQueryBatchesRes* dataquerybatchesres) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_data();
  if (dataquerybatchesres) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(dataquerybatchesres);
    if (message_arena != submessage_arena) {
      dataquerybatchesres = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, dataquerybatchesres, submessage_arena);
    }
    set_has_dataquerybatchesres();
    _impl_.data_.dataquerybatchesres_ = dataquerybatchesres;
  }
  // @@protoc_insertion_point(field_set_allocated:datacapture.Message.dataQueryBatchesRes)
}
void Message::set_allocated_systeminfo(::datacapture::SystemInfo* systeminfo) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_data();
  if (systeminfo) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(systeminfo);
    if (message_arena != submessage_arena) {
      systeminfo = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, systeminfo, submessage_arena);
    }
    set_has_systeminfo();
    _impl_.data_.systeminfo_ = systeminfo;
  }
  // @@protoc_insertion_point(field_set_allocated:datacapture.Message.systemInfo)
}
void Message::set_allocated_clientinfo(::datacapture::ClientInfo* clientinfo) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_data();
  if (clientinfo) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(clientinfo);
    if (message_arena != submessage_arena) {
      clientinfo = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, clientinfo, submessage_arena);
    }
    set_has_clientinfo();
    _impl_.data_.clientinfo_ = clientinfo;
  }
  // @@protoc_insertion_point(field_set_allocated:datacapture.Message.clientInfo)
}
void Message::set_allocated_systemcmd(::datacapture::SystemCmd* systemcmd) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_data();
  if (systemcmd) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(systemcmd);
    if (message_arena != submessage_arena) {
      systemcmd = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, systemcmd, submessage_arena);
    }
    set_has_systemcmd();
    _impl_.data_.systemcmd_ = systemcmd;
  }
  // @@protoc_insertion_point(field_set_allocated:datacapture.Message.systemcmd)
}
void Message::set_allocated_storeinfo(::datacapture::storeInfo* storeinfo) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_data();
  if (storeinfo) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(storeinfo);
    if (message_arena != submessage_arena) {
      storeinfo = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, storeinfo, submessage_arena);
    }
    set_has_storeinfo();
    _impl_.data_.storeinfo_ = storeinfo;
  }
  // @@protoc_insertion_point(field_set_allocated:datacapture.Message.storeinfo)
}
void Message::set_allocated_netconfig(::datacapture::NetConfig* netconfig) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_data();
  if (netconfig) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(netconfig);
    if (message_arena != submessage_arena) {
      netconfig = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, netconfig, submessage_arena);
    }
    set_has_netconfig();
    _impl_.data_.netconfig_ = netconfig;
  }
  // @@protoc_insertion_point(field_set_allocated:datacapture.Message.netconfig)
}
void Message::set_allocated_systemlog(::datacapture::SystemLog* systemlog) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_data();
  if (systemlog) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(systemlog);
    if (message_arena != submessage_arena) {
      systemlog = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, systemlog, submessage_arena);
    }
    set_has_systemlog();
    _impl_.data_.systemlog_ = systemlog;
  }
  // @@protoc_insertion_point(field_set_allocated:datacapture.Message.systemLog)
}
Message::Message(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:datacapture.Message)
}
Message::Message(const Message& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Message* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.type_){}
    , decltype(_impl_.data_){}
    , /*decltype(_impl_._cached_size_)*/{}
    , /*decltype(_impl_._oneof_case_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _this->_impl_.type_ = from._impl_.type_;
  clear_has_data();
  switch (from.data_case()) {
    case kCommonCmd: {
      _this->_internal_mutable_commoncmd()->::datacapture::CommonCMD::MergeFrom(
          from._internal_commoncmd());
      break;
    }
    case kArgsList: {
      _this->_internal_mutable_argslist()->::datacapture::ArgsList::MergeFrom(
          from._internal_argslist());
      break;
    }
    case kCapStatus: {
      _this->_internal_mutable_capstatus()->::datacapture::CapStatus::MergeFrom(
          from._internal_capstatus());
      break;
    }
    case kDataQueryReq: {
      _this->_internal_mutable_dataqueryreq()->::datacapture::DataQuery::MergeFrom(
          from._internal_dataqueryreq());
      break;
    }
    case kDataQueryRes: {
      _this->_internal_mutable_dataqueryres()->::datacapture::DataQueryRes::MergeFrom(
          from._internal_dataqueryres());
      break;
    }
    case kCompressReq: {
      _this->_internal_mutable_compressreq()->::datacapture::CompressReqList::MergeFrom(
          from._internal_compressreq());
      break;
    }
    case kCompressRes: {
      _this->_internal_mutable_compressres()->::datacapture::CompressRes::MergeFrom(
          from._internal_compressres());
      break;
    }
    case kMonitor: {
      _this->_internal_mutable_monitor()->::datacapture::Monitor::MergeFrom(
          from._internal_monitor());
      break;
    }
    case kRoadInfoList: {
      _this->_internal_mutable_roadinfolist()->::datacapture::RoadInfoList::MergeFrom(
          from._internal_roadinfolist());
      break;
    }
    case kSystemStatus: {
      _this->_internal_mutable_systemstatus()->::datacapture::SystemStatus::MergeFrom(
          from._internal_systemstatus());
      break;
    }
    case kDataQueryBatchesReq: {
      _this->_internal_mutable_dataquerybatchesreq()->::datacapture::DataQueryBatchesReq::MergeFrom(
          from._internal_dataquerybatchesreq());
      break;
    }
    case kDataQueryBatchesRes: {
      _this->_internal_mutable_dataquerybatchesres()->::datacapture::DataQueryBatchesRes::MergeFrom(
          from._internal_dataquerybatchesres());
      break;
    }
    case kSystemInfo: {
      _this->_internal_mutable_systeminfo()->::datacapture::SystemInfo::MergeFrom(
          from._internal_systeminfo());
      break;
    }
    case kClientInfo: {
      _this->_internal_mutable_clientinfo()->::datacapture::ClientInfo::MergeFrom(
          from._internal_clientinfo());
      break;
    }
    case kSystemcmd: {
      _this->_internal_mutable_systemcmd()->::datacapture::SystemCmd::MergeFrom(
          from._internal_systemcmd());
      break;
    }
    case kStoreinfo: {
      _this->_internal_mutable_storeinfo()->::datacapture::storeInfo::MergeFrom(
          from._internal_storeinfo());
      break;
    }
    case kNetconfig: {
      _this->_internal_mutable_netconfig()->::datacapture::NetConfig::MergeFrom(
          from._internal_netconfig());
      break;
    }
    case kSystemLog: {
      _this->_internal_mutable_systemlog()->::datacapture::SystemLog::MergeFrom(
          from._internal_systemlog());
      break;
    }
    case DATA_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:datacapture.Message)
}

inline void Message::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.type_){0}
    , decltype(_impl_.data_){}
    , /*decltype(_impl_._cached_size_)*/{}
    , /*decltype(_impl_._oneof_case_)*/{}
  };
  clear_has_data();
}

Message::~Message() {
  // @@protoc_insertion_point(destructor:datacapture.Message)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Message::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (has_data()) {
    clear_data();
  }
}

void Message::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Message::clear_data() {
// @@protoc_insertion_point(one_of_clear_start:datacapture.Message)
  switch (data_case()) {
    case kCommonCmd: {
      if (GetArenaForAllocation() == nullptr) {
        delete _impl_.data_.commoncmd_;
      }
      break;
    }
    case kArgsList: {
      if (GetArenaForAllocation() == nullptr) {
        delete _impl_.data_.argslist_;
      }
      break;
    }
    case kCapStatus: {
      if (GetArenaForAllocation() == nullptr) {
        delete _impl_.data_.capstatus_;
      }
      break;
    }
    case kDataQueryReq: {
      if (GetArenaForAllocation() == nullptr) {
        delete _impl_.data_.dataqueryreq_;
      }
      break;
    }
    case kDataQueryRes: {
      if (GetArenaForAllocation() == nullptr) {
        delete _impl_.data_.dataqueryres_;
      }
      break;
    }
    case kCompressReq: {
      if (GetArenaForAllocation() == nullptr) {
        delete _impl_.data_.compressreq_;
      }
      break;
    }
    case kCompressRes: {
      if (GetArenaForAllocation() == nullptr) {
        delete _impl_.data_.compressres_;
      }
      break;
    }
    case kMonitor: {
      if (GetArenaForAllocation() == nullptr) {
        delete _impl_.data_.monitor_;
      }
      break;
    }
    case kRoadInfoList: {
      if (GetArenaForAllocation() == nullptr) {
        delete _impl_.data_.roadinfolist_;
      }
      break;
    }
    case kSystemStatus: {
      if (GetArenaForAllocation() == nullptr) {
        delete _impl_.data_.systemstatus_;
      }
      break;
    }
    case kDataQueryBatchesReq: {
      if (GetArenaForAllocation() == nullptr) {
        delete _impl_.data_.dataquerybatchesreq_;
      }
      break;
    }
    case kDataQueryBatchesRes: {
      if (GetArenaForAllocation() == nullptr) {
        delete _impl_.data_.dataquerybatchesres_;
      }
      break;
    }
    case kSystemInfo: {
      if (GetArenaForAllocation() == nullptr) {
        delete _impl_.data_.systeminfo_;
      }
      break;
    }
    case kClientInfo: {
      if (GetArenaForAllocation() == nullptr) {
        delete _impl_.data_.clientinfo_;
      }
      break;
    }
    case kSystemcmd: {
      if (GetArenaForAllocation() == nullptr) {
        delete _impl_.data_.systemcmd_;
      }
      break;
    }
    case kStoreinfo: {
      if (GetArenaForAllocation() == nullptr) {
        delete _impl_.data_.storeinfo_;
      }
      break;
    }
    case kNetconfig: {
      if (GetArenaForAllocation() == nullptr) {
        delete _impl_.data_.netconfig_;
      }
      break;
    }
    case kSystemLog: {
      if (GetArenaForAllocation() == nullptr) {
        delete _impl_.data_.systemlog_;
      }
      break;
    }
    case DATA_NOT_SET: {
      break;
    }
  }
  _impl_._oneof_case_[0] = DATA_NOT_SET;
}


void Message::Clear() {
// @@protoc_insertion_point(message_clear_start:datacapture.Message)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.type_ = 0;
  clear_data();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Message::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .datacapture.Message.MessageType type = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_type(static_cast<::datacapture::Message_MessageType>(val));
        } else
          goto handle_unusual;
        continue;
      // .datacapture.CommonCMD commonCmd = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_commoncmd(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .datacapture.ArgsList argsList = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_argslist(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .datacapture.CapStatus capStatus = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_capstatus(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .datacapture.DataQuery dataQueryReq = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_dataqueryreq(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .datacapture.DataQueryRes dataQueryRes = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_dataqueryres(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .datacapture.CompressReqList compressReq = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          ptr = ctx->ParseMessage(_internal_mutable_compressreq(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .datacapture.CompressRes compressRes = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 66)) {
          ptr = ctx->ParseMessage(_internal_mutable_compressres(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .datacapture.Monitor monitor = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 74)) {
          ptr = ctx->ParseMessage(_internal_mutable_monitor(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .datacapture.RoadInfoList roadInfoList = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 82)) {
          ptr = ctx->ParseMessage(_internal_mutable_roadinfolist(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .datacapture.SystemStatus systemStatus = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 90)) {
          ptr = ctx->ParseMessage(_internal_mutable_systemstatus(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .datacapture.DataQueryBatchesReq dataQueryBatchesReq = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 98)) {
          ptr = ctx->ParseMessage(_internal_mutable_dataquerybatchesreq(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .datacapture.DataQueryBatchesRes dataQueryBatchesRes = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 106)) {
          ptr = ctx->ParseMessage(_internal_mutable_dataquerybatchesres(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .datacapture.SystemInfo systemInfo = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 114)) {
          ptr = ctx->ParseMessage(_internal_mutable_systeminfo(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .datacapture.ClientInfo clientInfo = 15;
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 122)) {
          ptr = ctx->ParseMessage(_internal_mutable_clientinfo(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .datacapture.SystemCmd systemcmd = 16;
      case 16:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 130)) {
          ptr = ctx->ParseMessage(_internal_mutable_systemcmd(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .datacapture.storeInfo storeinfo = 17;
      case 17:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 138)) {
          ptr = ctx->ParseMessage(_internal_mutable_storeinfo(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .datacapture.NetConfig netconfig = 18;
      case 18:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 146)) {
          ptr = ctx->ParseMessage(_internal_mutable_netconfig(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .datacapture.SystemLog systemLog = 19;
      case 19:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 154)) {
          ptr = ctx->ParseMessage(_internal_mutable_systemlog(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Message::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:datacapture.Message)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .datacapture.Message.MessageType type = 1;
  if (this->_internal_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteEnumToArray(
      1, this->_internal_type(), target);
  }

  // .datacapture.CommonCMD commonCmd = 2;
  if (_internal_has_commoncmd()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, _Internal::commoncmd(this),
        _Internal::commoncmd(this).GetCachedSize(), target, stream);
  }

  // .datacapture.ArgsList argsList = 3;
  if (_internal_has_argslist()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(3, _Internal::argslist(this),
        _Internal::argslist(this).GetCachedSize(), target, stream);
  }

  // .datacapture.CapStatus capStatus = 4;
  if (_internal_has_capstatus()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(4, _Internal::capstatus(this),
        _Internal::capstatus(this).GetCachedSize(), target, stream);
  }

  // .datacapture.DataQuery dataQueryReq = 5;
  if (_internal_has_dataqueryreq()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(5, _Internal::dataqueryreq(this),
        _Internal::dataqueryreq(this).GetCachedSize(), target, stream);
  }

  // .datacapture.DataQueryRes dataQueryRes = 6;
  if (_internal_has_dataqueryres()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(6, _Internal::dataqueryres(this),
        _Internal::dataqueryres(this).GetCachedSize(), target, stream);
  }

  // .datacapture.CompressReqList compressReq = 7;
  if (_internal_has_compressreq()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(7, _Internal::compressreq(this),
        _Internal::compressreq(this).GetCachedSize(), target, stream);
  }

  // .datacapture.CompressRes compressRes = 8;
  if (_internal_has_compressres()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(8, _Internal::compressres(this),
        _Internal::compressres(this).GetCachedSize(), target, stream);
  }

  // .datacapture.Monitor monitor = 9;
  if (_internal_has_monitor()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(9, _Internal::monitor(this),
        _Internal::monitor(this).GetCachedSize(), target, stream);
  }

  // .datacapture.RoadInfoList roadInfoList = 10;
  if (_internal_has_roadinfolist()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(10, _Internal::roadinfolist(this),
        _Internal::roadinfolist(this).GetCachedSize(), target, stream);
  }

  // .datacapture.SystemStatus systemStatus = 11;
  if (_internal_has_systemstatus()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(11, _Internal::systemstatus(this),
        _Internal::systemstatus(this).GetCachedSize(), target, stream);
  }

  // .datacapture.DataQueryBatchesReq dataQueryBatchesReq = 12;
  if (_internal_has_dataquerybatchesreq()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(12, _Internal::dataquerybatchesreq(this),
        _Internal::dataquerybatchesreq(this).GetCachedSize(), target, stream);
  }

  // .datacapture.DataQueryBatchesRes dataQueryBatchesRes = 13;
  if (_internal_has_dataquerybatchesres()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(13, _Internal::dataquerybatchesres(this),
        _Internal::dataquerybatchesres(this).GetCachedSize(), target, stream);
  }

  // .datacapture.SystemInfo systemInfo = 14;
  if (_internal_has_systeminfo()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(14, _Internal::systeminfo(this),
        _Internal::systeminfo(this).GetCachedSize(), target, stream);
  }

  // .datacapture.ClientInfo clientInfo = 15;
  if (_internal_has_clientinfo()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(15, _Internal::clientinfo(this),
        _Internal::clientinfo(this).GetCachedSize(), target, stream);
  }

  // .datacapture.SystemCmd systemcmd = 16;
  if (_internal_has_systemcmd()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(16, _Internal::systemcmd(this),
        _Internal::systemcmd(this).GetCachedSize(), target, stream);
  }

  // .datacapture.storeInfo storeinfo = 17;
  if (_internal_has_storeinfo()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(17, _Internal::storeinfo(this),
        _Internal::storeinfo(this).GetCachedSize(), target, stream);
  }

  // .datacapture.NetConfig netconfig = 18;
  if (_internal_has_netconfig()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(18, _Internal::netconfig(this),
        _Internal::netconfig(this).GetCachedSize(), target, stream);
  }

  // .datacapture.SystemLog systemLog = 19;
  if (_internal_has_systemlog()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(19, _Internal::systemlog(this),
        _Internal::systemlog(this).GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:datacapture.Message)
  return target;
}

size_t Message::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:datacapture.Message)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .datacapture.Message.MessageType type = 1;
  if (this->_internal_type() != 0) {
    total_size += 1 +
      ::_pbi::WireFormatLite::EnumSize(this->_internal_type());
  }

  switch (data_case()) {
    // .datacapture.CommonCMD commonCmd = 2;
    case kCommonCmd: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *_impl_.data_.commoncmd_);
      break;
    }
    // .datacapture.ArgsList argsList = 3;
    case kArgsList: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *_impl_.data_.argslist_);
      break;
    }
    // .datacapture.CapStatus capStatus = 4;
    case kCapStatus: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *_impl_.data_.capstatus_);
      break;
    }
    // .datacapture.DataQuery dataQueryReq = 5;
    case kDataQueryReq: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *_impl_.data_.dataqueryreq_);
      break;
    }
    // .datacapture.DataQueryRes dataQueryRes = 6;
    case kDataQueryRes: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *_impl_.data_.dataqueryres_);
      break;
    }
    // .datacapture.CompressReqList compressReq = 7;
    case kCompressReq: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *_impl_.data_.compressreq_);
      break;
    }
    // .datacapture.CompressRes compressRes = 8;
    case kCompressRes: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *_impl_.data_.compressres_);
      break;
    }
    // .datacapture.Monitor monitor = 9;
    case kMonitor: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *_impl_.data_.monitor_);
      break;
    }
    // .datacapture.RoadInfoList roadInfoList = 10;
    case kRoadInfoList: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *_impl_.data_.roadinfolist_);
      break;
    }
    // .datacapture.SystemStatus systemStatus = 11;
    case kSystemStatus: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *_impl_.data_.systemstatus_);
      break;
    }
    // .datacapture.DataQueryBatchesReq dataQueryBatchesReq = 12;
    case kDataQueryBatchesReq: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *_impl_.data_.dataquerybatchesreq_);
      break;
    }
    // .datacapture.DataQueryBatchesRes dataQueryBatchesRes = 13;
    case kDataQueryBatchesRes: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *_impl_.data_.dataquerybatchesres_);
      break;
    }
    // .datacapture.SystemInfo systemInfo = 14;
    case kSystemInfo: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *_impl_.data_.systeminfo_);
      break;
    }
    // .datacapture.ClientInfo clientInfo = 15;
    case kClientInfo: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *_impl_.data_.clientinfo_);
      break;
    }
    // .datacapture.SystemCmd systemcmd = 16;
    case kSystemcmd: {
      total_size += 2 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *_impl_.data_.systemcmd_);
      break;
    }
    // .datacapture.storeInfo storeinfo = 17;
    case kStoreinfo: {
      total_size += 2 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *_impl_.data_.storeinfo_);
      break;
    }
    // .datacapture.NetConfig netconfig = 18;
    case kNetconfig: {
      total_size += 2 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *_impl_.data_.netconfig_);
      break;
    }
    // .datacapture.SystemLog systemLog = 19;
    case kSystemLog: {
      total_size += 2 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *_impl_.data_.systemlog_);
      break;
    }
    case DATA_NOT_SET: {
      break;
    }
  }
  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Message::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Message::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Message::GetClassData() const { return &_class_data_; }


void Message::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Message*>(&to_msg);
  auto& from = static_cast<const Message&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:datacapture.Message)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_type() != 0) {
    _this->_internal_set_type(from._internal_type());
  }
  switch (from.data_case()) {
    case kCommonCmd: {
      _this->_internal_mutable_commoncmd()->::datacapture::CommonCMD::MergeFrom(
          from._internal_commoncmd());
      break;
    }
    case kArgsList: {
      _this->_internal_mutable_argslist()->::datacapture::ArgsList::MergeFrom(
          from._internal_argslist());
      break;
    }
    case kCapStatus: {
      _this->_internal_mutable_capstatus()->::datacapture::CapStatus::MergeFrom(
          from._internal_capstatus());
      break;
    }
    case kDataQueryReq: {
      _this->_internal_mutable_dataqueryreq()->::datacapture::DataQuery::MergeFrom(
          from._internal_dataqueryreq());
      break;
    }
    case kDataQueryRes: {
      _this->_internal_mutable_dataqueryres()->::datacapture::DataQueryRes::MergeFrom(
          from._internal_dataqueryres());
      break;
    }
    case kCompressReq: {
      _this->_internal_mutable_compressreq()->::datacapture::CompressReqList::MergeFrom(
          from._internal_compressreq());
      break;
    }
    case kCompressRes: {
      _this->_internal_mutable_compressres()->::datacapture::CompressRes::MergeFrom(
          from._internal_compressres());
      break;
    }
    case kMonitor: {
      _this->_internal_mutable_monitor()->::datacapture::Monitor::MergeFrom(
          from._internal_monitor());
      break;
    }
    case kRoadInfoList: {
      _this->_internal_mutable_roadinfolist()->::datacapture::RoadInfoList::MergeFrom(
          from._internal_roadinfolist());
      break;
    }
    case kSystemStatus: {
      _this->_internal_mutable_systemstatus()->::datacapture::SystemStatus::MergeFrom(
          from._internal_systemstatus());
      break;
    }
    case kDataQueryBatchesReq: {
      _this->_internal_mutable_dataquerybatchesreq()->::datacapture::DataQueryBatchesReq::MergeFrom(
          from._internal_dataquerybatchesreq());
      break;
    }
    case kDataQueryBatchesRes: {
      _this->_internal_mutable_dataquerybatchesres()->::datacapture::DataQueryBatchesRes::MergeFrom(
          from._internal_dataquerybatchesres());
      break;
    }
    case kSystemInfo: {
      _this->_internal_mutable_systeminfo()->::datacapture::SystemInfo::MergeFrom(
          from._internal_systeminfo());
      break;
    }
    case kClientInfo: {
      _this->_internal_mutable_clientinfo()->::datacapture::ClientInfo::MergeFrom(
          from._internal_clientinfo());
      break;
    }
    case kSystemcmd: {
      _this->_internal_mutable_systemcmd()->::datacapture::SystemCmd::MergeFrom(
          from._internal_systemcmd());
      break;
    }
    case kStoreinfo: {
      _this->_internal_mutable_storeinfo()->::datacapture::storeInfo::MergeFrom(
          from._internal_storeinfo());
      break;
    }
    case kNetconfig: {
      _this->_internal_mutable_netconfig()->::datacapture::NetConfig::MergeFrom(
          from._internal_netconfig());
      break;
    }
    case kSystemLog: {
      _this->_internal_mutable_systemlog()->::datacapture::SystemLog::MergeFrom(
          from._internal_systemlog());
      break;
    }
    case DATA_NOT_SET: {
      break;
    }
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Message::CopyFrom(const Message& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:datacapture.Message)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Message::IsInitialized() const {
  return true;
}

void Message::InternalSwap(Message* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_impl_.type_, other->_impl_.type_);
  swap(_impl_.data_, other->_impl_.data_);
  swap(_impl_._oneof_case_[0], other->_impl_._oneof_case_[0]);
}

::PROTOBUF_NAMESPACE_ID::Metadata Message::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_datacapture_2eCommunicate_2eproto_getter, &descriptor_table_datacapture_2eCommunicate_2eproto_once,
      file_level_metadata_datacapture_2eCommunicate_2eproto[0]);
}

// ===================================================================

class RawData::_Internal {
 public:
};

RawData::RawData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:datacapture.RawData)
}
RawData::RawData(const RawData& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  RawData* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.strcrossroadid_){}
    , decltype(_impl_.strdata_){}
    , decltype(_impl_.llrecvtime_){}
    , decltype(_impl_.lldatatime_){}
    , decltype(_impl_.dwdatalength_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.strcrossroadid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strcrossroadid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_strcrossroadid().empty()) {
    _this->_impl_.strcrossroadid_.Set(from._internal_strcrossroadid(), 
      _this->GetArenaForAllocation());
  }
  _impl_.strdata_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strdata_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_strdata().empty()) {
    _this->_impl_.strdata_.Set(from._internal_strdata(), 
      _this->GetArenaForAllocation());
  }
  ::memcpy(&_impl_.llrecvtime_, &from._impl_.llrecvtime_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.dwdatalength_) -
    reinterpret_cast<char*>(&_impl_.llrecvtime_)) + sizeof(_impl_.dwdatalength_));
  // @@protoc_insertion_point(copy_constructor:datacapture.RawData)
}

inline void RawData::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.strcrossroadid_){}
    , decltype(_impl_.strdata_){}
    , decltype(_impl_.llrecvtime_){uint64_t{0u}}
    , decltype(_impl_.lldatatime_){uint64_t{0u}}
    , decltype(_impl_.dwdatalength_){0u}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.strcrossroadid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strcrossroadid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.strdata_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strdata_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

RawData::~RawData() {
  // @@protoc_insertion_point(destructor:datacapture.RawData)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void RawData::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.strcrossroadid_.Destroy();
  _impl_.strdata_.Destroy();
}

void RawData::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void RawData::Clear() {
// @@protoc_insertion_point(message_clear_start:datacapture.RawData)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.strcrossroadid_.ClearToEmpty();
  _impl_.strdata_.ClearToEmpty();
  ::memset(&_impl_.llrecvtime_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.dwdatalength_) -
      reinterpret_cast<char*>(&_impl_.llrecvtime_)) + sizeof(_impl_.dwdatalength_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RawData::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string strCrossroadId = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_strcrossroadid();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "datacapture.RawData.strCrossroadId"));
        } else
          goto handle_unusual;
        continue;
      // uint64 llRecvTime = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _impl_.llrecvtime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint64 llDataTime = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _impl_.lldatatime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 dwDatalength = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          _impl_.dwdatalength_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string strData = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          auto str = _internal_mutable_strdata();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "datacapture.RawData.strData"));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* RawData::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:datacapture.RawData)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string strCrossroadId = 1;
  if (!this->_internal_strcrossroadid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_strcrossroadid().data(), static_cast<int>(this->_internal_strcrossroadid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "datacapture.RawData.strCrossroadId");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_strcrossroadid(), target);
  }

  // uint64 llRecvTime = 2;
  if (this->_internal_llrecvtime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt64ToArray(2, this->_internal_llrecvtime(), target);
  }

  // uint64 llDataTime = 3;
  if (this->_internal_lldatatime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt64ToArray(3, this->_internal_lldatatime(), target);
  }

  // uint32 dwDatalength = 4;
  if (this->_internal_dwdatalength() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(4, this->_internal_dwdatalength(), target);
  }

  // string strData = 5;
  if (!this->_internal_strdata().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_strdata().data(), static_cast<int>(this->_internal_strdata().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "datacapture.RawData.strData");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_strdata(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:datacapture.RawData)
  return target;
}

size_t RawData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:datacapture.RawData)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string strCrossroadId = 1;
  if (!this->_internal_strcrossroadid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_strcrossroadid());
  }

  // string strData = 5;
  if (!this->_internal_strdata().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_strdata());
  }

  // uint64 llRecvTime = 2;
  if (this->_internal_llrecvtime() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt64SizePlusOne(this->_internal_llrecvtime());
  }

  // uint64 llDataTime = 3;
  if (this->_internal_lldatatime() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt64SizePlusOne(this->_internal_lldatatime());
  }

  // uint32 dwDatalength = 4;
  if (this->_internal_dwdatalength() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(this->_internal_dwdatalength());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData RawData::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    RawData::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*RawData::GetClassData() const { return &_class_data_; }


void RawData::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<RawData*>(&to_msg);
  auto& from = static_cast<const RawData&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:datacapture.RawData)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_strcrossroadid().empty()) {
    _this->_internal_set_strcrossroadid(from._internal_strcrossroadid());
  }
  if (!from._internal_strdata().empty()) {
    _this->_internal_set_strdata(from._internal_strdata());
  }
  if (from._internal_llrecvtime() != 0) {
    _this->_internal_set_llrecvtime(from._internal_llrecvtime());
  }
  if (from._internal_lldatatime() != 0) {
    _this->_internal_set_lldatatime(from._internal_lldatatime());
  }
  if (from._internal_dwdatalength() != 0) {
    _this->_internal_set_dwdatalength(from._internal_dwdatalength());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void RawData::CopyFrom(const RawData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:datacapture.RawData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RawData::IsInitialized() const {
  return true;
}

void RawData::InternalSwap(RawData* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.strcrossroadid_, lhs_arena,
      &other->_impl_.strcrossroadid_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.strdata_, lhs_arena,
      &other->_impl_.strdata_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RawData, _impl_.dwdatalength_)
      + sizeof(RawData::_impl_.dwdatalength_)
      - PROTOBUF_FIELD_OFFSET(RawData, _impl_.llrecvtime_)>(
          reinterpret_cast<char*>(&_impl_.llrecvtime_),
          reinterpret_cast<char*>(&other->_impl_.llrecvtime_));
}

::PROTOBUF_NAMESPACE_ID::Metadata RawData::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_datacapture_2eCommunicate_2eproto_getter, &descriptor_table_datacapture_2eCommunicate_2eproto_once,
      file_level_metadata_datacapture_2eCommunicate_2eproto[1]);
}

// ===================================================================

class CapStatus::_Internal {
 public:
};

CapStatus::CapStatus(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:datacapture.CapStatus)
}
CapStatus::CapStatus(const CapStatus& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  CapStatus* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.strcrossroadid_){}
    , decltype(_impl_.strdescribe_){}
    , decltype(_impl_.lltimestamp_){}
    , decltype(_impl_.biscaptruing_){}
    , decltype(_impl_.ffreq_){}
    , decltype(_impl_.dwrecvdatacnt_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.strcrossroadid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strcrossroadid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_strcrossroadid().empty()) {
    _this->_impl_.strcrossroadid_.Set(from._internal_strcrossroadid(), 
      _this->GetArenaForAllocation());
  }
  _impl_.strdescribe_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strdescribe_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_strdescribe().empty()) {
    _this->_impl_.strdescribe_.Set(from._internal_strdescribe(), 
      _this->GetArenaForAllocation());
  }
  ::memcpy(&_impl_.lltimestamp_, &from._impl_.lltimestamp_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.dwrecvdatacnt_) -
    reinterpret_cast<char*>(&_impl_.lltimestamp_)) + sizeof(_impl_.dwrecvdatacnt_));
  // @@protoc_insertion_point(copy_constructor:datacapture.CapStatus)
}

inline void CapStatus::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.strcrossroadid_){}
    , decltype(_impl_.strdescribe_){}
    , decltype(_impl_.lltimestamp_){uint64_t{0u}}
    , decltype(_impl_.biscaptruing_){false}
    , decltype(_impl_.ffreq_){0}
    , decltype(_impl_.dwrecvdatacnt_){0u}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.strcrossroadid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strcrossroadid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.strdescribe_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strdescribe_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

CapStatus::~CapStatus() {
  // @@protoc_insertion_point(destructor:datacapture.CapStatus)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void CapStatus::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.strcrossroadid_.Destroy();
  _impl_.strdescribe_.Destroy();
}

void CapStatus::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void CapStatus::Clear() {
// @@protoc_insertion_point(message_clear_start:datacapture.CapStatus)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.strcrossroadid_.ClearToEmpty();
  _impl_.strdescribe_.ClearToEmpty();
  ::memset(&_impl_.lltimestamp_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.dwrecvdatacnt_) -
      reinterpret_cast<char*>(&_impl_.lltimestamp_)) + sizeof(_impl_.dwrecvdatacnt_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CapStatus::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string strCrossroadId = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_strcrossroadid();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "datacapture.CapStatus.strCrossroadId"));
        } else
          goto handle_unusual;
        continue;
      // string strDescribe = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_strdescribe();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "datacapture.CapStatus.strDescribe"));
        } else
          goto handle_unusual;
        continue;
      // uint64 llTimestamp = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _impl_.lltimestamp_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool bIsCaptruing = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          _impl_.biscaptruing_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float fFreq = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 45)) {
          _impl_.ffreq_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // uint32 dwRecvDataCnt = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          _impl_.dwrecvdatacnt_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* CapStatus::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:datacapture.CapStatus)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string strCrossroadId = 1;
  if (!this->_internal_strcrossroadid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_strcrossroadid().data(), static_cast<int>(this->_internal_strcrossroadid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "datacapture.CapStatus.strCrossroadId");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_strcrossroadid(), target);
  }

  // string strDescribe = 2;
  if (!this->_internal_strdescribe().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_strdescribe().data(), static_cast<int>(this->_internal_strdescribe().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "datacapture.CapStatus.strDescribe");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_strdescribe(), target);
  }

  // uint64 llTimestamp = 3;
  if (this->_internal_lltimestamp() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt64ToArray(3, this->_internal_lltimestamp(), target);
  }

  // bool bIsCaptruing = 4;
  if (this->_internal_biscaptruing() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(4, this->_internal_biscaptruing(), target);
  }

  // float fFreq = 5;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_ffreq = this->_internal_ffreq();
  uint32_t raw_ffreq;
  memcpy(&raw_ffreq, &tmp_ffreq, sizeof(tmp_ffreq));
  if (raw_ffreq != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(5, this->_internal_ffreq(), target);
  }

  // uint32 dwRecvDataCnt = 6;
  if (this->_internal_dwrecvdatacnt() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(6, this->_internal_dwrecvdatacnt(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:datacapture.CapStatus)
  return target;
}

size_t CapStatus::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:datacapture.CapStatus)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string strCrossroadId = 1;
  if (!this->_internal_strcrossroadid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_strcrossroadid());
  }

  // string strDescribe = 2;
  if (!this->_internal_strdescribe().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_strdescribe());
  }

  // uint64 llTimestamp = 3;
  if (this->_internal_lltimestamp() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt64SizePlusOne(this->_internal_lltimestamp());
  }

  // bool bIsCaptruing = 4;
  if (this->_internal_biscaptruing() != 0) {
    total_size += 1 + 1;
  }

  // float fFreq = 5;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_ffreq = this->_internal_ffreq();
  uint32_t raw_ffreq;
  memcpy(&raw_ffreq, &tmp_ffreq, sizeof(tmp_ffreq));
  if (raw_ffreq != 0) {
    total_size += 1 + 4;
  }

  // uint32 dwRecvDataCnt = 6;
  if (this->_internal_dwrecvdatacnt() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(this->_internal_dwrecvdatacnt());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData CapStatus::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    CapStatus::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*CapStatus::GetClassData() const { return &_class_data_; }


void CapStatus::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<CapStatus*>(&to_msg);
  auto& from = static_cast<const CapStatus&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:datacapture.CapStatus)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_strcrossroadid().empty()) {
    _this->_internal_set_strcrossroadid(from._internal_strcrossroadid());
  }
  if (!from._internal_strdescribe().empty()) {
    _this->_internal_set_strdescribe(from._internal_strdescribe());
  }
  if (from._internal_lltimestamp() != 0) {
    _this->_internal_set_lltimestamp(from._internal_lltimestamp());
  }
  if (from._internal_biscaptruing() != 0) {
    _this->_internal_set_biscaptruing(from._internal_biscaptruing());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_ffreq = from._internal_ffreq();
  uint32_t raw_ffreq;
  memcpy(&raw_ffreq, &tmp_ffreq, sizeof(tmp_ffreq));
  if (raw_ffreq != 0) {
    _this->_internal_set_ffreq(from._internal_ffreq());
  }
  if (from._internal_dwrecvdatacnt() != 0) {
    _this->_internal_set_dwrecvdatacnt(from._internal_dwrecvdatacnt());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void CapStatus::CopyFrom(const CapStatus& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:datacapture.CapStatus)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CapStatus::IsInitialized() const {
  return true;
}

void CapStatus::InternalSwap(CapStatus* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.strcrossroadid_, lhs_arena,
      &other->_impl_.strcrossroadid_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.strdescribe_, lhs_arena,
      &other->_impl_.strdescribe_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(CapStatus, _impl_.dwrecvdatacnt_)
      + sizeof(CapStatus::_impl_.dwrecvdatacnt_)
      - PROTOBUF_FIELD_OFFSET(CapStatus, _impl_.lltimestamp_)>(
          reinterpret_cast<char*>(&_impl_.lltimestamp_),
          reinterpret_cast<char*>(&other->_impl_.lltimestamp_));
}

::PROTOBUF_NAMESPACE_ID::Metadata CapStatus::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_datacapture_2eCommunicate_2eproto_getter, &descriptor_table_datacapture_2eCommunicate_2eproto_once,
      file_level_metadata_datacapture_2eCommunicate_2eproto[2]);
}

// ===================================================================

class Monitor::_Internal {
 public:
};

Monitor::Monitor(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:datacapture.Monitor)
}
Monitor::Monitor(const Monitor& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Monitor* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.statuslist_){from._impl_.statuslist_}
    , decltype(_impl_.llupdatetime_){}
    , decltype(_impl_.llstarttime_){}
    , decltype(_impl_.lldurationtime_){}
    , decltype(_impl_.dwstatuscnt_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.llupdatetime_, &from._impl_.llupdatetime_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.dwstatuscnt_) -
    reinterpret_cast<char*>(&_impl_.llupdatetime_)) + sizeof(_impl_.dwstatuscnt_));
  // @@protoc_insertion_point(copy_constructor:datacapture.Monitor)
}

inline void Monitor::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.statuslist_){arena}
    , decltype(_impl_.llupdatetime_){uint64_t{0u}}
    , decltype(_impl_.llstarttime_){uint64_t{0u}}
    , decltype(_impl_.lldurationtime_){uint64_t{0u}}
    , decltype(_impl_.dwstatuscnt_){0u}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

Monitor::~Monitor() {
  // @@protoc_insertion_point(destructor:datacapture.Monitor)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Monitor::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.statuslist_.~RepeatedPtrField();
}

void Monitor::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Monitor::Clear() {
// @@protoc_insertion_point(message_clear_start:datacapture.Monitor)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.statuslist_.Clear();
  ::memset(&_impl_.llupdatetime_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.dwstatuscnt_) -
      reinterpret_cast<char*>(&_impl_.llupdatetime_)) + sizeof(_impl_.dwstatuscnt_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Monitor::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 dwStatusCnt = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.dwstatuscnt_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint64 llUpdateTime = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _impl_.llupdatetime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint64 llStartTime = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _impl_.llstarttime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint64 llDurationTime = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          _impl_.lldurationtime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .datacapture.CapStatus statusList = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_statuslist(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<42>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Monitor::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:datacapture.Monitor)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 dwStatusCnt = 1;
  if (this->_internal_dwstatuscnt() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(1, this->_internal_dwstatuscnt(), target);
  }

  // uint64 llUpdateTime = 2;
  if (this->_internal_llupdatetime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt64ToArray(2, this->_internal_llupdatetime(), target);
  }

  // uint64 llStartTime = 3;
  if (this->_internal_llstarttime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt64ToArray(3, this->_internal_llstarttime(), target);
  }

  // uint64 llDurationTime = 4;
  if (this->_internal_lldurationtime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt64ToArray(4, this->_internal_lldurationtime(), target);
  }

  // repeated .datacapture.CapStatus statusList = 5;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_statuslist_size()); i < n; i++) {
    const auto& repfield = this->_internal_statuslist(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(5, repfield, repfield.GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:datacapture.Monitor)
  return target;
}

size_t Monitor::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:datacapture.Monitor)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .datacapture.CapStatus statusList = 5;
  total_size += 1UL * this->_internal_statuslist_size();
  for (const auto& msg : this->_impl_.statuslist_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // uint64 llUpdateTime = 2;
  if (this->_internal_llupdatetime() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt64SizePlusOne(this->_internal_llupdatetime());
  }

  // uint64 llStartTime = 3;
  if (this->_internal_llstarttime() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt64SizePlusOne(this->_internal_llstarttime());
  }

  // uint64 llDurationTime = 4;
  if (this->_internal_lldurationtime() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt64SizePlusOne(this->_internal_lldurationtime());
  }

  // uint32 dwStatusCnt = 1;
  if (this->_internal_dwstatuscnt() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(this->_internal_dwstatuscnt());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Monitor::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Monitor::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Monitor::GetClassData() const { return &_class_data_; }


void Monitor::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Monitor*>(&to_msg);
  auto& from = static_cast<const Monitor&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:datacapture.Monitor)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.statuslist_.MergeFrom(from._impl_.statuslist_);
  if (from._internal_llupdatetime() != 0) {
    _this->_internal_set_llupdatetime(from._internal_llupdatetime());
  }
  if (from._internal_llstarttime() != 0) {
    _this->_internal_set_llstarttime(from._internal_llstarttime());
  }
  if (from._internal_lldurationtime() != 0) {
    _this->_internal_set_lldurationtime(from._internal_lldurationtime());
  }
  if (from._internal_dwstatuscnt() != 0) {
    _this->_internal_set_dwstatuscnt(from._internal_dwstatuscnt());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Monitor::CopyFrom(const Monitor& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:datacapture.Monitor)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Monitor::IsInitialized() const {
  return true;
}

void Monitor::InternalSwap(Monitor* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.statuslist_.InternalSwap(&other->_impl_.statuslist_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Monitor, _impl_.dwstatuscnt_)
      + sizeof(Monitor::_impl_.dwstatuscnt_)
      - PROTOBUF_FIELD_OFFSET(Monitor, _impl_.llupdatetime_)>(
          reinterpret_cast<char*>(&_impl_.llupdatetime_),
          reinterpret_cast<char*>(&other->_impl_.llupdatetime_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Monitor::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_datacapture_2eCommunicate_2eproto_getter, &descriptor_table_datacapture_2eCommunicate_2eproto_once,
      file_level_metadata_datacapture_2eCommunicate_2eproto[3]);
}

// ===================================================================

class DataQuery::_Internal {
 public:
};

DataQuery::DataQuery(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:datacapture.DataQuery)
}
DataQuery::DataQuery(const DataQuery& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  DataQuery* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.strcrossroadid_){}
    , decltype(_impl_.llstarttime_){}
    , decltype(_impl_.llendtime_){}
    , decltype(_impl_.type_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.strcrossroadid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strcrossroadid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_strcrossroadid().empty()) {
    _this->_impl_.strcrossroadid_.Set(from._internal_strcrossroadid(), 
      _this->GetArenaForAllocation());
  }
  ::memcpy(&_impl_.llstarttime_, &from._impl_.llstarttime_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.type_) -
    reinterpret_cast<char*>(&_impl_.llstarttime_)) + sizeof(_impl_.type_));
  // @@protoc_insertion_point(copy_constructor:datacapture.DataQuery)
}

inline void DataQuery::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.strcrossroadid_){}
    , decltype(_impl_.llstarttime_){uint64_t{0u}}
    , decltype(_impl_.llendtime_){uint64_t{0u}}
    , decltype(_impl_.type_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.strcrossroadid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strcrossroadid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

DataQuery::~DataQuery() {
  // @@protoc_insertion_point(destructor:datacapture.DataQuery)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void DataQuery::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.strcrossroadid_.Destroy();
}

void DataQuery::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void DataQuery::Clear() {
// @@protoc_insertion_point(message_clear_start:datacapture.DataQuery)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.strcrossroadid_.ClearToEmpty();
  ::memset(&_impl_.llstarttime_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.type_) -
      reinterpret_cast<char*>(&_impl_.llstarttime_)) + sizeof(_impl_.type_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DataQuery::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string strCrossroadId = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_strcrossroadid();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "datacapture.DataQuery.strCrossroadId"));
        } else
          goto handle_unusual;
        continue;
      // .datacapture.DataQuery.dataType type = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_type(static_cast<::datacapture::DataQuery_dataType>(val));
        } else
          goto handle_unusual;
        continue;
      // uint64 llStartTime = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _impl_.llstarttime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint64 llEndTime = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          _impl_.llendtime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DataQuery::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:datacapture.DataQuery)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string strCrossroadId = 1;
  if (!this->_internal_strcrossroadid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_strcrossroadid().data(), static_cast<int>(this->_internal_strcrossroadid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "datacapture.DataQuery.strCrossroadId");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_strcrossroadid(), target);
  }

  // .datacapture.DataQuery.dataType type = 2;
  if (this->_internal_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteEnumToArray(
      2, this->_internal_type(), target);
  }

  // uint64 llStartTime = 3;
  if (this->_internal_llstarttime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt64ToArray(3, this->_internal_llstarttime(), target);
  }

  // uint64 llEndTime = 4;
  if (this->_internal_llendtime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt64ToArray(4, this->_internal_llendtime(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:datacapture.DataQuery)
  return target;
}

size_t DataQuery::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:datacapture.DataQuery)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string strCrossroadId = 1;
  if (!this->_internal_strcrossroadid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_strcrossroadid());
  }

  // uint64 llStartTime = 3;
  if (this->_internal_llstarttime() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt64SizePlusOne(this->_internal_llstarttime());
  }

  // uint64 llEndTime = 4;
  if (this->_internal_llendtime() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt64SizePlusOne(this->_internal_llendtime());
  }

  // .datacapture.DataQuery.dataType type = 2;
  if (this->_internal_type() != 0) {
    total_size += 1 +
      ::_pbi::WireFormatLite::EnumSize(this->_internal_type());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DataQuery::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    DataQuery::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DataQuery::GetClassData() const { return &_class_data_; }


void DataQuery::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<DataQuery*>(&to_msg);
  auto& from = static_cast<const DataQuery&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:datacapture.DataQuery)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_strcrossroadid().empty()) {
    _this->_internal_set_strcrossroadid(from._internal_strcrossroadid());
  }
  if (from._internal_llstarttime() != 0) {
    _this->_internal_set_llstarttime(from._internal_llstarttime());
  }
  if (from._internal_llendtime() != 0) {
    _this->_internal_set_llendtime(from._internal_llendtime());
  }
  if (from._internal_type() != 0) {
    _this->_internal_set_type(from._internal_type());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DataQuery::CopyFrom(const DataQuery& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:datacapture.DataQuery)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DataQuery::IsInitialized() const {
  return true;
}

void DataQuery::InternalSwap(DataQuery* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.strcrossroadid_, lhs_arena,
      &other->_impl_.strcrossroadid_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(DataQuery, _impl_.type_)
      + sizeof(DataQuery::_impl_.type_)
      - PROTOBUF_FIELD_OFFSET(DataQuery, _impl_.llstarttime_)>(
          reinterpret_cast<char*>(&_impl_.llstarttime_),
          reinterpret_cast<char*>(&other->_impl_.llstarttime_));
}

::PROTOBUF_NAMESPACE_ID::Metadata DataQuery::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_datacapture_2eCommunicate_2eproto_getter, &descriptor_table_datacapture_2eCommunicate_2eproto_once,
      file_level_metadata_datacapture_2eCommunicate_2eproto[4]);
}

// ===================================================================

class DataQueryRes::_Internal {
 public:
};

DataQueryRes::DataQueryRes(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:datacapture.DataQueryRes)
}
DataQueryRes::DataQueryRes(const DataQueryRes& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  DataQueryRes* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.datalist_){from._impl_.datalist_}
    , decltype(_impl_.strerr_){}
    , decltype(_impl_.dwcnt_){}
    , decltype(_impl_.bissucceed_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.strerr_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strerr_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_strerr().empty()) {
    _this->_impl_.strerr_.Set(from._internal_strerr(), 
      _this->GetArenaForAllocation());
  }
  ::memcpy(&_impl_.dwcnt_, &from._impl_.dwcnt_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.bissucceed_) -
    reinterpret_cast<char*>(&_impl_.dwcnt_)) + sizeof(_impl_.bissucceed_));
  // @@protoc_insertion_point(copy_constructor:datacapture.DataQueryRes)
}

inline void DataQueryRes::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.datalist_){arena}
    , decltype(_impl_.strerr_){}
    , decltype(_impl_.dwcnt_){0u}
    , decltype(_impl_.bissucceed_){false}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.strerr_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strerr_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

DataQueryRes::~DataQueryRes() {
  // @@protoc_insertion_point(destructor:datacapture.DataQueryRes)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void DataQueryRes::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.datalist_.~RepeatedPtrField();
  _impl_.strerr_.Destroy();
}

void DataQueryRes::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void DataQueryRes::Clear() {
// @@protoc_insertion_point(message_clear_start:datacapture.DataQueryRes)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.datalist_.Clear();
  _impl_.strerr_.ClearToEmpty();
  ::memset(&_impl_.dwcnt_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.bissucceed_) -
      reinterpret_cast<char*>(&_impl_.dwcnt_)) + sizeof(_impl_.bissucceed_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DataQueryRes::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 dwCnt = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.dwcnt_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool bIsSucceed = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _impl_.bissucceed_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string strErr = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_strerr();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "datacapture.DataQueryRes.strErr"));
        } else
          goto handle_unusual;
        continue;
      // repeated .datacapture.RawData dataList = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_datalist(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<34>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DataQueryRes::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:datacapture.DataQueryRes)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 dwCnt = 1;
  if (this->_internal_dwcnt() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(1, this->_internal_dwcnt(), target);
  }

  // bool bIsSucceed = 2;
  if (this->_internal_bissucceed() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(2, this->_internal_bissucceed(), target);
  }

  // string strErr = 3;
  if (!this->_internal_strerr().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_strerr().data(), static_cast<int>(this->_internal_strerr().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "datacapture.DataQueryRes.strErr");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_strerr(), target);
  }

  // repeated .datacapture.RawData dataList = 4;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_datalist_size()); i < n; i++) {
    const auto& repfield = this->_internal_datalist(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(4, repfield, repfield.GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:datacapture.DataQueryRes)
  return target;
}

size_t DataQueryRes::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:datacapture.DataQueryRes)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .datacapture.RawData dataList = 4;
  total_size += 1UL * this->_internal_datalist_size();
  for (const auto& msg : this->_impl_.datalist_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string strErr = 3;
  if (!this->_internal_strerr().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_strerr());
  }

  // uint32 dwCnt = 1;
  if (this->_internal_dwcnt() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(this->_internal_dwcnt());
  }

  // bool bIsSucceed = 2;
  if (this->_internal_bissucceed() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DataQueryRes::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    DataQueryRes::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DataQueryRes::GetClassData() const { return &_class_data_; }


void DataQueryRes::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<DataQueryRes*>(&to_msg);
  auto& from = static_cast<const DataQueryRes&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:datacapture.DataQueryRes)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.datalist_.MergeFrom(from._impl_.datalist_);
  if (!from._internal_strerr().empty()) {
    _this->_internal_set_strerr(from._internal_strerr());
  }
  if (from._internal_dwcnt() != 0) {
    _this->_internal_set_dwcnt(from._internal_dwcnt());
  }
  if (from._internal_bissucceed() != 0) {
    _this->_internal_set_bissucceed(from._internal_bissucceed());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DataQueryRes::CopyFrom(const DataQueryRes& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:datacapture.DataQueryRes)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DataQueryRes::IsInitialized() const {
  return true;
}

void DataQueryRes::InternalSwap(DataQueryRes* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.datalist_.InternalSwap(&other->_impl_.datalist_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.strerr_, lhs_arena,
      &other->_impl_.strerr_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(DataQueryRes, _impl_.bissucceed_)
      + sizeof(DataQueryRes::_impl_.bissucceed_)
      - PROTOBUF_FIELD_OFFSET(DataQueryRes, _impl_.dwcnt_)>(
          reinterpret_cast<char*>(&_impl_.dwcnt_),
          reinterpret_cast<char*>(&other->_impl_.dwcnt_));
}

::PROTOBUF_NAMESPACE_ID::Metadata DataQueryRes::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_datacapture_2eCommunicate_2eproto_getter, &descriptor_table_datacapture_2eCommunicate_2eproto_once,
      file_level_metadata_datacapture_2eCommunicate_2eproto[5]);
}

// ===================================================================

class DataQueryBatchesReq::_Internal {
 public:
};

DataQueryBatchesReq::DataQueryBatchesReq(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:datacapture.DataQueryBatchesReq)
}
DataQueryBatchesReq::DataQueryBatchesReq(const DataQueryBatchesReq& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  DataQueryBatchesReq* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.strcrossid_){}
    , decltype(_impl_.llstarttime_){}
    , decltype(_impl_.llendtime_){}
    , decltype(_impl_.dwnowpage_){}
    , decltype(_impl_.dwpagesize_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.strcrossid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strcrossid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_strcrossid().empty()) {
    _this->_impl_.strcrossid_.Set(from._internal_strcrossid(), 
      _this->GetArenaForAllocation());
  }
  ::memcpy(&_impl_.llstarttime_, &from._impl_.llstarttime_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.dwpagesize_) -
    reinterpret_cast<char*>(&_impl_.llstarttime_)) + sizeof(_impl_.dwpagesize_));
  // @@protoc_insertion_point(copy_constructor:datacapture.DataQueryBatchesReq)
}

inline void DataQueryBatchesReq::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.strcrossid_){}
    , decltype(_impl_.llstarttime_){uint64_t{0u}}
    , decltype(_impl_.llendtime_){uint64_t{0u}}
    , decltype(_impl_.dwnowpage_){0u}
    , decltype(_impl_.dwpagesize_){0u}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.strcrossid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strcrossid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

DataQueryBatchesReq::~DataQueryBatchesReq() {
  // @@protoc_insertion_point(destructor:datacapture.DataQueryBatchesReq)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void DataQueryBatchesReq::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.strcrossid_.Destroy();
}

void DataQueryBatchesReq::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void DataQueryBatchesReq::Clear() {
// @@protoc_insertion_point(message_clear_start:datacapture.DataQueryBatchesReq)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.strcrossid_.ClearToEmpty();
  ::memset(&_impl_.llstarttime_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.dwpagesize_) -
      reinterpret_cast<char*>(&_impl_.llstarttime_)) + sizeof(_impl_.dwpagesize_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DataQueryBatchesReq::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string strCrossID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_strcrossid();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "datacapture.DataQueryBatchesReq.strCrossID"));
        } else
          goto handle_unusual;
        continue;
      // uint64 llStartTime = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _impl_.llstarttime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint64 llEndTime = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _impl_.llendtime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 dwNowPage = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          _impl_.dwnowpage_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 dwPageSize = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          _impl_.dwpagesize_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DataQueryBatchesReq::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:datacapture.DataQueryBatchesReq)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string strCrossID = 1;
  if (!this->_internal_strcrossid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_strcrossid().data(), static_cast<int>(this->_internal_strcrossid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "datacapture.DataQueryBatchesReq.strCrossID");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_strcrossid(), target);
  }

  // uint64 llStartTime = 2;
  if (this->_internal_llstarttime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt64ToArray(2, this->_internal_llstarttime(), target);
  }

  // uint64 llEndTime = 3;
  if (this->_internal_llendtime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt64ToArray(3, this->_internal_llendtime(), target);
  }

  // uint32 dwNowPage = 4;
  if (this->_internal_dwnowpage() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(4, this->_internal_dwnowpage(), target);
  }

  // uint32 dwPageSize = 5;
  if (this->_internal_dwpagesize() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(5, this->_internal_dwpagesize(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:datacapture.DataQueryBatchesReq)
  return target;
}

size_t DataQueryBatchesReq::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:datacapture.DataQueryBatchesReq)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string strCrossID = 1;
  if (!this->_internal_strcrossid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_strcrossid());
  }

  // uint64 llStartTime = 2;
  if (this->_internal_llstarttime() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt64SizePlusOne(this->_internal_llstarttime());
  }

  // uint64 llEndTime = 3;
  if (this->_internal_llendtime() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt64SizePlusOne(this->_internal_llendtime());
  }

  // uint32 dwNowPage = 4;
  if (this->_internal_dwnowpage() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(this->_internal_dwnowpage());
  }

  // uint32 dwPageSize = 5;
  if (this->_internal_dwpagesize() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(this->_internal_dwpagesize());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DataQueryBatchesReq::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    DataQueryBatchesReq::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DataQueryBatchesReq::GetClassData() const { return &_class_data_; }


void DataQueryBatchesReq::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<DataQueryBatchesReq*>(&to_msg);
  auto& from = static_cast<const DataQueryBatchesReq&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:datacapture.DataQueryBatchesReq)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_strcrossid().empty()) {
    _this->_internal_set_strcrossid(from._internal_strcrossid());
  }
  if (from._internal_llstarttime() != 0) {
    _this->_internal_set_llstarttime(from._internal_llstarttime());
  }
  if (from._internal_llendtime() != 0) {
    _this->_internal_set_llendtime(from._internal_llendtime());
  }
  if (from._internal_dwnowpage() != 0) {
    _this->_internal_set_dwnowpage(from._internal_dwnowpage());
  }
  if (from._internal_dwpagesize() != 0) {
    _this->_internal_set_dwpagesize(from._internal_dwpagesize());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DataQueryBatchesReq::CopyFrom(const DataQueryBatchesReq& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:datacapture.DataQueryBatchesReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DataQueryBatchesReq::IsInitialized() const {
  return true;
}

void DataQueryBatchesReq::InternalSwap(DataQueryBatchesReq* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.strcrossid_, lhs_arena,
      &other->_impl_.strcrossid_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(DataQueryBatchesReq, _impl_.dwpagesize_)
      + sizeof(DataQueryBatchesReq::_impl_.dwpagesize_)
      - PROTOBUF_FIELD_OFFSET(DataQueryBatchesReq, _impl_.llstarttime_)>(
          reinterpret_cast<char*>(&_impl_.llstarttime_),
          reinterpret_cast<char*>(&other->_impl_.llstarttime_));
}

::PROTOBUF_NAMESPACE_ID::Metadata DataQueryBatchesReq::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_datacapture_2eCommunicate_2eproto_getter, &descriptor_table_datacapture_2eCommunicate_2eproto_once,
      file_level_metadata_datacapture_2eCommunicate_2eproto[6]);
}

// ===================================================================

class DataQueryBatchesRes::_Internal {
 public:
};

DataQueryBatchesRes::DataQueryBatchesRes(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:datacapture.DataQueryBatchesRes)
}
DataQueryBatchesRes::DataQueryBatchesRes(const DataQueryBatchesRes& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  DataQueryBatchesRes* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.datalist_){from._impl_.datalist_}
    , decltype(_impl_.strerr_){}
    , decltype(_impl_.bissucceed_){}
    , decltype(_impl_.dwnowpage_){}
    , decltype(_impl_.dwpagesize_){}
    , decltype(_impl_.dwtotalpages_){}
    , decltype(_impl_.dwtotaldatas_){}
    , decltype(_impl_.dwcnt_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.strerr_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strerr_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_strerr().empty()) {
    _this->_impl_.strerr_.Set(from._internal_strerr(), 
      _this->GetArenaForAllocation());
  }
  ::memcpy(&_impl_.bissucceed_, &from._impl_.bissucceed_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.dwcnt_) -
    reinterpret_cast<char*>(&_impl_.bissucceed_)) + sizeof(_impl_.dwcnt_));
  // @@protoc_insertion_point(copy_constructor:datacapture.DataQueryBatchesRes)
}

inline void DataQueryBatchesRes::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.datalist_){arena}
    , decltype(_impl_.strerr_){}
    , decltype(_impl_.bissucceed_){false}
    , decltype(_impl_.dwnowpage_){0u}
    , decltype(_impl_.dwpagesize_){0u}
    , decltype(_impl_.dwtotalpages_){0u}
    , decltype(_impl_.dwtotaldatas_){0u}
    , decltype(_impl_.dwcnt_){0u}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.strerr_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strerr_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

DataQueryBatchesRes::~DataQueryBatchesRes() {
  // @@protoc_insertion_point(destructor:datacapture.DataQueryBatchesRes)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void DataQueryBatchesRes::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.datalist_.~RepeatedPtrField();
  _impl_.strerr_.Destroy();
}

void DataQueryBatchesRes::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void DataQueryBatchesRes::Clear() {
// @@protoc_insertion_point(message_clear_start:datacapture.DataQueryBatchesRes)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.datalist_.Clear();
  _impl_.strerr_.ClearToEmpty();
  ::memset(&_impl_.bissucceed_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.dwcnt_) -
      reinterpret_cast<char*>(&_impl_.bissucceed_)) + sizeof(_impl_.dwcnt_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DataQueryBatchesRes::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bool bIsSucceed = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.bissucceed_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 dwNowPage = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _impl_.dwnowpage_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 dwPageSize = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _impl_.dwpagesize_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 dwTotalPages = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          _impl_.dwtotalpages_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 dwTotalDatas = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          _impl_.dwtotaldatas_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string strErr = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          auto str = _internal_mutable_strerr();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "datacapture.DataQueryBatchesRes.strErr"));
        } else
          goto handle_unusual;
        continue;
      // uint32 dwCnt = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 56)) {
          _impl_.dwcnt_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .datacapture.RawData dataList = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 66)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_datalist(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<66>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DataQueryBatchesRes::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:datacapture.DataQueryBatchesRes)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bool bIsSucceed = 1;
  if (this->_internal_bissucceed() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(1, this->_internal_bissucceed(), target);
  }

  // uint32 dwNowPage = 2;
  if (this->_internal_dwnowpage() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(2, this->_internal_dwnowpage(), target);
  }

  // uint32 dwPageSize = 3;
  if (this->_internal_dwpagesize() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(3, this->_internal_dwpagesize(), target);
  }

  // uint32 dwTotalPages = 4;
  if (this->_internal_dwtotalpages() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(4, this->_internal_dwtotalpages(), target);
  }

  // uint32 dwTotalDatas = 5;
  if (this->_internal_dwtotaldatas() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(5, this->_internal_dwtotaldatas(), target);
  }

  // string strErr = 6;
  if (!this->_internal_strerr().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_strerr().data(), static_cast<int>(this->_internal_strerr().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "datacapture.DataQueryBatchesRes.strErr");
    target = stream->WriteStringMaybeAliased(
        6, this->_internal_strerr(), target);
  }

  // uint32 dwCnt = 7;
  if (this->_internal_dwcnt() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(7, this->_internal_dwcnt(), target);
  }

  // repeated .datacapture.RawData dataList = 8;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_datalist_size()); i < n; i++) {
    const auto& repfield = this->_internal_datalist(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(8, repfield, repfield.GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:datacapture.DataQueryBatchesRes)
  return target;
}

size_t DataQueryBatchesRes::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:datacapture.DataQueryBatchesRes)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .datacapture.RawData dataList = 8;
  total_size += 1UL * this->_internal_datalist_size();
  for (const auto& msg : this->_impl_.datalist_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string strErr = 6;
  if (!this->_internal_strerr().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_strerr());
  }

  // bool bIsSucceed = 1;
  if (this->_internal_bissucceed() != 0) {
    total_size += 1 + 1;
  }

  // uint32 dwNowPage = 2;
  if (this->_internal_dwnowpage() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(this->_internal_dwnowpage());
  }

  // uint32 dwPageSize = 3;
  if (this->_internal_dwpagesize() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(this->_internal_dwpagesize());
  }

  // uint32 dwTotalPages = 4;
  if (this->_internal_dwtotalpages() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(this->_internal_dwtotalpages());
  }

  // uint32 dwTotalDatas = 5;
  if (this->_internal_dwtotaldatas() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(this->_internal_dwtotaldatas());
  }

  // uint32 dwCnt = 7;
  if (this->_internal_dwcnt() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(this->_internal_dwcnt());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DataQueryBatchesRes::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    DataQueryBatchesRes::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DataQueryBatchesRes::GetClassData() const { return &_class_data_; }


void DataQueryBatchesRes::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<DataQueryBatchesRes*>(&to_msg);
  auto& from = static_cast<const DataQueryBatchesRes&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:datacapture.DataQueryBatchesRes)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.datalist_.MergeFrom(from._impl_.datalist_);
  if (!from._internal_strerr().empty()) {
    _this->_internal_set_strerr(from._internal_strerr());
  }
  if (from._internal_bissucceed() != 0) {
    _this->_internal_set_bissucceed(from._internal_bissucceed());
  }
  if (from._internal_dwnowpage() != 0) {
    _this->_internal_set_dwnowpage(from._internal_dwnowpage());
  }
  if (from._internal_dwpagesize() != 0) {
    _this->_internal_set_dwpagesize(from._internal_dwpagesize());
  }
  if (from._internal_dwtotalpages() != 0) {
    _this->_internal_set_dwtotalpages(from._internal_dwtotalpages());
  }
  if (from._internal_dwtotaldatas() != 0) {
    _this->_internal_set_dwtotaldatas(from._internal_dwtotaldatas());
  }
  if (from._internal_dwcnt() != 0) {
    _this->_internal_set_dwcnt(from._internal_dwcnt());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DataQueryBatchesRes::CopyFrom(const DataQueryBatchesRes& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:datacapture.DataQueryBatchesRes)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DataQueryBatchesRes::IsInitialized() const {
  return true;
}

void DataQueryBatchesRes::InternalSwap(DataQueryBatchesRes* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.datalist_.InternalSwap(&other->_impl_.datalist_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.strerr_, lhs_arena,
      &other->_impl_.strerr_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(DataQueryBatchesRes, _impl_.dwcnt_)
      + sizeof(DataQueryBatchesRes::_impl_.dwcnt_)
      - PROTOBUF_FIELD_OFFSET(DataQueryBatchesRes, _impl_.bissucceed_)>(
          reinterpret_cast<char*>(&_impl_.bissucceed_),
          reinterpret_cast<char*>(&other->_impl_.bissucceed_));
}

::PROTOBUF_NAMESPACE_ID::Metadata DataQueryBatchesRes::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_datacapture_2eCommunicate_2eproto_getter, &descriptor_table_datacapture_2eCommunicate_2eproto_once,
      file_level_metadata_datacapture_2eCommunicate_2eproto[7]);
}

// ===================================================================

class CompressReqList::_Internal {
 public:
};

CompressReqList::CompressReqList(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:datacapture.CompressReqList)
}
CompressReqList::CompressReqList(const CompressReqList& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  CompressReqList* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.querylist_){from._impl_.querylist_}
    , decltype(_impl_.strhttpurl_){}
    , decltype(_impl_.strpackagename_){}
    , decltype(_impl_.strserialnum_){}
    , decltype(_impl_.compresstype_){}
    , decltype(_impl_.dwcnt_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.strhttpurl_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strhttpurl_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_strhttpurl().empty()) {
    _this->_impl_.strhttpurl_.Set(from._internal_strhttpurl(), 
      _this->GetArenaForAllocation());
  }
  _impl_.strpackagename_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strpackagename_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_strpackagename().empty()) {
    _this->_impl_.strpackagename_.Set(from._internal_strpackagename(), 
      _this->GetArenaForAllocation());
  }
  _impl_.strserialnum_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strserialnum_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_strserialnum().empty()) {
    _this->_impl_.strserialnum_.Set(from._internal_strserialnum(), 
      _this->GetArenaForAllocation());
  }
  ::memcpy(&_impl_.compresstype_, &from._impl_.compresstype_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.dwcnt_) -
    reinterpret_cast<char*>(&_impl_.compresstype_)) + sizeof(_impl_.dwcnt_));
  // @@protoc_insertion_point(copy_constructor:datacapture.CompressReqList)
}

inline void CompressReqList::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.querylist_){arena}
    , decltype(_impl_.strhttpurl_){}
    , decltype(_impl_.strpackagename_){}
    , decltype(_impl_.strserialnum_){}
    , decltype(_impl_.compresstype_){0u}
    , decltype(_impl_.dwcnt_){0u}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.strhttpurl_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strhttpurl_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.strpackagename_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strpackagename_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.strserialnum_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strserialnum_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

CompressReqList::~CompressReqList() {
  // @@protoc_insertion_point(destructor:datacapture.CompressReqList)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void CompressReqList::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.querylist_.~RepeatedPtrField();
  _impl_.strhttpurl_.Destroy();
  _impl_.strpackagename_.Destroy();
  _impl_.strserialnum_.Destroy();
}

void CompressReqList::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void CompressReqList::Clear() {
// @@protoc_insertion_point(message_clear_start:datacapture.CompressReqList)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.querylist_.Clear();
  _impl_.strhttpurl_.ClearToEmpty();
  _impl_.strpackagename_.ClearToEmpty();
  _impl_.strserialnum_.ClearToEmpty();
  ::memset(&_impl_.compresstype_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.dwcnt_) -
      reinterpret_cast<char*>(&_impl_.compresstype_)) + sizeof(_impl_.dwcnt_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CompressReqList::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 compressType = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.compresstype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 dwCnt = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _impl_.dwcnt_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string strHttpUrl = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_strhttpurl();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "datacapture.CompressReqList.strHttpUrl"));
        } else
          goto handle_unusual;
        continue;
      // string strPackageName = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_strpackagename();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "datacapture.CompressReqList.strPackageName"));
        } else
          goto handle_unusual;
        continue;
      // string strSerialNum = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          auto str = _internal_mutable_strserialnum();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "datacapture.CompressReqList.strSerialNum"));
        } else
          goto handle_unusual;
        continue;
      // repeated .datacapture.DataQuery queryList = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_querylist(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<50>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* CompressReqList::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:datacapture.CompressReqList)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 compressType = 1;
  if (this->_internal_compresstype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(1, this->_internal_compresstype(), target);
  }

  // uint32 dwCnt = 2;
  if (this->_internal_dwcnt() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(2, this->_internal_dwcnt(), target);
  }

  // string strHttpUrl = 3;
  if (!this->_internal_strhttpurl().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_strhttpurl().data(), static_cast<int>(this->_internal_strhttpurl().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "datacapture.CompressReqList.strHttpUrl");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_strhttpurl(), target);
  }

  // string strPackageName = 4;
  if (!this->_internal_strpackagename().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_strpackagename().data(), static_cast<int>(this->_internal_strpackagename().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "datacapture.CompressReqList.strPackageName");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_strpackagename(), target);
  }

  // string strSerialNum = 5;
  if (!this->_internal_strserialnum().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_strserialnum().data(), static_cast<int>(this->_internal_strserialnum().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "datacapture.CompressReqList.strSerialNum");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_strserialnum(), target);
  }

  // repeated .datacapture.DataQuery queryList = 6;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_querylist_size()); i < n; i++) {
    const auto& repfield = this->_internal_querylist(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(6, repfield, repfield.GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:datacapture.CompressReqList)
  return target;
}

size_t CompressReqList::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:datacapture.CompressReqList)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .datacapture.DataQuery queryList = 6;
  total_size += 1UL * this->_internal_querylist_size();
  for (const auto& msg : this->_impl_.querylist_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string strHttpUrl = 3;
  if (!this->_internal_strhttpurl().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_strhttpurl());
  }

  // string strPackageName = 4;
  if (!this->_internal_strpackagename().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_strpackagename());
  }

  // string strSerialNum = 5;
  if (!this->_internal_strserialnum().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_strserialnum());
  }

  // uint32 compressType = 1;
  if (this->_internal_compresstype() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(this->_internal_compresstype());
  }

  // uint32 dwCnt = 2;
  if (this->_internal_dwcnt() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(this->_internal_dwcnt());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData CompressReqList::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    CompressReqList::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*CompressReqList::GetClassData() const { return &_class_data_; }


void CompressReqList::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<CompressReqList*>(&to_msg);
  auto& from = static_cast<const CompressReqList&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:datacapture.CompressReqList)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.querylist_.MergeFrom(from._impl_.querylist_);
  if (!from._internal_strhttpurl().empty()) {
    _this->_internal_set_strhttpurl(from._internal_strhttpurl());
  }
  if (!from._internal_strpackagename().empty()) {
    _this->_internal_set_strpackagename(from._internal_strpackagename());
  }
  if (!from._internal_strserialnum().empty()) {
    _this->_internal_set_strserialnum(from._internal_strserialnum());
  }
  if (from._internal_compresstype() != 0) {
    _this->_internal_set_compresstype(from._internal_compresstype());
  }
  if (from._internal_dwcnt() != 0) {
    _this->_internal_set_dwcnt(from._internal_dwcnt());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void CompressReqList::CopyFrom(const CompressReqList& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:datacapture.CompressReqList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CompressReqList::IsInitialized() const {
  return true;
}

void CompressReqList::InternalSwap(CompressReqList* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.querylist_.InternalSwap(&other->_impl_.querylist_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.strhttpurl_, lhs_arena,
      &other->_impl_.strhttpurl_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.strpackagename_, lhs_arena,
      &other->_impl_.strpackagename_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.strserialnum_, lhs_arena,
      &other->_impl_.strserialnum_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(CompressReqList, _impl_.dwcnt_)
      + sizeof(CompressReqList::_impl_.dwcnt_)
      - PROTOBUF_FIELD_OFFSET(CompressReqList, _impl_.compresstype_)>(
          reinterpret_cast<char*>(&_impl_.compresstype_),
          reinterpret_cast<char*>(&other->_impl_.compresstype_));
}

::PROTOBUF_NAMESPACE_ID::Metadata CompressReqList::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_datacapture_2eCommunicate_2eproto_getter, &descriptor_table_datacapture_2eCommunicate_2eproto_once,
      file_level_metadata_datacapture_2eCommunicate_2eproto[8]);
}

// ===================================================================

class CompressRes::_Internal {
 public:
};

CompressRes::CompressRes(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:datacapture.CompressRes)
}
CompressRes::CompressRes(const CompressRes& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  CompressRes* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.filepath_){}
    , decltype(_impl_.strerr_){}
    , decltype(_impl_.strserialnum_){}
    , decltype(_impl_.strpackagename_){}
    , decltype(_impl_.compresstype_){}
    , decltype(_impl_.bissucceed_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.filepath_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.filepath_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_filepath().empty()) {
    _this->_impl_.filepath_.Set(from._internal_filepath(), 
      _this->GetArenaForAllocation());
  }
  _impl_.strerr_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strerr_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_strerr().empty()) {
    _this->_impl_.strerr_.Set(from._internal_strerr(), 
      _this->GetArenaForAllocation());
  }
  _impl_.strserialnum_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strserialnum_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_strserialnum().empty()) {
    _this->_impl_.strserialnum_.Set(from._internal_strserialnum(), 
      _this->GetArenaForAllocation());
  }
  _impl_.strpackagename_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strpackagename_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_strpackagename().empty()) {
    _this->_impl_.strpackagename_.Set(from._internal_strpackagename(), 
      _this->GetArenaForAllocation());
  }
  ::memcpy(&_impl_.compresstype_, &from._impl_.compresstype_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.bissucceed_) -
    reinterpret_cast<char*>(&_impl_.compresstype_)) + sizeof(_impl_.bissucceed_));
  // @@protoc_insertion_point(copy_constructor:datacapture.CompressRes)
}

inline void CompressRes::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.filepath_){}
    , decltype(_impl_.strerr_){}
    , decltype(_impl_.strserialnum_){}
    , decltype(_impl_.strpackagename_){}
    , decltype(_impl_.compresstype_){0u}
    , decltype(_impl_.bissucceed_){false}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.filepath_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.filepath_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.strerr_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strerr_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.strserialnum_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strserialnum_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.strpackagename_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strpackagename_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

CompressRes::~CompressRes() {
  // @@protoc_insertion_point(destructor:datacapture.CompressRes)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void CompressRes::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.filepath_.Destroy();
  _impl_.strerr_.Destroy();
  _impl_.strserialnum_.Destroy();
  _impl_.strpackagename_.Destroy();
}

void CompressRes::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void CompressRes::Clear() {
// @@protoc_insertion_point(message_clear_start:datacapture.CompressRes)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.filepath_.ClearToEmpty();
  _impl_.strerr_.ClearToEmpty();
  _impl_.strserialnum_.ClearToEmpty();
  _impl_.strpackagename_.ClearToEmpty();
  ::memset(&_impl_.compresstype_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.bissucceed_) -
      reinterpret_cast<char*>(&_impl_.compresstype_)) + sizeof(_impl_.bissucceed_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CompressRes::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 compressType = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.compresstype_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool bIsSucceed = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _impl_.bissucceed_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string filepath = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_filepath();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "datacapture.CompressRes.filepath"));
        } else
          goto handle_unusual;
        continue;
      // string strErr = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_strerr();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "datacapture.CompressRes.strErr"));
        } else
          goto handle_unusual;
        continue;
      // string strSerialNum = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          auto str = _internal_mutable_strserialnum();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "datacapture.CompressRes.strSerialNum"));
        } else
          goto handle_unusual;
        continue;
      // string strPackageName = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          auto str = _internal_mutable_strpackagename();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "datacapture.CompressRes.strPackageName"));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* CompressRes::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:datacapture.CompressRes)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 compressType = 1;
  if (this->_internal_compresstype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(1, this->_internal_compresstype(), target);
  }

  // bool bIsSucceed = 2;
  if (this->_internal_bissucceed() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(2, this->_internal_bissucceed(), target);
  }

  // string filepath = 3;
  if (!this->_internal_filepath().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_filepath().data(), static_cast<int>(this->_internal_filepath().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "datacapture.CompressRes.filepath");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_filepath(), target);
  }

  // string strErr = 4;
  if (!this->_internal_strerr().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_strerr().data(), static_cast<int>(this->_internal_strerr().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "datacapture.CompressRes.strErr");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_strerr(), target);
  }

  // string strSerialNum = 5;
  if (!this->_internal_strserialnum().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_strserialnum().data(), static_cast<int>(this->_internal_strserialnum().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "datacapture.CompressRes.strSerialNum");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_strserialnum(), target);
  }

  // string strPackageName = 6;
  if (!this->_internal_strpackagename().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_strpackagename().data(), static_cast<int>(this->_internal_strpackagename().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "datacapture.CompressRes.strPackageName");
    target = stream->WriteStringMaybeAliased(
        6, this->_internal_strpackagename(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:datacapture.CompressRes)
  return target;
}

size_t CompressRes::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:datacapture.CompressRes)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string filepath = 3;
  if (!this->_internal_filepath().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_filepath());
  }

  // string strErr = 4;
  if (!this->_internal_strerr().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_strerr());
  }

  // string strSerialNum = 5;
  if (!this->_internal_strserialnum().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_strserialnum());
  }

  // string strPackageName = 6;
  if (!this->_internal_strpackagename().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_strpackagename());
  }

  // uint32 compressType = 1;
  if (this->_internal_compresstype() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(this->_internal_compresstype());
  }

  // bool bIsSucceed = 2;
  if (this->_internal_bissucceed() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData CompressRes::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    CompressRes::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*CompressRes::GetClassData() const { return &_class_data_; }


void CompressRes::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<CompressRes*>(&to_msg);
  auto& from = static_cast<const CompressRes&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:datacapture.CompressRes)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_filepath().empty()) {
    _this->_internal_set_filepath(from._internal_filepath());
  }
  if (!from._internal_strerr().empty()) {
    _this->_internal_set_strerr(from._internal_strerr());
  }
  if (!from._internal_strserialnum().empty()) {
    _this->_internal_set_strserialnum(from._internal_strserialnum());
  }
  if (!from._internal_strpackagename().empty()) {
    _this->_internal_set_strpackagename(from._internal_strpackagename());
  }
  if (from._internal_compresstype() != 0) {
    _this->_internal_set_compresstype(from._internal_compresstype());
  }
  if (from._internal_bissucceed() != 0) {
    _this->_internal_set_bissucceed(from._internal_bissucceed());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void CompressRes::CopyFrom(const CompressRes& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:datacapture.CompressRes)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CompressRes::IsInitialized() const {
  return true;
}

void CompressRes::InternalSwap(CompressRes* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.filepath_, lhs_arena,
      &other->_impl_.filepath_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.strerr_, lhs_arena,
      &other->_impl_.strerr_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.strserialnum_, lhs_arena,
      &other->_impl_.strserialnum_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.strpackagename_, lhs_arena,
      &other->_impl_.strpackagename_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(CompressRes, _impl_.bissucceed_)
      + sizeof(CompressRes::_impl_.bissucceed_)
      - PROTOBUF_FIELD_OFFSET(CompressRes, _impl_.compresstype_)>(
          reinterpret_cast<char*>(&_impl_.compresstype_),
          reinterpret_cast<char*>(&other->_impl_.compresstype_));
}

::PROTOBUF_NAMESPACE_ID::Metadata CompressRes::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_datacapture_2eCommunicate_2eproto_getter, &descriptor_table_datacapture_2eCommunicate_2eproto_once,
      file_level_metadata_datacapture_2eCommunicate_2eproto[9]);
}

// ===================================================================

class ConnectArgs::_Internal {
 public:
};

ConnectArgs::ConnectArgs(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:datacapture.ConnectArgs)
}
ConnectArgs::ConnectArgs(const ConnectArgs& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  ConnectArgs* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.strcrossroadid_){}
    , decltype(_impl_.strtopice_){}
    , decltype(_impl_.strpassword_){}
    , decltype(_impl_.strclientid_){}
    , decltype(_impl_.straddr_){}
    , decltype(_impl_.strusername_){}
    , decltype(_impl_.strdescribe_){}
    , decltype(_impl_.dwno_){}
    , decltype(_impl_.isenable_){}
    , decltype(_impl_.type_){}
    , decltype(_impl_.dwfactory_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.strcrossroadid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strcrossroadid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_strcrossroadid().empty()) {
    _this->_impl_.strcrossroadid_.Set(from._internal_strcrossroadid(), 
      _this->GetArenaForAllocation());
  }
  _impl_.strtopice_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strtopice_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_strtopice().empty()) {
    _this->_impl_.strtopice_.Set(from._internal_strtopice(), 
      _this->GetArenaForAllocation());
  }
  _impl_.strpassword_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strpassword_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_strpassword().empty()) {
    _this->_impl_.strpassword_.Set(from._internal_strpassword(), 
      _this->GetArenaForAllocation());
  }
  _impl_.strclientid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strclientid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_strclientid().empty()) {
    _this->_impl_.strclientid_.Set(from._internal_strclientid(), 
      _this->GetArenaForAllocation());
  }
  _impl_.straddr_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.straddr_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_straddr().empty()) {
    _this->_impl_.straddr_.Set(from._internal_straddr(), 
      _this->GetArenaForAllocation());
  }
  _impl_.strusername_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strusername_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_strusername().empty()) {
    _this->_impl_.strusername_.Set(from._internal_strusername(), 
      _this->GetArenaForAllocation());
  }
  _impl_.strdescribe_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strdescribe_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_strdescribe().empty()) {
    _this->_impl_.strdescribe_.Set(from._internal_strdescribe(), 
      _this->GetArenaForAllocation());
  }
  ::memcpy(&_impl_.dwno_, &from._impl_.dwno_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.dwfactory_) -
    reinterpret_cast<char*>(&_impl_.dwno_)) + sizeof(_impl_.dwfactory_));
  // @@protoc_insertion_point(copy_constructor:datacapture.ConnectArgs)
}

inline void ConnectArgs::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.strcrossroadid_){}
    , decltype(_impl_.strtopice_){}
    , decltype(_impl_.strpassword_){}
    , decltype(_impl_.strclientid_){}
    , decltype(_impl_.straddr_){}
    , decltype(_impl_.strusername_){}
    , decltype(_impl_.strdescribe_){}
    , decltype(_impl_.dwno_){0u}
    , decltype(_impl_.isenable_){false}
    , decltype(_impl_.type_){0}
    , decltype(_impl_.dwfactory_){0u}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.strcrossroadid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strcrossroadid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.strtopice_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strtopice_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.strpassword_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strpassword_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.strclientid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strclientid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.straddr_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.straddr_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.strusername_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strusername_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.strdescribe_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strdescribe_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

ConnectArgs::~ConnectArgs() {
  // @@protoc_insertion_point(destructor:datacapture.ConnectArgs)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void ConnectArgs::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.strcrossroadid_.Destroy();
  _impl_.strtopice_.Destroy();
  _impl_.strpassword_.Destroy();
  _impl_.strclientid_.Destroy();
  _impl_.straddr_.Destroy();
  _impl_.strusername_.Destroy();
  _impl_.strdescribe_.Destroy();
}

void ConnectArgs::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void ConnectArgs::Clear() {
// @@protoc_insertion_point(message_clear_start:datacapture.ConnectArgs)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.strcrossroadid_.ClearToEmpty();
  _impl_.strtopice_.ClearToEmpty();
  _impl_.strpassword_.ClearToEmpty();
  _impl_.strclientid_.ClearToEmpty();
  _impl_.straddr_.ClearToEmpty();
  _impl_.strusername_.ClearToEmpty();
  _impl_.strdescribe_.ClearToEmpty();
  ::memset(&_impl_.dwno_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.dwfactory_) -
      reinterpret_cast<char*>(&_impl_.dwno_)) + sizeof(_impl_.dwfactory_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ConnectArgs::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 dwNo = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.dwno_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool isEnable = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _impl_.isenable_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .datacapture.ConnectArgs.ConnectType type = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_type(static_cast<::datacapture::ConnectArgs_ConnectType>(val));
        } else
          goto handle_unusual;
        continue;
      // string strCrossroadId = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_strcrossroadid();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "datacapture.ConnectArgs.strCrossroadId"));
        } else
          goto handle_unusual;
        continue;
      // string strTopice = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          auto str = _internal_mutable_strtopice();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "datacapture.ConnectArgs.strTopice"));
        } else
          goto handle_unusual;
        continue;
      // string strPassword = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          auto str = _internal_mutable_strpassword();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "datacapture.ConnectArgs.strPassword"));
        } else
          goto handle_unusual;
        continue;
      // string strClientId = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          auto str = _internal_mutable_strclientid();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "datacapture.ConnectArgs.strClientId"));
        } else
          goto handle_unusual;
        continue;
      // string strAddr = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 66)) {
          auto str = _internal_mutable_straddr();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "datacapture.ConnectArgs.strAddr"));
        } else
          goto handle_unusual;
        continue;
      // string strUsername = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 74)) {
          auto str = _internal_mutable_strusername();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "datacapture.ConnectArgs.strUsername"));
        } else
          goto handle_unusual;
        continue;
      // string strDescribe = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 82)) {
          auto str = _internal_mutable_strdescribe();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "datacapture.ConnectArgs.strDescribe"));
        } else
          goto handle_unusual;
        continue;
      // uint32 dwFactory = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 88)) {
          _impl_.dwfactory_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ConnectArgs::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:datacapture.ConnectArgs)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 dwNo = 1;
  if (this->_internal_dwno() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(1, this->_internal_dwno(), target);
  }

  // bool isEnable = 2;
  if (this->_internal_isenable() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(2, this->_internal_isenable(), target);
  }

  // .datacapture.ConnectArgs.ConnectType type = 3;
  if (this->_internal_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteEnumToArray(
      3, this->_internal_type(), target);
  }

  // string strCrossroadId = 4;
  if (!this->_internal_strcrossroadid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_strcrossroadid().data(), static_cast<int>(this->_internal_strcrossroadid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "datacapture.ConnectArgs.strCrossroadId");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_strcrossroadid(), target);
  }

  // string strTopice = 5;
  if (!this->_internal_strtopice().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_strtopice().data(), static_cast<int>(this->_internal_strtopice().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "datacapture.ConnectArgs.strTopice");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_strtopice(), target);
  }

  // string strPassword = 6;
  if (!this->_internal_strpassword().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_strpassword().data(), static_cast<int>(this->_internal_strpassword().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "datacapture.ConnectArgs.strPassword");
    target = stream->WriteStringMaybeAliased(
        6, this->_internal_strpassword(), target);
  }

  // string strClientId = 7;
  if (!this->_internal_strclientid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_strclientid().data(), static_cast<int>(this->_internal_strclientid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "datacapture.ConnectArgs.strClientId");
    target = stream->WriteStringMaybeAliased(
        7, this->_internal_strclientid(), target);
  }

  // string strAddr = 8;
  if (!this->_internal_straddr().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_straddr().data(), static_cast<int>(this->_internal_straddr().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "datacapture.ConnectArgs.strAddr");
    target = stream->WriteStringMaybeAliased(
        8, this->_internal_straddr(), target);
  }

  // string strUsername = 9;
  if (!this->_internal_strusername().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_strusername().data(), static_cast<int>(this->_internal_strusername().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "datacapture.ConnectArgs.strUsername");
    target = stream->WriteStringMaybeAliased(
        9, this->_internal_strusername(), target);
  }

  // string strDescribe = 10;
  if (!this->_internal_strdescribe().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_strdescribe().data(), static_cast<int>(this->_internal_strdescribe().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "datacapture.ConnectArgs.strDescribe");
    target = stream->WriteStringMaybeAliased(
        10, this->_internal_strdescribe(), target);
  }

  // uint32 dwFactory = 11;
  if (this->_internal_dwfactory() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(11, this->_internal_dwfactory(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:datacapture.ConnectArgs)
  return target;
}

size_t ConnectArgs::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:datacapture.ConnectArgs)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string strCrossroadId = 4;
  if (!this->_internal_strcrossroadid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_strcrossroadid());
  }

  // string strTopice = 5;
  if (!this->_internal_strtopice().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_strtopice());
  }

  // string strPassword = 6;
  if (!this->_internal_strpassword().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_strpassword());
  }

  // string strClientId = 7;
  if (!this->_internal_strclientid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_strclientid());
  }

  // string strAddr = 8;
  if (!this->_internal_straddr().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_straddr());
  }

  // string strUsername = 9;
  if (!this->_internal_strusername().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_strusername());
  }

  // string strDescribe = 10;
  if (!this->_internal_strdescribe().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_strdescribe());
  }

  // uint32 dwNo = 1;
  if (this->_internal_dwno() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(this->_internal_dwno());
  }

  // bool isEnable = 2;
  if (this->_internal_isenable() != 0) {
    total_size += 1 + 1;
  }

  // .datacapture.ConnectArgs.ConnectType type = 3;
  if (this->_internal_type() != 0) {
    total_size += 1 +
      ::_pbi::WireFormatLite::EnumSize(this->_internal_type());
  }

  // uint32 dwFactory = 11;
  if (this->_internal_dwfactory() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(this->_internal_dwfactory());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ConnectArgs::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    ConnectArgs::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ConnectArgs::GetClassData() const { return &_class_data_; }


void ConnectArgs::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<ConnectArgs*>(&to_msg);
  auto& from = static_cast<const ConnectArgs&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:datacapture.ConnectArgs)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_strcrossroadid().empty()) {
    _this->_internal_set_strcrossroadid(from._internal_strcrossroadid());
  }
  if (!from._internal_strtopice().empty()) {
    _this->_internal_set_strtopice(from._internal_strtopice());
  }
  if (!from._internal_strpassword().empty()) {
    _this->_internal_set_strpassword(from._internal_strpassword());
  }
  if (!from._internal_strclientid().empty()) {
    _this->_internal_set_strclientid(from._internal_strclientid());
  }
  if (!from._internal_straddr().empty()) {
    _this->_internal_set_straddr(from._internal_straddr());
  }
  if (!from._internal_strusername().empty()) {
    _this->_internal_set_strusername(from._internal_strusername());
  }
  if (!from._internal_strdescribe().empty()) {
    _this->_internal_set_strdescribe(from._internal_strdescribe());
  }
  if (from._internal_dwno() != 0) {
    _this->_internal_set_dwno(from._internal_dwno());
  }
  if (from._internal_isenable() != 0) {
    _this->_internal_set_isenable(from._internal_isenable());
  }
  if (from._internal_type() != 0) {
    _this->_internal_set_type(from._internal_type());
  }
  if (from._internal_dwfactory() != 0) {
    _this->_internal_set_dwfactory(from._internal_dwfactory());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ConnectArgs::CopyFrom(const ConnectArgs& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:datacapture.ConnectArgs)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ConnectArgs::IsInitialized() const {
  return true;
}

void ConnectArgs::InternalSwap(ConnectArgs* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.strcrossroadid_, lhs_arena,
      &other->_impl_.strcrossroadid_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.strtopice_, lhs_arena,
      &other->_impl_.strtopice_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.strpassword_, lhs_arena,
      &other->_impl_.strpassword_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.strclientid_, lhs_arena,
      &other->_impl_.strclientid_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.straddr_, lhs_arena,
      &other->_impl_.straddr_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.strusername_, lhs_arena,
      &other->_impl_.strusername_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.strdescribe_, lhs_arena,
      &other->_impl_.strdescribe_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ConnectArgs, _impl_.dwfactory_)
      + sizeof(ConnectArgs::_impl_.dwfactory_)
      - PROTOBUF_FIELD_OFFSET(ConnectArgs, _impl_.dwno_)>(
          reinterpret_cast<char*>(&_impl_.dwno_),
          reinterpret_cast<char*>(&other->_impl_.dwno_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ConnectArgs::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_datacapture_2eCommunicate_2eproto_getter, &descriptor_table_datacapture_2eCommunicate_2eproto_once,
      file_level_metadata_datacapture_2eCommunicate_2eproto[10]);
}

// ===================================================================

class ArgsList::_Internal {
 public:
};

ArgsList::ArgsList(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:datacapture.ArgsList)
}
ArgsList::ArgsList(const ArgsList& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  ArgsList* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.argslist_){from._impl_.argslist_}
    , decltype(_impl_.dwcnt_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _this->_impl_.dwcnt_ = from._impl_.dwcnt_;
  // @@protoc_insertion_point(copy_constructor:datacapture.ArgsList)
}

inline void ArgsList::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.argslist_){arena}
    , decltype(_impl_.dwcnt_){0u}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

ArgsList::~ArgsList() {
  // @@protoc_insertion_point(destructor:datacapture.ArgsList)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void ArgsList::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.argslist_.~RepeatedPtrField();
}

void ArgsList::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void ArgsList::Clear() {
// @@protoc_insertion_point(message_clear_start:datacapture.ArgsList)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.argslist_.Clear();
  _impl_.dwcnt_ = 0u;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ArgsList::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 dwCnt = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.dwcnt_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .datacapture.ConnectArgs argsList = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_argslist(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ArgsList::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:datacapture.ArgsList)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 dwCnt = 1;
  if (this->_internal_dwcnt() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(1, this->_internal_dwcnt(), target);
  }

  // repeated .datacapture.ConnectArgs argsList = 2;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_argslist_size()); i < n; i++) {
    const auto& repfield = this->_internal_argslist(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(2, repfield, repfield.GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:datacapture.ArgsList)
  return target;
}

size_t ArgsList::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:datacapture.ArgsList)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .datacapture.ConnectArgs argsList = 2;
  total_size += 1UL * this->_internal_argslist_size();
  for (const auto& msg : this->_impl_.argslist_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // uint32 dwCnt = 1;
  if (this->_internal_dwcnt() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(this->_internal_dwcnt());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ArgsList::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    ArgsList::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ArgsList::GetClassData() const { return &_class_data_; }


void ArgsList::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<ArgsList*>(&to_msg);
  auto& from = static_cast<const ArgsList&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:datacapture.ArgsList)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.argslist_.MergeFrom(from._impl_.argslist_);
  if (from._internal_dwcnt() != 0) {
    _this->_internal_set_dwcnt(from._internal_dwcnt());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ArgsList::CopyFrom(const ArgsList& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:datacapture.ArgsList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ArgsList::IsInitialized() const {
  return true;
}

void ArgsList::InternalSwap(ArgsList* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.argslist_.InternalSwap(&other->_impl_.argslist_);
  swap(_impl_.dwcnt_, other->_impl_.dwcnt_);
}

::PROTOBUF_NAMESPACE_ID::Metadata ArgsList::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_datacapture_2eCommunicate_2eproto_getter, &descriptor_table_datacapture_2eCommunicate_2eproto_once,
      file_level_metadata_datacapture_2eCommunicate_2eproto[11]);
}

// ===================================================================

class CommonCMD::_Internal {
 public:
};

CommonCMD::CommonCMD(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:datacapture.CommonCMD)
}
CommonCMD::CommonCMD(const CommonCMD& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  CommonCMD* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.type_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _this->_impl_.type_ = from._impl_.type_;
  // @@protoc_insertion_point(copy_constructor:datacapture.CommonCMD)
}

inline void CommonCMD::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.type_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

CommonCMD::~CommonCMD() {
  // @@protoc_insertion_point(destructor:datacapture.CommonCMD)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void CommonCMD::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void CommonCMD::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void CommonCMD::Clear() {
// @@protoc_insertion_point(message_clear_start:datacapture.CommonCMD)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.type_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CommonCMD::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .datacapture.CommonCMD.cmdType type = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_type(static_cast<::datacapture::CommonCMD_cmdType>(val));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* CommonCMD::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:datacapture.CommonCMD)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .datacapture.CommonCMD.cmdType type = 1;
  if (this->_internal_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteEnumToArray(
      1, this->_internal_type(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:datacapture.CommonCMD)
  return target;
}

size_t CommonCMD::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:datacapture.CommonCMD)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .datacapture.CommonCMD.cmdType type = 1;
  if (this->_internal_type() != 0) {
    total_size += 1 +
      ::_pbi::WireFormatLite::EnumSize(this->_internal_type());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData CommonCMD::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    CommonCMD::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*CommonCMD::GetClassData() const { return &_class_data_; }


void CommonCMD::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<CommonCMD*>(&to_msg);
  auto& from = static_cast<const CommonCMD&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:datacapture.CommonCMD)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_type() != 0) {
    _this->_internal_set_type(from._internal_type());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void CommonCMD::CopyFrom(const CommonCMD& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:datacapture.CommonCMD)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CommonCMD::IsInitialized() const {
  return true;
}

void CommonCMD::InternalSwap(CommonCMD* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_impl_.type_, other->_impl_.type_);
}

::PROTOBUF_NAMESPACE_ID::Metadata CommonCMD::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_datacapture_2eCommunicate_2eproto_getter, &descriptor_table_datacapture_2eCommunicate_2eproto_once,
      file_level_metadata_datacapture_2eCommunicate_2eproto[12]);
}

// ===================================================================

class RoadInfo::_Internal {
 public:
};

RoadInfo::RoadInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:datacapture.RoadInfo)
}
RoadInfo::RoadInfo(const RoadInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  RoadInfo* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.strid_){}
    , decltype(_impl_.strdescribe_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.strid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_strid().empty()) {
    _this->_impl_.strid_.Set(from._internal_strid(), 
      _this->GetArenaForAllocation());
  }
  _impl_.strdescribe_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strdescribe_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_strdescribe().empty()) {
    _this->_impl_.strdescribe_.Set(from._internal_strdescribe(), 
      _this->GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:datacapture.RoadInfo)
}

inline void RoadInfo::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.strid_){}
    , decltype(_impl_.strdescribe_){}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.strid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.strdescribe_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.strdescribe_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

RoadInfo::~RoadInfo() {
  // @@protoc_insertion_point(destructor:datacapture.RoadInfo)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void RoadInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.strid_.Destroy();
  _impl_.strdescribe_.Destroy();
}

void RoadInfo::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void RoadInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:datacapture.RoadInfo)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.strid_.ClearToEmpty();
  _impl_.strdescribe_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RoadInfo::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string strID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_strid();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "datacapture.RoadInfo.strID"));
        } else
          goto handle_unusual;
        continue;
      // string strDescribe = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_strdescribe();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "datacapture.RoadInfo.strDescribe"));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* RoadInfo::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:datacapture.RoadInfo)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string strID = 1;
  if (!this->_internal_strid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_strid().data(), static_cast<int>(this->_internal_strid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "datacapture.RoadInfo.strID");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_strid(), target);
  }

  // string strDescribe = 2;
  if (!this->_internal_strdescribe().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_strdescribe().data(), static_cast<int>(this->_internal_strdescribe().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "datacapture.RoadInfo.strDescribe");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_strdescribe(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:datacapture.RoadInfo)
  return target;
}

size_t RoadInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:datacapture.RoadInfo)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string strID = 1;
  if (!this->_internal_strid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_strid());
  }

  // string strDescribe = 2;
  if (!this->_internal_strdescribe().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_strdescribe());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData RoadInfo::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    RoadInfo::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*RoadInfo::GetClassData() const { return &_class_data_; }


void RoadInfo::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<RoadInfo*>(&to_msg);
  auto& from = static_cast<const RoadInfo&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:datacapture.RoadInfo)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_strid().empty()) {
    _this->_internal_set_strid(from._internal_strid());
  }
  if (!from._internal_strdescribe().empty()) {
    _this->_internal_set_strdescribe(from._internal_strdescribe());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void RoadInfo::CopyFrom(const RoadInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:datacapture.RoadInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RoadInfo::IsInitialized() const {
  return true;
}

void RoadInfo::InternalSwap(RoadInfo* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.strid_, lhs_arena,
      &other->_impl_.strid_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.strdescribe_, lhs_arena,
      &other->_impl_.strdescribe_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata RoadInfo::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_datacapture_2eCommunicate_2eproto_getter, &descriptor_table_datacapture_2eCommunicate_2eproto_once,
      file_level_metadata_datacapture_2eCommunicate_2eproto[13]);
}

// ===================================================================

class RoadInfoList::_Internal {
 public:
};

RoadInfoList::RoadInfoList(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:datacapture.RoadInfoList)
}
RoadInfoList::RoadInfoList(const RoadInfoList& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  RoadInfoList* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.list_){from._impl_.list_}
    , decltype(_impl_.dwcnt_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _this->_impl_.dwcnt_ = from._impl_.dwcnt_;
  // @@protoc_insertion_point(copy_constructor:datacapture.RoadInfoList)
}

inline void RoadInfoList::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.list_){arena}
    , decltype(_impl_.dwcnt_){0u}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

RoadInfoList::~RoadInfoList() {
  // @@protoc_insertion_point(destructor:datacapture.RoadInfoList)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void RoadInfoList::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.list_.~RepeatedPtrField();
}

void RoadInfoList::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void RoadInfoList::Clear() {
// @@protoc_insertion_point(message_clear_start:datacapture.RoadInfoList)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.list_.Clear();
  _impl_.dwcnt_ = 0u;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RoadInfoList::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 dwCnt = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.dwcnt_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .datacapture.RoadInfo list = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_list(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* RoadInfoList::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:datacapture.RoadInfoList)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 dwCnt = 1;
  if (this->_internal_dwcnt() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(1, this->_internal_dwcnt(), target);
  }

  // repeated .datacapture.RoadInfo list = 2;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_list_size()); i < n; i++) {
    const auto& repfield = this->_internal_list(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(2, repfield, repfield.GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:datacapture.RoadInfoList)
  return target;
}

size_t RoadInfoList::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:datacapture.RoadInfoList)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .datacapture.RoadInfo list = 2;
  total_size += 1UL * this->_internal_list_size();
  for (const auto& msg : this->_impl_.list_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // uint32 dwCnt = 1;
  if (this->_internal_dwcnt() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(this->_internal_dwcnt());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData RoadInfoList::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    RoadInfoList::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*RoadInfoList::GetClassData() const { return &_class_data_; }


void RoadInfoList::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<RoadInfoList*>(&to_msg);
  auto& from = static_cast<const RoadInfoList&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:datacapture.RoadInfoList)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.list_.MergeFrom(from._impl_.list_);
  if (from._internal_dwcnt() != 0) {
    _this->_internal_set_dwcnt(from._internal_dwcnt());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void RoadInfoList::CopyFrom(const RoadInfoList& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:datacapture.RoadInfoList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RoadInfoList::IsInitialized() const {
  return true;
}

void RoadInfoList::InternalSwap(RoadInfoList* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.list_.InternalSwap(&other->_impl_.list_);
  swap(_impl_.dwcnt_, other->_impl_.dwcnt_);
}

::PROTOBUF_NAMESPACE_ID::Metadata RoadInfoList::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_datacapture_2eCommunicate_2eproto_getter, &descriptor_table_datacapture_2eCommunicate_2eproto_once,
      file_level_metadata_datacapture_2eCommunicate_2eproto[14]);
}

// ===================================================================

class SystemStatus::_Internal {
 public:
};

SystemStatus::SystemStatus(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:datacapture.SystemStatus)
}
SystemStatus::SystemStatus(const SystemStatus& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  SystemStatus* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.lltimestamp_){}
    , decltype(_impl_.type_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.lltimestamp_, &from._impl_.lltimestamp_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.type_) -
    reinterpret_cast<char*>(&_impl_.lltimestamp_)) + sizeof(_impl_.type_));
  // @@protoc_insertion_point(copy_constructor:datacapture.SystemStatus)
}

inline void SystemStatus::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.lltimestamp_){uint64_t{0u}}
    , decltype(_impl_.type_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

SystemStatus::~SystemStatus() {
  // @@protoc_insertion_point(destructor:datacapture.SystemStatus)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void SystemStatus::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void SystemStatus::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void SystemStatus::Clear() {
// @@protoc_insertion_point(message_clear_start:datacapture.SystemStatus)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&_impl_.lltimestamp_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.type_) -
      reinterpret_cast<char*>(&_impl_.lltimestamp_)) + sizeof(_impl_.type_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SystemStatus::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint64 llTimestamp = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.lltimestamp_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .datacapture.SystemStatus.statusType type = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_type(static_cast<::datacapture::SystemStatus_statusType>(val));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SystemStatus::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:datacapture.SystemStatus)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 llTimestamp = 1;
  if (this->_internal_lltimestamp() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt64ToArray(1, this->_internal_lltimestamp(), target);
  }

  // .datacapture.SystemStatus.statusType type = 2;
  if (this->_internal_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteEnumToArray(
      2, this->_internal_type(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:datacapture.SystemStatus)
  return target;
}

size_t SystemStatus::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:datacapture.SystemStatus)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint64 llTimestamp = 1;
  if (this->_internal_lltimestamp() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt64SizePlusOne(this->_internal_lltimestamp());
  }

  // .datacapture.SystemStatus.statusType type = 2;
  if (this->_internal_type() != 0) {
    total_size += 1 +
      ::_pbi::WireFormatLite::EnumSize(this->_internal_type());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SystemStatus::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    SystemStatus::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SystemStatus::GetClassData() const { return &_class_data_; }


void SystemStatus::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<SystemStatus*>(&to_msg);
  auto& from = static_cast<const SystemStatus&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:datacapture.SystemStatus)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_lltimestamp() != 0) {
    _this->_internal_set_lltimestamp(from._internal_lltimestamp());
  }
  if (from._internal_type() != 0) {
    _this->_internal_set_type(from._internal_type());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SystemStatus::CopyFrom(const SystemStatus& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:datacapture.SystemStatus)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SystemStatus::IsInitialized() const {
  return true;
}

void SystemStatus::InternalSwap(SystemStatus* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(SystemStatus, _impl_.type_)
      + sizeof(SystemStatus::_impl_.type_)
      - PROTOBUF_FIELD_OFFSET(SystemStatus, _impl_.lltimestamp_)>(
          reinterpret_cast<char*>(&_impl_.lltimestamp_),
          reinterpret_cast<char*>(&other->_impl_.lltimestamp_));
}

::PROTOBUF_NAMESPACE_ID::Metadata SystemStatus::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_datacapture_2eCommunicate_2eproto_getter, &descriptor_table_datacapture_2eCommunicate_2eproto_once,
      file_level_metadata_datacapture_2eCommunicate_2eproto[15]);
}

// ===================================================================

class SystemInfo::_Internal {
 public:
};

SystemInfo::SystemInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:datacapture.SystemInfo)
}
SystemInfo::SystemInfo(const SystemInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  SystemInfo* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.cpuused_){}
    , decltype(_impl_.cputemp_){}
    , decltype(_impl_.cpufreq_){}
    , decltype(_impl_.cpucorenum_){}
    , decltype(_impl_.memoryused_){}
    , decltype(_impl_.memorytotal_){}
    , decltype(_impl_.diskused_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.cpuused_, &from._impl_.cpuused_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.diskused_) -
    reinterpret_cast<char*>(&_impl_.cpuused_)) + sizeof(_impl_.diskused_));
  // @@protoc_insertion_point(copy_constructor:datacapture.SystemInfo)
}

inline void SystemInfo::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.cpuused_){0}
    , decltype(_impl_.cputemp_){0}
    , decltype(_impl_.cpufreq_){0}
    , decltype(_impl_.cpucorenum_){0}
    , decltype(_impl_.memoryused_){0}
    , decltype(_impl_.memorytotal_){0}
    , decltype(_impl_.diskused_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

SystemInfo::~SystemInfo() {
  // @@protoc_insertion_point(destructor:datacapture.SystemInfo)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void SystemInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void SystemInfo::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void SystemInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:datacapture.SystemInfo)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&_impl_.cpuused_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.diskused_) -
      reinterpret_cast<char*>(&_impl_.cpuused_)) + sizeof(_impl_.diskused_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SystemInfo::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float cpuUsed = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 13)) {
          _impl_.cpuused_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float cpuTemp = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          _impl_.cputemp_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float cpuFreq = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 29)) {
          _impl_.cpufreq_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float cpuCoreNum = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 37)) {
          _impl_.cpucorenum_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float memoryUsed = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 45)) {
          _impl_.memoryused_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float memoryTotal = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 53)) {
          _impl_.memorytotal_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float diskUsed = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 61)) {
          _impl_.diskused_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SystemInfo::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:datacapture.SystemInfo)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // float cpuUsed = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_cpuused = this->_internal_cpuused();
  uint32_t raw_cpuused;
  memcpy(&raw_cpuused, &tmp_cpuused, sizeof(tmp_cpuused));
  if (raw_cpuused != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(1, this->_internal_cpuused(), target);
  }

  // float cpuTemp = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_cputemp = this->_internal_cputemp();
  uint32_t raw_cputemp;
  memcpy(&raw_cputemp, &tmp_cputemp, sizeof(tmp_cputemp));
  if (raw_cputemp != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(2, this->_internal_cputemp(), target);
  }

  // float cpuFreq = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_cpufreq = this->_internal_cpufreq();
  uint32_t raw_cpufreq;
  memcpy(&raw_cpufreq, &tmp_cpufreq, sizeof(tmp_cpufreq));
  if (raw_cpufreq != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(3, this->_internal_cpufreq(), target);
  }

  // float cpuCoreNum = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_cpucorenum = this->_internal_cpucorenum();
  uint32_t raw_cpucorenum;
  memcpy(&raw_cpucorenum, &tmp_cpucorenum, sizeof(tmp_cpucorenum));
  if (raw_cpucorenum != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(4, this->_internal_cpucorenum(), target);
  }

  // float memoryUsed = 5;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_memoryused = this->_internal_memoryused();
  uint32_t raw_memoryused;
  memcpy(&raw_memoryused, &tmp_memoryused, sizeof(tmp_memoryused));
  if (raw_memoryused != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(5, this->_internal_memoryused(), target);
  }

  // float memoryTotal = 6;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_memorytotal = this->_internal_memorytotal();
  uint32_t raw_memorytotal;
  memcpy(&raw_memorytotal, &tmp_memorytotal, sizeof(tmp_memorytotal));
  if (raw_memorytotal != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(6, this->_internal_memorytotal(), target);
  }

  // float diskUsed = 7;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_diskused = this->_internal_diskused();
  uint32_t raw_diskused;
  memcpy(&raw_diskused, &tmp_diskused, sizeof(tmp_diskused));
  if (raw_diskused != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(7, this->_internal_diskused(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:datacapture.SystemInfo)
  return target;
}

size_t SystemInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:datacapture.SystemInfo)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float cpuUsed = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_cpuused = this->_internal_cpuused();
  uint32_t raw_cpuused;
  memcpy(&raw_cpuused, &tmp_cpuused, sizeof(tmp_cpuused));
  if (raw_cpuused != 0) {
    total_size += 1 + 4;
  }

  // float cpuTemp = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_cputemp = this->_internal_cputemp();
  uint32_t raw_cputemp;
  memcpy(&raw_cputemp, &tmp_cputemp, sizeof(tmp_cputemp));
  if (raw_cputemp != 0) {
    total_size += 1 + 4;
  }

  // float cpuFreq = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_cpufreq = this->_internal_cpufreq();
  uint32_t raw_cpufreq;
  memcpy(&raw_cpufreq, &tmp_cpufreq, sizeof(tmp_cpufreq));
  if (raw_cpufreq != 0) {
    total_size += 1 + 4;
  }

  // float cpuCoreNum = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_cpucorenum = this->_internal_cpucorenum();
  uint32_t raw_cpucorenum;
  memcpy(&raw_cpucorenum, &tmp_cpucorenum, sizeof(tmp_cpucorenum));
  if (raw_cpucorenum != 0) {
    total_size += 1 + 4;
  }

  // float memoryUsed = 5;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_memoryused = this->_internal_memoryused();
  uint32_t raw_memoryused;
  memcpy(&raw_memoryused, &tmp_memoryused, sizeof(tmp_memoryused));
  if (raw_memoryused != 0) {
    total_size += 1 + 4;
  }

  // float memoryTotal = 6;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_memorytotal = this->_internal_memorytotal();
  uint32_t raw_memorytotal;
  memcpy(&raw_memorytotal, &tmp_memorytotal, sizeof(tmp_memorytotal));
  if (raw_memorytotal != 0) {
    total_size += 1 + 4;
  }

  // float diskUsed = 7;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_diskused = this->_internal_diskused();
  uint32_t raw_diskused;
  memcpy(&raw_diskused, &tmp_diskused, sizeof(tmp_diskused));
  if (raw_diskused != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SystemInfo::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    SystemInfo::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SystemInfo::GetClassData() const { return &_class_data_; }


void SystemInfo::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<SystemInfo*>(&to_msg);
  auto& from = static_cast<const SystemInfo&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:datacapture.SystemInfo)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_cpuused = from._internal_cpuused();
  uint32_t raw_cpuused;
  memcpy(&raw_cpuused, &tmp_cpuused, sizeof(tmp_cpuused));
  if (raw_cpuused != 0) {
    _this->_internal_set_cpuused(from._internal_cpuused());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_cputemp = from._internal_cputemp();
  uint32_t raw_cputemp;
  memcpy(&raw_cputemp, &tmp_cputemp, sizeof(tmp_cputemp));
  if (raw_cputemp != 0) {
    _this->_internal_set_cputemp(from._internal_cputemp());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_cpufreq = from._internal_cpufreq();
  uint32_t raw_cpufreq;
  memcpy(&raw_cpufreq, &tmp_cpufreq, sizeof(tmp_cpufreq));
  if (raw_cpufreq != 0) {
    _this->_internal_set_cpufreq(from._internal_cpufreq());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_cpucorenum = from._internal_cpucorenum();
  uint32_t raw_cpucorenum;
  memcpy(&raw_cpucorenum, &tmp_cpucorenum, sizeof(tmp_cpucorenum));
  if (raw_cpucorenum != 0) {
    _this->_internal_set_cpucorenum(from._internal_cpucorenum());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_memoryused = from._internal_memoryused();
  uint32_t raw_memoryused;
  memcpy(&raw_memoryused, &tmp_memoryused, sizeof(tmp_memoryused));
  if (raw_memoryused != 0) {
    _this->_internal_set_memoryused(from._internal_memoryused());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_memorytotal = from._internal_memorytotal();
  uint32_t raw_memorytotal;
  memcpy(&raw_memorytotal, &tmp_memorytotal, sizeof(tmp_memorytotal));
  if (raw_memorytotal != 0) {
    _this->_internal_set_memorytotal(from._internal_memorytotal());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_diskused = from._internal_diskused();
  uint32_t raw_diskused;
  memcpy(&raw_diskused, &tmp_diskused, sizeof(tmp_diskused));
  if (raw_diskused != 0) {
    _this->_internal_set_diskused(from._internal_diskused());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SystemInfo::CopyFrom(const SystemInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:datacapture.SystemInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SystemInfo::IsInitialized() const {
  return true;
}

void SystemInfo::InternalSwap(SystemInfo* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(SystemInfo, _impl_.diskused_)
      + sizeof(SystemInfo::_impl_.diskused_)
      - PROTOBUF_FIELD_OFFSET(SystemInfo, _impl_.cpuused_)>(
          reinterpret_cast<char*>(&_impl_.cpuused_),
          reinterpret_cast<char*>(&other->_impl_.cpuused_));
}

::PROTOBUF_NAMESPACE_ID::Metadata SystemInfo::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_datacapture_2eCommunicate_2eproto_getter, &descriptor_table_datacapture_2eCommunicate_2eproto_once,
      file_level_metadata_datacapture_2eCommunicate_2eproto[16]);
}

// ===================================================================

class ClientInfo::_Internal {
 public:
};

ClientInfo::ClientInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:datacapture.ClientInfo)
}
ClientInfo::ClientInfo(const ClientInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  ClientInfo* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.ip_){}
    , decltype(_impl_.port_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.ip_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.ip_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_ip().empty()) {
    _this->_impl_.ip_.Set(from._internal_ip(), 
      _this->GetArenaForAllocation());
  }
  _this->_impl_.port_ = from._impl_.port_;
  // @@protoc_insertion_point(copy_constructor:datacapture.ClientInfo)
}

inline void ClientInfo::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.ip_){}
    , decltype(_impl_.port_){0u}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.ip_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.ip_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

ClientInfo::~ClientInfo() {
  // @@protoc_insertion_point(destructor:datacapture.ClientInfo)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void ClientInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.ip_.Destroy();
}

void ClientInfo::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void ClientInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:datacapture.ClientInfo)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.ip_.ClearToEmpty();
  _impl_.port_ = 0u;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ClientInfo::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string ip = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_ip();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "datacapture.ClientInfo.ip"));
        } else
          goto handle_unusual;
        continue;
      // uint32 port = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _impl_.port_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ClientInfo::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:datacapture.ClientInfo)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string ip = 1;
  if (!this->_internal_ip().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_ip().data(), static_cast<int>(this->_internal_ip().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "datacapture.ClientInfo.ip");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_ip(), target);
  }

  // uint32 port = 2;
  if (this->_internal_port() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(2, this->_internal_port(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:datacapture.ClientInfo)
  return target;
}

size_t ClientInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:datacapture.ClientInfo)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string ip = 1;
  if (!this->_internal_ip().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_ip());
  }

  // uint32 port = 2;
  if (this->_internal_port() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(this->_internal_port());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ClientInfo::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    ClientInfo::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ClientInfo::GetClassData() const { return &_class_data_; }


void ClientInfo::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<ClientInfo*>(&to_msg);
  auto& from = static_cast<const ClientInfo&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:datacapture.ClientInfo)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_ip().empty()) {
    _this->_internal_set_ip(from._internal_ip());
  }
  if (from._internal_port() != 0) {
    _this->_internal_set_port(from._internal_port());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ClientInfo::CopyFrom(const ClientInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:datacapture.ClientInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ClientInfo::IsInitialized() const {
  return true;
}

void ClientInfo::InternalSwap(ClientInfo* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.ip_, lhs_arena,
      &other->_impl_.ip_, rhs_arena
  );
  swap(_impl_.port_, other->_impl_.port_);
}

::PROTOBUF_NAMESPACE_ID::Metadata ClientInfo::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_datacapture_2eCommunicate_2eproto_getter, &descriptor_table_datacapture_2eCommunicate_2eproto_once,
      file_level_metadata_datacapture_2eCommunicate_2eproto[17]);
}

// ===================================================================

class NetConfig::_Internal {
 public:
};

NetConfig::NetConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:datacapture.NetConfig)
}
NetConfig::NetConfig(const NetConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  NetConfig* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.localip_){}
    , decltype(_impl_.mask_){}
    , decltype(_impl_.gateway_){}
    , decltype(_impl_.targetip_){}
    , decltype(_impl_.route_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.localip_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.localip_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_localip().empty()) {
    _this->_impl_.localip_.Set(from._internal_localip(), 
      _this->GetArenaForAllocation());
  }
  _impl_.mask_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.mask_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_mask().empty()) {
    _this->_impl_.mask_.Set(from._internal_mask(), 
      _this->GetArenaForAllocation());
  }
  _impl_.gateway_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.gateway_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_gateway().empty()) {
    _this->_impl_.gateway_.Set(from._internal_gateway(), 
      _this->GetArenaForAllocation());
  }
  _impl_.targetip_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.targetip_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_targetip().empty()) {
    _this->_impl_.targetip_.Set(from._internal_targetip(), 
      _this->GetArenaForAllocation());
  }
  _impl_.route_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.route_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_route().empty()) {
    _this->_impl_.route_.Set(from._internal_route(), 
      _this->GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:datacapture.NetConfig)
}

inline void NetConfig::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.localip_){}
    , decltype(_impl_.mask_){}
    , decltype(_impl_.gateway_){}
    , decltype(_impl_.targetip_){}
    , decltype(_impl_.route_){}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.localip_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.localip_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.mask_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.mask_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.gateway_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.gateway_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.targetip_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.targetip_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.route_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.route_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

NetConfig::~NetConfig() {
  // @@protoc_insertion_point(destructor:datacapture.NetConfig)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void NetConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.localip_.Destroy();
  _impl_.mask_.Destroy();
  _impl_.gateway_.Destroy();
  _impl_.targetip_.Destroy();
  _impl_.route_.Destroy();
}

void NetConfig::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void NetConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:datacapture.NetConfig)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.localip_.ClearToEmpty();
  _impl_.mask_.ClearToEmpty();
  _impl_.gateway_.ClearToEmpty();
  _impl_.targetip_.ClearToEmpty();
  _impl_.route_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* NetConfig::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string localIp = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_localip();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "datacapture.NetConfig.localIp"));
        } else
          goto handle_unusual;
        continue;
      // string mask = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_mask();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "datacapture.NetConfig.mask"));
        } else
          goto handle_unusual;
        continue;
      // string gateway = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_gateway();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "datacapture.NetConfig.gateway"));
        } else
          goto handle_unusual;
        continue;
      // string targetIp = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_targetip();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "datacapture.NetConfig.targetIp"));
        } else
          goto handle_unusual;
        continue;
      // string route = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          auto str = _internal_mutable_route();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "datacapture.NetConfig.route"));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* NetConfig::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:datacapture.NetConfig)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string localIp = 1;
  if (!this->_internal_localip().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_localip().data(), static_cast<int>(this->_internal_localip().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "datacapture.NetConfig.localIp");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_localip(), target);
  }

  // string mask = 2;
  if (!this->_internal_mask().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_mask().data(), static_cast<int>(this->_internal_mask().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "datacapture.NetConfig.mask");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_mask(), target);
  }

  // string gateway = 3;
  if (!this->_internal_gateway().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_gateway().data(), static_cast<int>(this->_internal_gateway().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "datacapture.NetConfig.gateway");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_gateway(), target);
  }

  // string targetIp = 4;
  if (!this->_internal_targetip().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_targetip().data(), static_cast<int>(this->_internal_targetip().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "datacapture.NetConfig.targetIp");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_targetip(), target);
  }

  // string route = 5;
  if (!this->_internal_route().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_route().data(), static_cast<int>(this->_internal_route().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "datacapture.NetConfig.route");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_route(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:datacapture.NetConfig)
  return target;
}

size_t NetConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:datacapture.NetConfig)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string localIp = 1;
  if (!this->_internal_localip().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_localip());
  }

  // string mask = 2;
  if (!this->_internal_mask().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_mask());
  }

  // string gateway = 3;
  if (!this->_internal_gateway().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_gateway());
  }

  // string targetIp = 4;
  if (!this->_internal_targetip().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_targetip());
  }

  // string route = 5;
  if (!this->_internal_route().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_route());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData NetConfig::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    NetConfig::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*NetConfig::GetClassData() const { return &_class_data_; }


void NetConfig::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<NetConfig*>(&to_msg);
  auto& from = static_cast<const NetConfig&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:datacapture.NetConfig)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_localip().empty()) {
    _this->_internal_set_localip(from._internal_localip());
  }
  if (!from._internal_mask().empty()) {
    _this->_internal_set_mask(from._internal_mask());
  }
  if (!from._internal_gateway().empty()) {
    _this->_internal_set_gateway(from._internal_gateway());
  }
  if (!from._internal_targetip().empty()) {
    _this->_internal_set_targetip(from._internal_targetip());
  }
  if (!from._internal_route().empty()) {
    _this->_internal_set_route(from._internal_route());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void NetConfig::CopyFrom(const NetConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:datacapture.NetConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool NetConfig::IsInitialized() const {
  return true;
}

void NetConfig::InternalSwap(NetConfig* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.localip_, lhs_arena,
      &other->_impl_.localip_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.mask_, lhs_arena,
      &other->_impl_.mask_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.gateway_, lhs_arena,
      &other->_impl_.gateway_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.targetip_, lhs_arena,
      &other->_impl_.targetip_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.route_, lhs_arena,
      &other->_impl_.route_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata NetConfig::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_datacapture_2eCommunicate_2eproto_getter, &descriptor_table_datacapture_2eCommunicate_2eproto_once,
      file_level_metadata_datacapture_2eCommunicate_2eproto[18]);
}

// ===================================================================

class storeInfo::_Internal {
 public:
};

storeInfo::storeInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:datacapture.storeInfo)
}
storeInfo::storeInfo(const storeInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  storeInfo* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.storepath_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.storepath_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.storepath_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_storepath().empty()) {
    _this->_impl_.storepath_.Set(from._internal_storepath(), 
      _this->GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:datacapture.storeInfo)
}

inline void storeInfo::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.storepath_){}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.storepath_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.storepath_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

storeInfo::~storeInfo() {
  // @@protoc_insertion_point(destructor:datacapture.storeInfo)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void storeInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.storepath_.Destroy();
}

void storeInfo::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void storeInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:datacapture.storeInfo)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.storepath_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* storeInfo::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string storePath = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_storepath();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "datacapture.storeInfo.storePath"));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* storeInfo::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:datacapture.storeInfo)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string storePath = 1;
  if (!this->_internal_storepath().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_storepath().data(), static_cast<int>(this->_internal_storepath().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "datacapture.storeInfo.storePath");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_storepath(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:datacapture.storeInfo)
  return target;
}

size_t storeInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:datacapture.storeInfo)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string storePath = 1;
  if (!this->_internal_storepath().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_storepath());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData storeInfo::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    storeInfo::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*storeInfo::GetClassData() const { return &_class_data_; }


void storeInfo::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<storeInfo*>(&to_msg);
  auto& from = static_cast<const storeInfo&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:datacapture.storeInfo)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_storepath().empty()) {
    _this->_internal_set_storepath(from._internal_storepath());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void storeInfo::CopyFrom(const storeInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:datacapture.storeInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool storeInfo::IsInitialized() const {
  return true;
}

void storeInfo::InternalSwap(storeInfo* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.storepath_, lhs_arena,
      &other->_impl_.storepath_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata storeInfo::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_datacapture_2eCommunicate_2eproto_getter, &descriptor_table_datacapture_2eCommunicate_2eproto_once,
      file_level_metadata_datacapture_2eCommunicate_2eproto[19]);
}

// ===================================================================

class SystemCmd::_Internal {
 public:
};

SystemCmd::SystemCmd(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:datacapture.SystemCmd)
}
SystemCmd::SystemCmd(const SystemCmd& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  SystemCmd* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.port_){}
    , decltype(_impl_.type_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.port_, &from._impl_.port_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.type_) -
    reinterpret_cast<char*>(&_impl_.port_)) + sizeof(_impl_.type_));
  // @@protoc_insertion_point(copy_constructor:datacapture.SystemCmd)
}

inline void SystemCmd::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.port_){0u}
    , decltype(_impl_.type_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

SystemCmd::~SystemCmd() {
  // @@protoc_insertion_point(destructor:datacapture.SystemCmd)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void SystemCmd::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void SystemCmd::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void SystemCmd::Clear() {
// @@protoc_insertion_point(message_clear_start:datacapture.SystemCmd)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&_impl_.port_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.type_) -
      reinterpret_cast<char*>(&_impl_.port_)) + sizeof(_impl_.type_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SystemCmd::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 port = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.port_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .datacapture.SystemCmd.statusType type = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_type(static_cast<::datacapture::SystemCmd_statusType>(val));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SystemCmd::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:datacapture.SystemCmd)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 port = 1;
  if (this->_internal_port() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(1, this->_internal_port(), target);
  }

  // .datacapture.SystemCmd.statusType type = 2;
  if (this->_internal_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteEnumToArray(
      2, this->_internal_type(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:datacapture.SystemCmd)
  return target;
}

size_t SystemCmd::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:datacapture.SystemCmd)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 port = 1;
  if (this->_internal_port() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(this->_internal_port());
  }

  // .datacapture.SystemCmd.statusType type = 2;
  if (this->_internal_type() != 0) {
    total_size += 1 +
      ::_pbi::WireFormatLite::EnumSize(this->_internal_type());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SystemCmd::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    SystemCmd::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SystemCmd::GetClassData() const { return &_class_data_; }


void SystemCmd::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<SystemCmd*>(&to_msg);
  auto& from = static_cast<const SystemCmd&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:datacapture.SystemCmd)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_port() != 0) {
    _this->_internal_set_port(from._internal_port());
  }
  if (from._internal_type() != 0) {
    _this->_internal_set_type(from._internal_type());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SystemCmd::CopyFrom(const SystemCmd& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:datacapture.SystemCmd)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SystemCmd::IsInitialized() const {
  return true;
}

void SystemCmd::InternalSwap(SystemCmd* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(SystemCmd, _impl_.type_)
      + sizeof(SystemCmd::_impl_.type_)
      - PROTOBUF_FIELD_OFFSET(SystemCmd, _impl_.port_)>(
          reinterpret_cast<char*>(&_impl_.port_),
          reinterpret_cast<char*>(&other->_impl_.port_));
}

::PROTOBUF_NAMESPACE_ID::Metadata SystemCmd::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_datacapture_2eCommunicate_2eproto_getter, &descriptor_table_datacapture_2eCommunicate_2eproto_once,
      file_level_metadata_datacapture_2eCommunicate_2eproto[20]);
}

// ===================================================================

class SystemLog::_Internal {
 public:
};

SystemLog::SystemLog(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:datacapture.SystemLog)
}
SystemLog::SystemLog(const SystemLog& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  SystemLog* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.log_){}
    , decltype(_impl_.status_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.log_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.log_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_log().empty()) {
    _this->_impl_.log_.Set(from._internal_log(), 
      _this->GetArenaForAllocation());
  }
  _this->_impl_.status_ = from._impl_.status_;
  // @@protoc_insertion_point(copy_constructor:datacapture.SystemLog)
}

inline void SystemLog::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.log_){}
    , decltype(_impl_.status_){false}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.log_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.log_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

SystemLog::~SystemLog() {
  // @@protoc_insertion_point(destructor:datacapture.SystemLog)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void SystemLog::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.log_.Destroy();
}

void SystemLog::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void SystemLog::Clear() {
// @@protoc_insertion_point(message_clear_start:datacapture.SystemLog)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.log_.ClearToEmpty();
  _impl_.status_ = false;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SystemLog::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bool status = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.status_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string log = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_log();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "datacapture.SystemLog.log"));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SystemLog::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:datacapture.SystemLog)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bool status = 1;
  if (this->_internal_status() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(1, this->_internal_status(), target);
  }

  // string log = 2;
  if (!this->_internal_log().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_log().data(), static_cast<int>(this->_internal_log().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "datacapture.SystemLog.log");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_log(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:datacapture.SystemLog)
  return target;
}

size_t SystemLog::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:datacapture.SystemLog)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string log = 2;
  if (!this->_internal_log().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_log());
  }

  // bool status = 1;
  if (this->_internal_status() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SystemLog::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    SystemLog::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SystemLog::GetClassData() const { return &_class_data_; }


void SystemLog::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<SystemLog*>(&to_msg);
  auto& from = static_cast<const SystemLog&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:datacapture.SystemLog)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_log().empty()) {
    _this->_internal_set_log(from._internal_log());
  }
  if (from._internal_status() != 0) {
    _this->_internal_set_status(from._internal_status());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SystemLog::CopyFrom(const SystemLog& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:datacapture.SystemLog)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SystemLog::IsInitialized() const {
  return true;
}

void SystemLog::InternalSwap(SystemLog* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.log_, lhs_arena,
      &other->_impl_.log_, rhs_arena
  );
  swap(_impl_.status_, other->_impl_.status_);
}

::PROTOBUF_NAMESPACE_ID::Metadata SystemLog::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_datacapture_2eCommunicate_2eproto_getter, &descriptor_table_datacapture_2eCommunicate_2eproto_once,
      file_level_metadata_datacapture_2eCommunicate_2eproto[21]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace datacapture
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::datacapture::Message*
Arena::CreateMaybeMessage< ::datacapture::Message >(Arena* arena) {
  return Arena::CreateMessageInternal< ::datacapture::Message >(arena);
}
template<> PROTOBUF_NOINLINE ::datacapture::RawData*
Arena::CreateMaybeMessage< ::datacapture::RawData >(Arena* arena) {
  return Arena::CreateMessageInternal< ::datacapture::RawData >(arena);
}
template<> PROTOBUF_NOINLINE ::datacapture::CapStatus*
Arena::CreateMaybeMessage< ::datacapture::CapStatus >(Arena* arena) {
  return Arena::CreateMessageInternal< ::datacapture::CapStatus >(arena);
}
template<> PROTOBUF_NOINLINE ::datacapture::Monitor*
Arena::CreateMaybeMessage< ::datacapture::Monitor >(Arena* arena) {
  return Arena::CreateMessageInternal< ::datacapture::Monitor >(arena);
}
template<> PROTOBUF_NOINLINE ::datacapture::DataQuery*
Arena::CreateMaybeMessage< ::datacapture::DataQuery >(Arena* arena) {
  return Arena::CreateMessageInternal< ::datacapture::DataQuery >(arena);
}
template<> PROTOBUF_NOINLINE ::datacapture::DataQueryRes*
Arena::CreateMaybeMessage< ::datacapture::DataQueryRes >(Arena* arena) {
  return Arena::CreateMessageInternal< ::datacapture::DataQueryRes >(arena);
}
template<> PROTOBUF_NOINLINE ::datacapture::DataQueryBatchesReq*
Arena::CreateMaybeMessage< ::datacapture::DataQueryBatchesReq >(Arena* arena) {
  return Arena::CreateMessageInternal< ::datacapture::DataQueryBatchesReq >(arena);
}
template<> PROTOBUF_NOINLINE ::datacapture::DataQueryBatchesRes*
Arena::CreateMaybeMessage< ::datacapture::DataQueryBatchesRes >(Arena* arena) {
  return Arena::CreateMessageInternal< ::datacapture::DataQueryBatchesRes >(arena);
}
template<> PROTOBUF_NOINLINE ::datacapture::CompressReqList*
Arena::CreateMaybeMessage< ::datacapture::CompressReqList >(Arena* arena) {
  return Arena::CreateMessageInternal< ::datacapture::CompressReqList >(arena);
}
template<> PROTOBUF_NOINLINE ::datacapture::CompressRes*
Arena::CreateMaybeMessage< ::datacapture::CompressRes >(Arena* arena) {
  return Arena::CreateMessageInternal< ::datacapture::CompressRes >(arena);
}
template<> PROTOBUF_NOINLINE ::datacapture::ConnectArgs*
Arena::CreateMaybeMessage< ::datacapture::ConnectArgs >(Arena* arena) {
  return Arena::CreateMessageInternal< ::datacapture::ConnectArgs >(arena);
}
template<> PROTOBUF_NOINLINE ::datacapture::ArgsList*
Arena::CreateMaybeMessage< ::datacapture::ArgsList >(Arena* arena) {
  return Arena::CreateMessageInternal< ::datacapture::ArgsList >(arena);
}
template<> PROTOBUF_NOINLINE ::datacapture::CommonCMD*
Arena::CreateMaybeMessage< ::datacapture::CommonCMD >(Arena* arena) {
  return Arena::CreateMessageInternal< ::datacapture::CommonCMD >(arena);
}
template<> PROTOBUF_NOINLINE ::datacapture::RoadInfo*
Arena::CreateMaybeMessage< ::datacapture::RoadInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::datacapture::RoadInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::datacapture::RoadInfoList*
Arena::CreateMaybeMessage< ::datacapture::RoadInfoList >(Arena* arena) {
  return Arena::CreateMessageInternal< ::datacapture::RoadInfoList >(arena);
}
template<> PROTOBUF_NOINLINE ::datacapture::SystemStatus*
Arena::CreateMaybeMessage< ::datacapture::SystemStatus >(Arena* arena) {
  return Arena::CreateMessageInternal< ::datacapture::SystemStatus >(arena);
}
template<> PROTOBUF_NOINLINE ::datacapture::SystemInfo*
Arena::CreateMaybeMessage< ::datacapture::SystemInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::datacapture::SystemInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::datacapture::ClientInfo*
Arena::CreateMaybeMessage< ::datacapture::ClientInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::datacapture::ClientInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::datacapture::NetConfig*
Arena::CreateMaybeMessage< ::datacapture::NetConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::datacapture::NetConfig >(arena);
}
template<> PROTOBUF_NOINLINE ::datacapture::storeInfo*
Arena::CreateMaybeMessage< ::datacapture::storeInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::datacapture::storeInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::datacapture::SystemCmd*
Arena::CreateMaybeMessage< ::datacapture::SystemCmd >(Arena* arena) {
  return Arena::CreateMessageInternal< ::datacapture::SystemCmd >(arena);
}
template<> PROTOBUF_NOINLINE ::datacapture::SystemLog*
Arena::CreateMaybeMessage< ::datacapture::SystemLog >(Arena* arena) {
  return Arena::CreateMessageInternal< ::datacapture::SystemLog >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
