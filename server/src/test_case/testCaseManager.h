#ifndef _TEST_CASE_MANAGER_
#define _TEST_CASE_MANAGER_
#include "moduleBase.h"

class CTestCaseManager : public CModuleBase
{
public:
    CTestCaseManager(CMediator *pMediator);
    ~CTestCaseManager();
    
    void Init() override;
    void Start() override;
    void Stop() override;
    void Pause() override;
    
    bool Msg<PERSON>ilter(u32 msgType);
    void HandleMsg(u32 msgLevel,u32 deviceId,u32 msgType,std::shared_ptr<void> spData);

};

#endif