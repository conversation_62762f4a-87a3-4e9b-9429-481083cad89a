#include "testCaseManager.h"


CTestCaseManager::CTestCaseManager(CMediator *pMediator) : CModuleBase(pMediator)
{
}
CTestCaseManager::~CTestCaseManager()
{
    
}

void CTestCaseManager::Init() override;
void CTestCaseManager::Start() override;
void CTestCaseManager::Stop() override;
void CTestCaseManager::Pause() override;

bool CTestCaseManager::Msg<PERSON><PERSON><PERSON>(u32 msgType);
void CTestCaseManager::HandleMsg(u32 msgLevel, u32 deviceId, u32 msgType, std::shared_ptr<void> spData);
