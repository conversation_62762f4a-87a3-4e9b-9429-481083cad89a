cmake_minimum_required(VERSION 3.0.2)

project(data_capture)
set(CMAKE_BUILD_TYPE Debug)  
# 获取处理器架构信息
message("Processor architecture: ${CMAKE_SYSTEM_PROCESSOR}")

# 根据不同的处理器架构进行处理
if(${CMAKE_SYSTEM_PROCESSOR} STREQUAL "x86_64")
    message("platfrom : x86_64")
    # 添加针对 x86_64 平台架构的处理
    set(target_dir x86)

elseif(${CMAKE_SYSTEM_PROCESSOR} STREQUAL "arm64" OR ${CMAKE_SYSTEM_PROCESSOR} STREQUAL "aarch64")
    message("platfrom : arm64 ")
    # 添加针对 arm64 平台架构的处理
    set(target_dir arm64)
else()
    message("unknown platfrom")
endif()



# ====== Options ======
option(FACTORY_WANJI "Select factory WanJi" OFF)        #Conflict between WanJi and ZXK
option(FACTORY_ZXK "Select factory ZXK" OFF)             #Conflict between <PERSON><PERSON><PERSON> and ZXK
option(FACTORY_SHANGHAIFENGXIAN "Select factory shanghai fengxian" ON)
option(FACTORY_SHANGHAIFENGXIAN_TCP "Select factory shanghai fengxian TCP" ON)



include(${PROJECT_SOURCE_DIR}/version.cmake)
set(LIBRARY_OUTPUT_DIRECTORY ${PROJECT_SOURCE_DIR}/lib/${target_dir})


link_directories(${PROJECT_SOURCE_DIR}/lib/${target_dir})
link_directories(${PROJECT_SOURCE_DIR}/lib//${target_dir}/thirdlib)

ADD_DEFINITIONS(-std=c++14)

if(NOT EXISTS ${PROJECT_SOURCE_DIR}/bin)
    file(MAKE_DIRECTORY ${PROJECT_SOURCE_DIR}/bin)
endif()

set(EXECUTABLE_OUTPUT_PATH ${PROJECT_SOURCE_DIR}/bin)

set(LIBRARY_OUTPUT_PATH ${PROJECT_SOURCE_DIR}/lib/${target_dir})

include_directories(${PROJECT_SOURCE_DIR}/include)
include_directories(${PROJECT_SOURCE_DIR}/include/zip)
include_directories(${PROJECT_SOURCE_DIR}/include/common)
include_directories(${PROJECT_SOURCE_DIR}/include/common/logger)
include_directories(${PROJECT_SOURCE_DIR}/include/cppkafka)
include_directories(${PROJECT_SOURCE_DIR}/include/rdkafka)
include_directories(${PROJECT_SOURCE_DIR}/include/json)
include_directories(${PROJECT_SOURCE_DIR}/include/mqtt_cpp)
include_directories(${PROJECT_SOURCE_DIR}/src)
include_directories(${PROJECT_SOURCE_DIR}/src/common)
include_directories(${PROJECT_SOURCE_DIR}/src/communication)
include_directories(${PROJECT_SOURCE_DIR}/src/data_capture)
include_directories(${PROJECT_SOURCE_DIR}/src/database)
include_directories(${PROJECT_SOURCE_DIR}/src/file_manager)
include_directories(${PROJECT_SOURCE_DIR}/src/utils)
include_directories(${PROJECT_SOURCE_DIR}/src/proto)
include_directories(${PROJECT_SOURCE_DIR}/src/system_status)
include_directories(${PROJECT_SOURCE_DIR}/src/decoder)

add_subdirectory(src)

add_dependencies(data_capture_server communication datacapture database filemanager systemstatus decoder)

install(TARGETS data_capture_server DESTINATION ${PROJECT_SOURCE_DIR}/package/${target_dir}/usr/local/data_capture_server/bin)
install(DIRECTORY ${LIBRARY_OUTPUT_PATH}/ DESTINATION ${PROJECT_SOURCE_DIR}/package/${target_dir}/usr/local/data_capture_server/lib)
install(DIRECTORY ${PROJECT_SOURCE_DIR}/conf DESTINATION ${PROJECT_SOURCE_DIR}/package/${target_dir}/usr/local/data_capture_server)
install(FILES ${PROJECT_SOURCE_DIR}/start.sh  DESTINATION ${PROJECT_SOURCE_DIR}/package/${target_dir}/usr/local/data_capture_server/bin)
